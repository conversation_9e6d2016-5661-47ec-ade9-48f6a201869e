﻿<template>
  <div class="appointment-list-container">
    <!-- 搜索区域 -->
    <art-search-bar v-model:filter="searchForm" :items="searchItems" @search="handleSearch" @reset="handleReset" />

    <!-- 列表内容 -->
    <el-card class="list-card art-custom-card">
      <template #header>
        <div class="card-header">
          <!-- PC端布局 -->
          <template v-if="!isMobile">
            <div class="left-section">
              <span class="title">客户列表</span>
              <el-tag v-if="total" type="info" effect="plain" class="data-count">
                {{ total }} 条数据
              </el-tag>
            </div>
            <div class="right-section">
              <!-- Excel导入导出按钮 -->
              <el-dropdown @command="handleExcelCommand" trigger="click" placement="bottom-end" class="excel-dropdown">
                <el-button :size="'default'" :type="'default'" :plain="true" v-ripple class="excel-button">
                  <el-icon :size="14">
                    <Document />
                  </el-icon>
                  <span style="margin-left: 4px;">Excel</span>
                  <el-icon class="el-icon--right" :size="12">
                    <ArrowDown />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu class="excel-dropdown-menu">
                    <el-dropdown-item command="import" class="excel-dropdown-item">
                      <div class="dropdown-item-content">
                        <el-icon class="dropdown-icon upload-icon">
                          <Upload />
                        </el-icon>
                        <span class="dropdown-title">导入Excel</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="export" class="excel-dropdown-item">
                      <div class="dropdown-item-content">
                        <el-icon class="dropdown-icon download-icon">
                          <Download />
                        </el-icon>
                        <span class="dropdown-title">导出Excel</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="template" class="excel-dropdown-item" divided>
                      <div class="dropdown-item-content">
                        <el-icon class="dropdown-icon template-icon">
                          <DocumentCopy />
                        </el-icon>
                        <span class="dropdown-title">下载模板</span>
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <el-button type="primary" @click="handleAddCustomer" :size="'default'" class="add-button">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>新增客户</span>
              </el-button>

              <!-- 刷新按钮 - 放置在右侧 -->
              <el-button class="refresh-button" :class="{ refreshing: isRefreshing }" @click="handleRefresh"
                :disabled="isRefreshing" circle v-ripple>
                <el-icon>
                  <Refresh />
                </el-icon>
              </el-button>
            </div>
          </template>

          <!-- 移动端布局 - 重新设计 -->
          <template v-if="isMobile">
            <div class="mobile-header">
              <div class="mobile-title-section">
                <h2 class="mobile-title">客户列表</h2>
                <div class="mobile-subtitle">管理您的客户信息</div>
              </div>
              <div class="mobile-stats-section">
                <div class="stats-card">
                  <div class="stats-number">{{ total || 0 }}</div>
                  <div class="stats-label">条数据</div>
                </div>
              </div>
            </div>
          </template>

          <!-- 隐藏的导入导出组件 -->
          <ArtExcelImport ref="excelImportRef" @import-success="handleImportSuccess" @import-error="handleImportError"
            style="display: none;" />

          <ArtExcelExport ref="excelExportRef" :data="exportData" filename="预约客户列表" sheetName="客户数据" type="success"
            :headers="exportHeaders" :auto-index="true" @export-success="handleExportSuccess"
            @export-error="handleExportError" style="display: none;" />
        </div>
      </template>

      <!-- 批量操作区域 -->
      <div class="batch-operation" :class="{ 'ios-batch-operation': isIOSDevice }" v-if="multipleSelection.length > 0">
        <el-alert type="info" :closable="false">
          <div class="batch-operation-content" :class="{ 'ios-batch-content': isIOSDevice }">
            <div class="batch-info">
              <el-icon>
                <InfoFilled />
              </el-icon>
              <span>已选择 {{ multipleSelection.length }} 项</span>
            </div>
            <div class="batch-actions" :class="{ 'ios-batch-actions': isIOSDevice }">
              <el-button type="info" :size="isIOSDevice ? 'small' : 'small'" @click="handleToggleAllSelection"
                class="batch-btn">
                {{ isAllSelected ? '取消全选' : '全选' }}
              </el-button>
              <el-button type="danger" :size="isIOSDevice ? 'small' : 'small'" @click="handleBatchDelete"
                class="batch-btn">
                <el-icon>
                  <Delete />
                </el-icon>批量删除
              </el-button>
            </div>
          </div>
        </el-alert>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <!-- 空状态 -->
        <div v-if="!loading && tableData.length === 0" class="empty-state">
          <div class="empty-illustration">
            <el-icon class="empty-icon">
              <Document />
            </el-icon>
          </div>
          <div class="empty-content">
            <h3 class="empty-title">暂无客户数据</h3>
            <p class="empty-description">
              {{ hasActiveFilters ? '当前筛选条件下没有找到匹配的客户' : '还没有客户信息，快来添加第一个客户吧！' }}
            </p>
            <div class="empty-actions">
              <el-button v-if="hasActiveFilters" type="primary" @click="clearFilters">
                <el-icon>
                  <Refresh />
                </el-icon>
                清除筛选
              </el-button>
              <el-button v-else type="primary" @click="handleAddCustomer">
                <el-icon>
                  <Plus />
                </el-icon>
                添加客户
              </el-button>
            </div>
          </div>
        </div>

        <!-- 骨架屏 -->
        <div v-if="loading && tableData.length === 0" class="table-skeleton">
          <div class="skeleton-header">
            <el-skeleton-item variant="rect" style="width: 100%; height: 40px;" />
          </div>
          <div class="skeleton-rows">
            <div v-for="i in (isMobile ? 5 : 8)" :key="i" class="skeleton-row">
              <el-skeleton-item variant="rect" style="width: 40px; height: 20px;" />
              <el-skeleton-item variant="text" style="width: 80px;" />
              <el-skeleton-item variant="text" style="width: 100px;" />
              <el-skeleton-item variant="text" style="width: 120px;" />
              <el-skeleton-item variant="rect" style="width: 60px; height: 24px;" />
              <el-skeleton-item variant="rect" style="width: 200px; height: 60px;" />
              <el-skeleton-item variant="circle" style="width: 32px; height: 32px;" />
              <el-skeleton-item variant="circle" style="width: 32px; height: 32px;" />
              <el-skeleton-item variant="circle" style="width: 32px; height: 32px;" />
            </div>
          </div>
        </div>

        <!-- 表格 -->
        <!-- PC端表格 -->
        <transition name="table-fade" appear v-if="!isMobile">
          <ArtTable v-if="!loading || tableData.length > 0" :data="tableData" :loading="false" :total="total"
            v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            :border="true" :stripe="true" :show-header-background="true" :size="'default'" :pagination-size="'default'"
            :pagination-layout="'total, sizes, prev, pager, next, jumper'" :header-cell-style="{
              background: 'var(--el-table-row-hover-bg-color)',
              color: 'var(--el-text-color-primary)',
              'text-align': 'center',
              'font-weight': '600'
            }" :cell-style="{ 'text-align': 'center' }" :virtual-scroll="total > 100" :item-height="70"
            :table-layout="'auto'" :width="'100%'" @row-click="handleRowClick" @selection-change="handleSelectionChange"
            ref="tableRef" class="animated-table full-width-table">
            <el-table-column type="selection" :width="isIOSDevice && isMobile ? 45 : 55"
              :class-name="isIOSDevice ? 'ios-selection-column' : ''" />
            <el-table-column prop="_id" label="客户ID" :min-width="100"
              :width="isMobile ? 100 : getColumnWidth('customerId', 120)" resizable
              @resize-change="(width) => handleColumnResize('customerId', width)">
              <template #default="scope">
                <span class="customer-id" @click.stop="copyCustomerId(scope.row._id)">{{
                  formatShortId(scope.row._id)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="客户姓名" :min-width="120" :width="isMobile ? 80 : undefined" resizable
              @resize-change="(width) => handleColumnResize('name', width)" />
            <el-table-column prop="phone" label="联系电话" :min-width="140"
              :width="isMobile ? 120 : getColumnWidth('phone', 160)" resizable
              @resize-change="(width) => handleColumnResize('phone', width)" />

            <el-table-column label="标签" :min-width="160" :width="isMobile ? 140 : undefined" resizable
              @resize-change="(width) => handleColumnResize('tags', width)">
              <template #default="scope">
                <div class="tag-list">
                  <el-tag v-for="tag in scope.row.tags" :key="tag" size="small" class="tag-item" effect="light">
                    {{ tag }}
                  </el-tag>
                  <span v-if="!scope.row.tags || scope.row.tags.length === 0">-</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="套餐配置" :min-width="280" :width="isMobile ? 260 : undefined" resizable
              @resize-change="(width) => handleColumnResize('packageConfig', width)">
              <template #default="scope">
                <div class="package-config">
                  <!-- 套餐信息 -->
                  <div class="config-item" v-if="scope.row.packageInfo?.name || scope.row.packageTypeName">
                    <div class="config-icon package-icon">
                      <el-icon>
                        <Camera />
                      </el-icon>
                    </div>
                    <div class="config-content">
                      <span class="config-label">套餐</span>
                      <span class="config-value">{{ scope.row.packageInfo?.name || scope.row.packageTypeName }}</span>
                    </div>
                  </div>

                  <!-- 随心换信息 -->
                  <div class="config-item" v-if="scope.row.changeOptionInfo?.name || scope.row.changeOptionName">
                    <div class="config-icon change-icon">
                      <el-icon>
                        <Refresh />
                      </el-icon>
                    </div>
                    <div class="config-content">
                      <span class="config-label">随心换</span>
                      <span class="config-value">{{ scope.row.changeOptionInfo?.name || scope.row.changeOptionName
                      }}</span>
                    </div>
                  </div>

                  <!-- 内存卡信息 -->
                  <div class="config-item" v-if="scope.row.memoryCardInfo?.size || scope.row.memoryCardName">
                    <div class="config-icon memory-icon">
                      <el-icon>
                        <Cpu />
                      </el-icon>
                    </div>
                    <div class="config-content">
                      <span class="config-label">内存卡</span>
                      <span class="config-value">{{ scope.row.memoryCardInfo?.size || scope.row.memoryCardName }}</span>
                    </div>
                  </div>

                  <!-- 无配置时显示 -->
                  <div class="no-config" v-if="!hasPackageConfig(scope.row)">
                    <el-icon class="no-config-icon">
                      <Warning />
                    </el-icon>
                    <span class="no-config-text">暂无配置</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" fixed="right" :min-width="isMobile ? 120 : 200"
              :width="isMobile ? 120 : getColumnWidth('actions', 220)" resizable
              @resize-change="(width) => handleColumnResize('actions', width)">
              <template #default="scope">
                <div class="operation-buttons" :class="{ 'mobile-buttons': isMobile, 'ios-buttons': isIOSDevice }">
                  <el-button type="primary" :size="isMobile ? 'small' : 'default'" circle
                    @click.stop="handleView(scope.row)" class="action-btn">
                    <el-icon>
                      <View />
                    </el-icon>
                  </el-button>
                  <el-button type="warning" :size="isMobile ? 'small' : 'default'" circle
                    @click.stop="handleEdit(scope.row)" class="action-btn">
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </el-button>
                  <el-button type="danger" :size="isMobile ? 'small' : 'default'" circle
                    @click.stop="handleDelete(scope.row)" class="action-btn">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </ArtTable>
        </transition>

        <!-- 移动端卡片式布局 -->
        <div v-if="isMobile" class="mobile-card-list">
          <!-- 加载状态 -->
          <div v-if="loading && tableData.length === 0" class="mobile-skeleton">
            <div v-for="i in 5" :key="i" class="mobile-skeleton-card">
              <el-skeleton animated>
                <template #template>
                  <div class="skeleton-header">
                    <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
                    <div class="skeleton-content">
                      <el-skeleton-item variant="text" style="width: 60%; height: 16px;" />
                      <el-skeleton-item variant="text" style="width: 40%; height: 14px;" />
                    </div>
                  </div>
                  <el-skeleton-item variant="rect" style="width: 100%; height: 60px; margin-top: 12px;" />
                </template>
              </el-skeleton>
            </div>
          </div>

          <!-- 客户卡片列表 -->
          <transition-group name="card-list" tag="div" class="card-container" v-if="!loading || tableData.length > 0">
            <div v-for="(item, index) in tableData" :key="item._id" class="customer-card" @click="handleRowClick(item)"
              :class="{ selected: multipleSelection.includes(item) }">
              <!-- 卡片头部 -->
              <div class="card-header-mobile">
                <div class="customer-info">
                  <div class="customer-avatar">
                    <span class="avatar-text">{{ item.name?.charAt(0) || '客' }}</span>
                  </div>
                  <div class="customer-details">
                    <div class="customer-name">{{ item.name || '未知客户' }}</div>
                    <div class="customer-id">ID: {{ formatShortId(item._id) }}</div>
                  </div>
                </div>
                <div class="card-actions">
                  <el-checkbox v-model="item.selected" @change="handleCardSelection(item)" @click.stop
                    class="card-checkbox" />
                </div>
              </div>

              <!-- 联系信息 -->
              <div class="contact-info">
                <div class="info-item">
                  <el-icon class="info-icon phone-icon">
                    <Phone />
                  </el-icon>
                  <span class="info-text">{{ item.phone || '暂无电话' }}</span>
                </div>
              </div>

              <!-- 标签 -->
              <div class="tags-section" v-if="item.tags && item.tags.length > 0">
                <div class="tag-list-mobile">
                  <el-tag v-for="tag in item.tags" :key="tag" size="small" class="tag-item-mobile" effect="light">
                    {{ tag }}
                  </el-tag>
                </div>
              </div>

              <!-- 套餐配置 -->
              <div class="package-section">
                <div class="section-title">套餐配置</div>
                <div class="package-config-mobile">
                  <!-- 套餐信息 -->
                  <div class="config-item-mobile" v-if="item.packageInfo?.name || item.packageTypeName">
                    <div class="config-icon-mobile package-icon">
                      <el-icon>
                        <Camera />
                      </el-icon>
                    </div>
                    <div class="config-content-mobile">
                      <span class="config-label-mobile">套餐</span>
                      <span class="config-value-mobile">{{ item.packageInfo?.name || item.packageTypeName }}</span>
                    </div>
                  </div>

                  <!-- 随心换信息 -->
                  <div class="config-item-mobile" v-if="item.changeOptionInfo?.name || item.changeOptionName">
                    <div class="config-icon-mobile change-icon">
                      <el-icon>
                        <Refresh />
                      </el-icon>
                    </div>
                    <div class="config-content-mobile">
                      <span class="config-label-mobile">随心换</span>
                      <span class="config-value-mobile">{{ item.changeOptionInfo?.name || item.changeOptionName
                      }}</span>
                    </div>
                  </div>

                  <!-- 内存卡信息 -->
                  <div class="config-item-mobile" v-if="item.memoryCardInfo?.size || item.memoryCardName">
                    <div class="config-icon-mobile memory-icon">
                      <el-icon>
                        <Cpu />
                      </el-icon>
                    </div>
                    <div class="config-content-mobile">
                      <span class="config-label-mobile">内存卡</span>
                      <span class="config-value-mobile">{{ item.memoryCardInfo?.size || item.memoryCardName }}</span>
                    </div>
                  </div>

                  <!-- 无配置时显示 -->
                  <div class="no-config-mobile" v-if="!hasPackageConfig(item)">
                    <el-icon class="no-config-icon-mobile">
                      <Warning />
                    </el-icon>
                    <span class="no-config-text-mobile">暂无配置</span>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="card-footer">
                <div class="action-buttons-mobile" :class="{ 'ios-mobile-buttons': isIOSDevice }">
                  <el-button size="small" type="primary" plain @click.stop="handleView(item)" class="mobile-action-btn">
                    <el-icon>
                      <View />
                    </el-icon>
                    <span>查看</span>
                  </el-button>
                  <el-button size="small" type="success" plain @click.stop="handleEdit(item)" class="mobile-action-btn">
                    <el-icon>
                      <Edit />
                    </el-icon>
                    <span>编辑</span>
                  </el-button>
                  <el-button size="small" type="danger" plain @click.stop="handleDelete(item)"
                    class="mobile-action-btn">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    <span>删除</span>
                  </el-button>
                </div>
              </div>
            </div>
          </transition-group>
        </div>
      </div>


    </el-card>

    <!-- 新增/编辑客户对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增客户' : '编辑客户'"
      :width="isMobile ? '90%' : '700px'" destroy-on-close :close-on-click-modal="false">
      <!-- 步骤条 -->
      <el-steps v-if="dialogType === 'add'" :active="activeStep" finish-status="success" simple
        style="margin-bottom: 20px">
        <el-step title="客户信息" />
        <el-step title="预约信息" />
      </el-steps>

      <div v-loading="formLoading" element-loading-text="正在加载数据" element-loading-background="rgba(255, 255, 255, 0.8)"
        :class="{ 'mobile-form-container': isMobile }">
        <!-- 动画容器 -->
        <div class="steps-container">
          <!-- 第一步：客户基本信息 -->
          <div v-show="activeStep === 0 || dialogType === 'edit'" :class="['step-content', { 'animate__animated': dialogType === 'add' },
            { 'animate__fadeInLeft': activeStep === 0 && animationDirection === 'backward' },
            { 'animate__fadeOutLeft': activeStep === 1 && animationDirection === 'forward' }]">

            <!-- 编辑模式下使用标签页 -->
            <el-tabs v-if="dialogType === 'edit'" v-model="activeTab" type="card" class="edit-tabs">
              <el-tab-pane label="客户信息" name="basic">
                <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" label-position="right">
                  <div class="form-row">
                    <el-form-item label="客户姓名" prop="name">
                      <el-input v-model="formData.name" placeholder="请输入客户姓名" />
                    </el-form-item>
                    <el-form-item label="联系电话" prop="phone">
                      <el-input v-model="formData.phone" placeholder="请输入联系电话" maxlength="11" show-word-limit />
                    </el-form-item>
                  </div>
                  <div class="form-row">
                    <el-form-item label="电子邮箱" prop="email">
                      <el-input v-model="formData.email" placeholder="请输入电子邮箱" />
                    </el-form-item>
                    <el-form-item label="客户标签" prop="tags">
                      <el-select v-model="formData.tags" multiple filterable allow-create placeholder="请选择或创建标签"
                        style="width: 100%">
                        <el-option label="VIP客户" value="VIP" />
                        <el-option label="新客户" value="新客户" />
                        <el-option label="老客户" value="老客户" />
                        <el-option label="商业客户" value="商业客户" />
                      </el-select>
                    </el-form-item>
                  </div>
                  <el-form-item label="所在地区" prop="region">
                    <el-cascader v-model="formData.region" :options="regionOptions"
                      :props="{ expandTrigger: 'click', checkStrictly: false, emitPath: true }" placeholder="请选择所在地区"
                      style="width: 100%" @change="handleRegionChange" />
                  </el-form-item>
                  <el-form-item label="详细地址" prop="addressDetail">
                    <el-input v-model="formData.addressDetail" placeholder="请输入详细地址"
                      @input="val => { formData.addressDetail = val; updateFullAddress(); }" />
                  </el-form-item>
                  <el-form-item label="备注" prop="remark">
                    <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" :rows="2" resize="none" />
                  </el-form-item>
                </el-form>
              </el-tab-pane>

              <el-tab-pane label="预约信息" name="appointment">
                <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" label-position="right">
                  <el-form-item label="型号" prop="packageType">
                    <el-select v-model="formData.packageType" placeholder="请选择型号" style="width: 100%"
                      @change="handleEditPackageChange">
                      <el-option v-for="item in packageOptions" :key="item._id" :label="item.name" :value="item._id">
                        <div class="package-option">
                          <span>{{ item.name }}</span>
                          <span class="package-price">¥{{ item.price }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="随心换" prop="changeOption">
                    <el-select v-model="formData.changeOption" placeholder="请选择随心换" style="width: 100%"
                      @change="handleEditChangeOptionChange">
                      <el-option v-for="item in changeOptions" :key="item._id" :label="item.name" :value="item._id">
                        <div class="package-option">
                          <span>{{ item.name }}</span>
                          <span class="package-price">¥{{ item.price }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="内存卡" prop="memoryCard">
                    <el-select v-model="formData.memoryCard" placeholder="请选择内存卡" style="width: 100%"
                      @change="handleEditMemoryCardChange">
                      <el-option v-for="item in memoryCards" :key="item._id" :label="item.size" :value="item._id">
                        <div class="package-option">
                          <span>{{ item.size }}</span>
                          <span class="package-price">¥{{ item.price }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="预约时间" prop="appointmentTime" :class="{ 'mobile-date-item': isMobile }">
                    <el-date-picker v-model="formData.appointmentTime" type="datetime" placeholder="选择预约时间"
                      style="width: 100%" value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss"
                      :default-time="new Date(2000, 1, 1, 0, 0, 0)" :teleported="!isMobile"
                      :popper-class="isMobile ? 'mobile-date-picker' : 'pc-date-picker'"
                      @focus="handleDatePickerFocus" />
                  </el-form-item>

                  <el-form-item label="预约备注" prop="appointmentRemark">
                    <el-input v-model="formData.appointmentRemark" type="textarea" placeholder="请输入预约备注" :rows="2"
                      resize="none" />
                  </el-form-item>

                  <el-form-item label="预约状态" prop="status">
                    <el-select v-model="formData.status" placeholder="请选择预约状态" style="width: 100%">
                      <el-option label="待确认" value="pending" />
                      <el-option label="已确认" value="confirmed" />
                      <el-option label="已完成" value="completed" />
                      <el-option label="已取消" value="cancelled" />
                    </el-select>
                  </el-form-item>


                </el-form>
              </el-tab-pane>
            </el-tabs>

            <!-- 新增模式下不使用标签页 -->
            <el-form v-else ref="formRef" :model="formData" :rules="formRules" label-width="120px"
              label-position="right">
              <div class="form-row">
                <el-form-item label="客户姓名" prop="name">
                  <el-input v-model="formData.name" placeholder="请输入客户姓名" />
                </el-form-item>
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="formData.phone" placeholder="请输入联系电话" maxlength="11" show-word-limit />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="电子邮箱" prop="email">
                  <el-input v-model="formData.email" placeholder="请输入电子邮箱" />
                </el-form-item>
                <el-form-item label="客户标签" prop="tags">
                  <el-select v-model="formData.tags" multiple filterable allow-create placeholder="请选择或创建标签"
                    style="width: 100%">
                    <el-option label="VIP客户" value="VIP" />
                    <el-option label="新客户" value="新客户" />
                    <el-option label="老客户" value="老客户" />
                    <el-option label="商业客户" value="商业客户" />
                  </el-select>
                </el-form-item>
              </div>
              <el-form-item label="所在地区" prop="region">
                <el-cascader v-model="formData.region" :options="regionOptions"
                  :props="{ expandTrigger: 'click', checkStrictly: false, emitPath: true }" placeholder="请选择所在地区"
                  style="width: 100%" @change="handleRegionChange" />
              </el-form-item>
              <el-form-item label="详细地址" prop="addressDetail">
                <el-input v-model="formData.addressDetail" placeholder="请输入详细地址"
                  @input="val => { formData.addressDetail = val; updateFullAddress(); }" />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" :rows="2" resize="none" />
              </el-form-item>
            </el-form>
          </div>

          <!-- 第二步：预约信息 -->
          <div v-show="activeStep === 1 && dialogType === 'add'" :class="['step-content step-content-scrollable', { 'animate__animated': dialogType === 'add' },
            { 'animate__fadeInRight': activeStep === 1 && animationDirection === 'forward' },
            { 'animate__fadeOutRight': activeStep === 0 && animationDirection === 'backward' }]">
            <el-form ref="appointmentFormRef" :model="appointmentData" :rules="appointmentRules" label-width="120px"
              label-position="right">



              <!-- 套餐选择 -->
              <el-form-item label="型号" prop="packageType">
                <el-select v-model="appointmentData.packageType" placeholder="请选择型号" style="width: 100%"
                  @change="handlePackageChange">
                  <el-option v-for="item in packageOptions" :key="item._id" :label="item.name" :value="item._id">
                    <div class="package-option">
                      <span>{{ item.name }}</span>
                      <span class="package-price">¥{{ item.price }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <!-- 随心换选项 -->
              <el-form-item label="随心换" prop="changeOption">
                <el-select v-model="appointmentData.changeOption" placeholder="请选择随心换" style="width: 100%"
                  @change="handleChangeOptionChange">
                  <el-option v-for="item in changeOptions" :key="item._id" :label="item.name" :value="item._id">
                    <div class="package-option">
                      <span>{{ item.name }}</span>
                      <span class="package-price">¥{{ item.price }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <!-- 内存卡选择 -->
              <el-form-item label="内存卡" prop="memoryCard">
                <el-select v-model="appointmentData.memoryCard" placeholder="请选择内存卡" style="width: 100%"
                  @change="handleMemoryCardChange">
                  <el-option v-for="item in memoryCards" :key="item._id" :label="item.size" :value="item._id">
                    <div class="package-option">
                      <span>{{ item.size }}</span>
                      <span class="package-price">¥{{ item.price }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <!-- 价格和时间 -->
              <el-form-item label="预约时间" prop="appointmentTime" :class="{ 'mobile-date-item': isMobile }">
                <el-date-picker v-model="appointmentData.appointmentTime" type="datetime" placeholder="选择预约时间"
                  style="width: 100%" value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss"
                  :default-time="new Date(2000, 1, 1, 0, 0, 0)" @change="handleDateTimeChange" :teleported="!isMobile"
                  :popper-class="isMobile ? 'mobile-date-picker' : 'pc-date-picker'" @focus="handleDatePickerFocus" />
              </el-form-item>

              <!-- 预约状态 -->
              <el-form-item label="预约状态" prop="status">
                <el-select v-model="appointmentData.status" placeholder="请选择预约状态" style="width: 100%">
                  <el-option label="待确认" value="pending">
                    <div class="status-option">
                      <el-icon color="#E6A23C">
                        <Clock />
                      </el-icon>
                      <span>待确认</span>
                    </div>
                  </el-option>
                  <el-option label="已确认" value="confirmed">
                    <div class="status-option">
                      <el-icon color="#67C23A">
                        <Check />
                      </el-icon>
                      <span>已确认</span>
                    </div>
                  </el-option>
                  <el-option label="已完成" value="completed">
                    <div class="status-option">
                      <el-icon color="#409EFF">
                        <CircleCheck />
                      </el-icon>
                      <span>已完成</span>
                    </div>
                  </el-option>
                  <el-option label="已取消" value="cancelled">
                    <div class="status-option">
                      <el-icon color="#F56C6C">
                        <CircleClose />
                      </el-icon>
                      <span>已取消</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <!-- 备注 -->
              <el-form-item label="预约备注" prop="remark">
                <el-input v-model="appointmentData.remark" type="textarea" placeholder="请输入预约备注" :rows="3" />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <!-- 返回按钮，仅在第二步显示 -->
          <el-button v-if="activeStep === 1 && dialogType === 'add'" @click="goToPreviousStep">上一步</el-button>

          <!-- 取消按钮 -->
          <el-button @click="dialogVisible = false">取消</el-button>

          <!-- 下一步按钮，仅在第一步显示 -->
          <el-button v-if="activeStep === 0 && dialogType === 'add'" type="primary" @click="goToNextStep"
            :loading="nextStepLoading">下一步</el-button>

          <!-- 提交按钮，使用计算属性判断是否显示 -->
          <el-button v-if="showSubmitButton" type="primary" @click="showConfirmSubmit"
            :loading="submitLoading">提交</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 提交确认对话框 -->
    <el-dialog v-model="confirmDialogVisible" title="确认提交" width="500px" append-to-body center>
      <div class="confirm-content">
        <div class="confirm-header">
          <el-icon class="confirm-icon" color="#E6A23C">
            <Warning />
          </el-icon>
          <p class="confirm-title">{{ dialogType === 'edit' ? '确认更新客户信息' : '确认提交预约信息' }}</p>
        </div>

        <!-- 已选择项目预览 -->
        <div class="confirm-selection-preview" v-if="hasSelections || hasEditSelections">
          <div class="confirm-preview-title">{{ dialogType === 'edit' ? '预约信息' : '已选择项目' }}</div>
          <div class="confirm-preview-items">
            <!-- 新增模式的预览 -->
            <template v-if="dialogType === 'add'">
              <div class="confirm-preview-item" v-if="selectedPackageInfo">
                <div class="confirm-item-tag">
                  <el-tag size="small" type="primary" effect="light">型号</el-tag>
                </div>
                <div class="confirm-item-content">
                  <span class="confirm-item-name">{{ selectedPackageInfo.name }}</span>
                  <span class="confirm-item-price">¥{{ selectedPackageInfo.price }}</span>
                </div>
              </div>
              <div class="confirm-preview-item" v-if="selectedChangeOptionInfo">
                <div class="confirm-item-tag">
                  <el-tag size="small" type="success" effect="light">随心换</el-tag>
                </div>
                <div class="confirm-item-content">
                  <span class="confirm-item-name">{{ selectedChangeOptionInfo.name }}</span>
                  <span class="confirm-item-price">¥{{ selectedChangeOptionInfo.price }}</span>
                </div>
              </div>
              <div class="confirm-preview-item" v-if="selectedMemoryCardInfo">
                <div class="confirm-item-tag">
                  <el-tag size="small" type="warning" effect="light">内存卡</el-tag>
                </div>
                <div class="confirm-item-content">
                  <span class="confirm-item-name">{{ selectedMemoryCardInfo.size }}</span>
                  <span class="confirm-item-price">¥{{ selectedMemoryCardInfo.price }}</span>
                </div>
              </div>
              <div class="confirm-total">
                <span class="confirm-total-label">总价</span>
                <span class="confirm-total-price">¥{{ appointmentData.totalPrice || 0 }}</span>
              </div>
            </template>

            <!-- 编辑模式的预览 -->
            <template v-if="dialogType === 'edit'">
              <div class="confirm-preview-item" v-if="formData.packageInfo">
                <div class="confirm-item-tag">
                  <el-tag size="small" type="primary" effect="light">型号</el-tag>
                </div>
                <div class="confirm-item-content">
                  <span class="confirm-item-name">{{ formData.packageInfo.name }}</span>
                  <span class="confirm-item-price">¥{{ formData.packageInfo.price }}</span>
                </div>
              </div>
              <div class="confirm-preview-item" v-if="formData.changeOptionInfo">
                <div class="confirm-item-tag">
                  <el-tag size="small" type="success" effect="light">随心换</el-tag>
                </div>
                <div class="confirm-item-content">
                  <span class="confirm-item-name">{{ formData.changeOptionInfo.name }}</span>
                  <span class="confirm-item-price">¥{{ formData.changeOptionInfo.price }}</span>
                </div>
              </div>
              <div class="confirm-preview-item" v-if="formData.memoryCardInfo">
                <div class="confirm-item-tag">
                  <el-tag size="small" type="warning" effect="light">内存卡</el-tag>
                </div>
                <div class="confirm-item-content">
                  <span class="confirm-item-name">{{ formData.memoryCardInfo.size }}</span>
                  <span class="confirm-item-price">¥{{ formData.memoryCardInfo.price }}</span>
                </div>
              </div>
              <div class="confirm-total">
                <span class="confirm-total-label">总价</span>
                <span class="confirm-total-price">¥{{ calculateTotalPrice() }}</span>
              </div>
            </template>
          </div>
        </div>

        <div class="confirm-message">
          <p>请确认以上信息无误后提交</p>
        </div>
      </div>
      <template #footer>
        <div class="confirm-footer">
          <el-button @click="confirmDialogVisible = false" size="large">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading" size="large">确认提交</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 客户详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="客户详情" :width="isMobile ? '95%' : '1000px'" destroy-on-close
      class="customer-detail-dialog" :class="{ 'mobile-detail-dialog': isMobile }">
      <div v-if="selectedCustomer" class="customer-detail">
        <!-- PC端保持原有布局 -->
        <template v-if="!isMobile">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="客户ID">
              <span class="customer-id" @click.stop="copyCustomerId(selectedCustomer._id)">{{
                formatShortId(selectedCustomer._id)
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="客户姓名">{{ selectedCustomer.name }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              selectedCustomer.phone || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱">{{
              selectedCustomer.email || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="客户地址" :span="2">{{
              selectedCustomer.address || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="标签">
              <div class="tag-list detail-tag-list">
                <el-tag v-for="tag in selectedCustomer.tags" :key="tag" size="small" class="tag-item" effect="light">
                  {{ tag }}
                </el-tag>
                <span v-if="!selectedCustomer.tags || selectedCustomer.tags.length === 0">-</span>
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="备注" :span="2">{{
              selectedCustomer.remark || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="预约状态">
              <el-tag :type="getStatusType(selectedCustomer.status)">
                {{ getStatusText(selectedCustomer.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="预约时间">{{
              formatDateTime(selectedCustomer.appointmentTime)
            }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{
              formatDateTime(selectedCustomer.createdAt)
            }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{
              formatDateTime(selectedCustomer.updatedAt)
            }}</el-descriptions-item>
          </el-descriptions>
        </template>

        <!-- 移动端水平滑动卡片布局 -->
        <template v-if="isMobile">
          <div class="mobile-swiper-container">
            <!-- 滑动指示器 -->
            <div class="swiper-indicators">
              <div v-for="(card, index) in mobileCards" :key="index" class="indicator-dot"
                :class="{ active: currentCardIndex === index }" @click="scrollToCard(index)"></div>
            </div>

            <!-- 水平滑动容器 -->
            <div class="mobile-cards-wrapper" ref="cardsWrapper" @scroll="handleCardScroll">
              <!-- 客户基本信息卡片 -->
              <div class="mobile-card basic-info-card" data-card-index="0">
                <div class="card-header-mobile">
                  <div class="customer-avatar-large">
                    <span class="avatar-text-large">{{ selectedCustomer.name?.charAt(0) || '客' }}</span>
                  </div>
                  <div class="customer-main-info">
                    <h3 class="customer-name-large">{{ selectedCustomer.name || '未知客户' }}</h3>
                    <div class="customer-id-large" @click.stop="copyCustomerId(selectedCustomer._id)">
                      ID: {{ formatShortId(selectedCustomer._id) }}
                    </div>
                  </div>
                </div>
                <div class="card-content-mobile">
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-icon">📞</div>
                      <div class="info-text">
                        <div class="info-label">电话</div>
                        <div class="info-value">{{ selectedCustomer.phone || '暂无' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <div class="info-icon">📧</div>
                      <div class="info-text">
                        <div class="info-label">邮箱</div>
                        <div class="info-value">{{ selectedCustomer.email || '暂无' }}</div>
                      </div>
                    </div>
                    <div class="info-item">
                      <div class="info-icon">📍</div>
                      <div class="info-text">
                        <div class="info-label">地址</div>
                        <div class="info-value">{{ selectedCustomer.address || '暂无' }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 状态信息卡片 -->
              <div class="mobile-card status-card" data-card-index="1">
                <div class="card-title-mobile">
                  <el-icon class="title-icon">
                    <InfoFilled />
                  </el-icon>
                  <span>状态信息</span>
                </div>
                <div class="card-content-mobile">
                  <div class="status-grid">
                    <div class="status-item">
                      <div class="status-label">预约状态</div>
                      <el-tag :type="getStatusType(selectedCustomer.status)" size="small" class="status-tag">
                        {{ getStatusText(selectedCustomer.status) }}
                      </el-tag>
                    </div>
                    <div class="status-item">
                      <div class="status-label">预约时间</div>
                      <div class="status-value">{{ formatDateTime(selectedCustomer.appointmentTime) }}</div>
                    </div>
                    <div class="status-item">
                      <div class="status-label">创建时间</div>
                      <div class="status-value">{{ formatDateTime(selectedCustomer.createdAt) }}</div>
                    </div>
                    <div class="status-item">
                      <div class="status-label">更新时间</div>
                      <div class="status-value">{{ formatDateTime(selectedCustomer.updatedAt) }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 标签和备注卡片 -->
              <div class="mobile-card tags-remark-card" data-card-index="2">
                <div class="card-title-mobile">
                  <el-icon class="title-icon">
                    <Collection />
                  </el-icon>
                  <span>标签备注</span>
                </div>
                <div class="card-content-mobile">
                  <!-- 标签区域 -->
                  <div class="tags-section" v-if="selectedCustomer.tags && selectedCustomer.tags.length > 0">
                    <div class="section-label">客户标签</div>
                    <div class="mobile-tags">
                      <el-tag v-for="tag in selectedCustomer.tags" :key="tag" size="small" effect="light"
                        class="mobile-tag">
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>

                  <!-- 备注区域 -->
                  <div class="remark-section" v-if="selectedCustomer.remark">
                    <div class="section-label">客户备注</div>
                    <div class="remark-text">{{ selectedCustomer.remark }}</div>
                  </div>

                  <!-- 如果没有标签和备注 -->
                  <div class="empty-state"
                    v-if="(!selectedCustomer.tags || selectedCustomer.tags.length === 0) && !selectedCustomer.remark">
                    <div class="empty-icon">📝</div>
                    <div class="empty-text">暂无标签和备注信息</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- PC端预约项目布局 -->
        <div class="latest-appointment" v-if="!isMobile">
          <div class="appointment-layout">
            <!-- 左侧：预约项目 -->
            <div class="appointment-left">
              <h3>预约项目</h3>
              <div class="appointment-items">
                <div class="appointment-item" v-if="selectedCustomer.packageInfo || selectedCustomer.packageType">
                  <div class="item-tag">
                    <el-tag size="small" type="primary" effect="light">型号</el-tag>
                  </div>
                  <div class="item-content">
                    <div class="item-details">
                      <span class="item-name">{{
                        selectedCustomer.packageInfo?.name ||
                        selectedCustomer.packageType
                      }}</span>
                      <span class="item-price">¥{{ selectedCustomer.packageInfo?.price || 0 }}</span>
                    </div>
                  </div>
                </div>

                <div class="appointment-item" v-if="selectedCustomer.changeOptionInfo || selectedCustomer.changeOption">
                  <div class="item-tag">
                    <el-tag size="small" type="success" effect="light">随心换</el-tag>
                  </div>
                  <div class="item-content">
                    <div class="item-details">
                      <span class="item-name">{{
                        selectedCustomer.changeOptionInfo?.name ||
                        selectedCustomer.changeOption
                      }}</span>
                      <span class="item-price">¥{{ selectedCustomer.changeOptionInfo?.price || 0 }}</span>
                    </div>
                  </div>
                </div>

                <div class="appointment-item" v-if="selectedCustomer.memoryCardInfo || selectedCustomer.memoryCard">
                  <div class="item-tag">
                    <el-tag size="small" type="warning" effect="light">内存卡</el-tag>
                  </div>
                  <div class="item-content">
                    <div class="item-details">
                      <span class="item-name">{{
                        selectedCustomer.memoryCardInfo?.size ||
                        selectedCustomer.memoryCard
                      }}</span>
                      <span class="item-price">¥{{ selectedCustomer.memoryCardInfo?.price || 0 }}</span>
                    </div>
                  </div>
                </div>

                <div class="appointment-total">
                  <span>总价：</span>
                  <span class="total-price">¥{{ calculateCustomerTotalPrice(selectedCustomer) }}</span>
                </div>
              </div>
            </div>

            <!-- 右侧：预约备注 -->
            <div class="appointment-right">
              <h3>预约备注</h3>
              <div class="appointment-remark">
                <div class="remark-content">
                  {{ selectedCustomer.appointmentRemark || '暂无备注' }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 移动端预约项目卡片 -->
        <template v-if="isMobile">
          <!-- 预约项目卡片 -->
          <div class="detail-card appointment-card"
            v-if="selectedCustomer.packageInfo || selectedCustomer.changeOptionInfo || selectedCustomer.memoryCardInfo">
            <div class="card-title">
              <el-icon class="title-icon">
                <Camera />
              </el-icon>
              <span>预约项目</span>
            </div>
            <div class="card-content">
              <!-- 套餐项目 -->
              <div class="appointment-item-mobile" v-if="selectedCustomer.packageInfo || selectedCustomer.packageType">
                <div class="item-header">
                  <el-tag size="small" type="primary" effect="light">套餐</el-tag>
                  <span class="item-price">¥{{ selectedCustomer.packageInfo?.price || 0 }}</span>
                </div>
                <div class="item-name">{{ selectedCustomer.packageInfo?.name || selectedCustomer.packageType }}</div>
              </div>

              <!-- 随心换项目 -->
              <div class="appointment-item-mobile"
                v-if="selectedCustomer.changeOptionInfo || selectedCustomer.changeOption">
                <div class="item-header">
                  <el-tag size="small" type="success" effect="light">随心换</el-tag>
                  <span class="item-price">¥{{ selectedCustomer.changeOptionInfo?.price || 0 }}</span>
                </div>
                <div class="item-name">{{ selectedCustomer.changeOptionInfo?.name || selectedCustomer.changeOption }}
                </div>
              </div>

              <!-- 内存卡项目 -->
              <div class="appointment-item-mobile"
                v-if="selectedCustomer.memoryCardInfo || selectedCustomer.memoryCard">
                <div class="item-header">
                  <el-tag size="small" type="warning" effect="light">内存卡</el-tag>
                  <span class="item-price">¥{{ selectedCustomer.memoryCardInfo?.price || 0 }}</span>
                </div>
                <div class="item-name">{{ selectedCustomer.memoryCardInfo?.size || selectedCustomer.memoryCard }}</div>
              </div>

              <!-- 总价 -->
              <div class="total-price-mobile">
                <span class="total-label">总价</span>
                <span class="total-amount">¥{{ calculateCustomerTotalPrice(selectedCustomer) }}</span>
              </div>
            </div>
          </div>

          <!-- 预约备注卡片 -->
          <div class="detail-card appointment-remark-card" v-if="selectedCustomer.appointmentRemark">
            <div class="card-title">
              <el-icon class="title-icon">
                <Edit />
              </el-icon>
              <span>预约备注</span>
            </div>
            <div class="card-content">
              <div class="remark-text">{{ selectedCustomer.appointmentRemark }}</div>
            </div>
          </div>
        </template>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 移动端底部操作栏 -->
    <div v-if="isMobile" class="mobile-action-bar">
      <div class="action-bar-container">
        <!-- Excel操作按钮 -->
        <el-dropdown @command="handleExcelCommand" trigger="click" placement="top" class="mobile-excel-dropdown">
          <div class="action-item">
            <div class="action-icon excel-icon">
              <el-icon :size="20">
                <Document />
              </el-icon>
            </div>
            <span class="action-label">Excel</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="mobile-excel-menu">
              <el-dropdown-item command="import" class="mobile-excel-item">
                <el-icon class="menu-icon">
                  <Upload />
                </el-icon>
                <span>导入数据</span>
              </el-dropdown-item>
              <el-dropdown-item command="export" class="mobile-excel-item">
                <el-icon class="menu-icon">
                  <Download />
                </el-icon>
                <span>导出数据</span>
              </el-dropdown-item>
              <el-dropdown-item command="template" class="mobile-excel-item" divided>
                <el-icon class="menu-icon">
                  <DocumentCopy />
                </el-icon>
                <span>下载模板</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 新增客户按钮 -->
        <div class="action-item primary-action" @click="handleAddCustomer">
          <div class="action-icon add-icon">
            <el-icon :size="24">
              <Plus />
            </el-icon>
          </div>
          <span class="action-label">新增</span>
        </div>

        <!-- 刷新按钮 -->
        <div class="action-item" @click="handleRefresh">
          <div class="action-icon refresh-icon">
            <el-icon :size="20">
              <Refresh />
            </el-icon>
          </div>
          <span class="action-label">刷新</span>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { Plus, Refresh, View, Edit, Calendar, Delete, Warning, InfoFilled, Camera, Cpu, Upload, Download, Document, DocumentCopy, ArrowDown, Clock, Check, CircleCheck, CircleClose, DataLine, Phone, Collection } from '@element-plus/icons-vue'
import { isIOS } from '@/utils/browser/ios-detector'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import type { SearchFormItem } from '@/types'
import { AppointmentService } from '@/api/appointmentApi'
import { CustomerService } from '@/api/customerApi'
import {
  CustomerItem,
  CustomerQueryParams,
  CustomerListResponse,
  AppointmentStatus,
  APPOINTMENT_STATUS_MAP,
  PackageTypeOption,
  ChangeOption,
  MemoryCardOption
} from '@/types/appointment'
import 'animate.css'
// 使用完整的地区数据（包含所有城市的区县数据）
import { fullAreaData as areaData, fullAreaConverter } from '@/utils/area/full-area-data'

// 预约表单数据接口
interface AppointmentFormData {
  packageType: string
  changeOption: string
  memoryCard: string
  totalPrice: number | string
  appointmentTime: string
  remark: string
  status: AppointmentStatus
}

// 搜索表单
const searchForm = reactive<{
  keyword: string
  tags: string
  appointmentStatus: AppointmentStatus | ''
}>({
  keyword: '',
  tags: '',
  appointmentStatus: ''
})

// 搜索项配置
const searchItems = ref<SearchFormItem[]>([
  {
    label: '关键词',
    prop: 'keyword',
    type: 'input',
    placeholder: '客户姓名/电话/邮箱/订单号',
    config: {
      clearable: true
    }
  },
  {
    label: '标签',
    prop: 'tags',
    type: 'select',
    placeholder: '请选择标签',
    config: {
      clearable: true
    },
    options: [
      { label: '全部', value: '' },
      { label: 'VIP客户', value: 'VIP' },
      { label: '新客户', value: '新客户' },
      { label: '老客户', value: '老客户' },
      { label: '商业客户', value: '商业客户' }
    ]
  },
  {
    label: '预约状态',
    prop: 'appointmentStatus',
    type: 'select',
    placeholder: '请选择预约状态',
    config: {
      clearable: true
    },
    options: [
      { label: '全部', value: '' },
      { label: '待确认', value: 'pending' },
      { label: '已确认', value: 'confirmed' },
      { label: '已完成', value: 'completed' },
      { label: '已取消', value: 'cancelled' }
    ]
  },

])

// 表格相关
const loading = ref(false)
const tableData = ref<CustomerItem[]>([])
const allCustomers = ref<CustomerItem[]>([]) // 存储所有客户数据

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const formRef = ref<FormInstance>()
const formData = reactive<Partial<CustomerItem & {
  region: string[],
  addressDetail: string,
  packageInfo?: {
    name: string;
    price: number;
  },
  changeOptionInfo?: {
    name: string;
    price: number;
  },
  memoryCardInfo?: {
    size: string;
    price: number;
  }
}>>({
  name: '',
  phone: '',
  email: '',
  region: [],
  addressDetail: '',
  address: '',
  tags: [],
  remark: '',
  packageType: '',
  changeOption: '',
  memoryCard: '',
  appointmentTime: '',
  appointmentRemark: '',
  status: 'pending'
})

// 查看客户详情
const viewDialogVisible = ref(false)
const selectedCustomer = ref<CustomerItem | null>(null)
// 保存原始客户数据，用于比较是否有变更
let originalCustomerData: Partial<CustomerItem> = {}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: false, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号码', trigger: 'blur' }
  ],
  email: [{ pattern: /^[\w.-]+@[\w.-]+\.\w+$/, message: '请输入正确的邮箱格式', trigger: 'blur' }],
  region: [{ required: false, message: '请选择所在地区', trigger: 'change' }],
  addressDetail: [{ required: false, message: '请输入详细地址', trigger: 'blur' }],
  packageType: [{ required: true, message: '请选择拍摄套餐', trigger: 'change' }],
  changeOption: [{ required: true, message: '请选择随心换', trigger: 'change' }],
  memoryCard: [{ required: true, message: '请选择内存卡', trigger: 'change' }]
}

// 判断是否为移动设备
const isMobile = ref(false)

// 判断是否为iOS设备
const isIOSDevice = ref(isIOS())

// 检测窗口大小变化
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 列宽记忆功能
const COLUMN_WIDTH_STORAGE_KEY = 'appointment_table_column_widths'
const columnWidths = ref<Record<string, number>>({})

// 加载保存的列宽
const loadColumnWidths = () => {
  try {
    const saved = localStorage.getItem(COLUMN_WIDTH_STORAGE_KEY)
    if (saved) {
      columnWidths.value = JSON.parse(saved)
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 保存列宽
const saveColumnWidths = () => {
  try {
    localStorage.setItem(COLUMN_WIDTH_STORAGE_KEY, JSON.stringify(columnWidths.value))
  } catch (error) {
    // 静默处理错误
  }
}

// 获取列宽
const getColumnWidth = (columnKey: string, defaultWidth: number) => {
  return columnWidths.value[columnKey] || defaultWidth
}

// 处理列宽变化
const handleColumnResize = (columnKey: string, width: number) => {
  columnWidths.value[columnKey] = width
  saveColumnWidths()
}

// 数据缓存功能
const CACHE_KEY = 'appointment_customer_cache'
const CACHE_EXPIRY = 5 * 60 * 1000 // 5分钟缓存
const dataCache = ref<{
  data: CustomerItem[]
  timestamp: number
  searchParams: string
}>()

// 生成缓存键
const generateCacheKey = (params: any) => {
  return JSON.stringify(params)
}

// 检查缓存是否有效
const isCacheValid = (cacheKey: string) => {
  if (!dataCache.value) return false

  const now = Date.now()
  const isExpired = now - dataCache.value.timestamp > CACHE_EXPIRY
  const isSameParams = dataCache.value.searchParams === cacheKey

  return !isExpired && isSameParams
}

// 从缓存获取数据
const getFromCache = (cacheKey: string) => {
  if (isCacheValid(cacheKey)) {
    return dataCache.value?.data || []
  }
  return null
}

// 保存到缓存
const saveToCache = (data: CustomerItem[], cacheKey: string) => {
  dataCache.value = {
    data: [...data],
    timestamp: Date.now(),
    searchParams: cacheKey
  }

  // 同时保存到localStorage作为持久化缓存
  try {
    localStorage.setItem(CACHE_KEY, JSON.stringify(dataCache.value))
  } catch (error) {
    // 静默处理错误
  }
}

// 加载持久化缓存
const loadPersistedCache = () => {
  try {
    const cached = localStorage.getItem(CACHE_KEY)
    if (cached) {
      const parsedCache = JSON.parse(cached)
      // 检查缓存是否过期
      if (Date.now() - parsedCache.timestamp < CACHE_EXPIRY) {
        dataCache.value = parsedCache
      } else {
        localStorage.removeItem(CACHE_KEY)
      }
    }
  } catch (error) {
    localStorage.removeItem(CACHE_KEY)
  }
}

// 清除缓存
const clearCache = () => {
  dataCache.value = undefined
  localStorage.removeItem(CACHE_KEY)
}

// 检查是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return !!(
    searchForm.keyword ||
    searchForm.tags ||
    searchForm.appointmentStatus
  )
})

// 清除所有筛选条件
const clearFilters = () => {
  searchForm.keyword = ''
  searchForm.tags = ''
  searchForm.appointmentStatus = ''
  handleSearch()
}

// 移动端滚动到指定元素
const scrollToElement = (elementSelector: string) => {
  if (isMobile.value) {
    nextTick(() => {
      const element = document.querySelector(elementSelector)
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        })
      }
    })
  }
}

// 移动端滚动到表单底部
const scrollToBottom = () => {
  if (isMobile.value) {
    nextTick(() => {
      const container = document.querySelector('.mobile-form-container')
      if (container) {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        })
      }
    })
  }
}

// 处理日期选择器获得焦点
const handleDatePickerFocus = () => {
  if (isMobile.value) {
    nextTick(() => {
      // 滚动到预约时间区域
      const appointmentSection = document.querySelector('.step-content-scrollable')
      if (appointmentSection) {
        appointmentSection.scrollTo({
          top: appointmentSection.scrollHeight - 200,
          behavior: 'smooth'
        })
      }
    })
  }
}

// 添加回车键事件监听
const handleDocKeyup = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    // 处理元素失焦
    const activeElement = document.activeElement as HTMLElement
    if (activeElement && activeElement.blur) {
      activeElement.blur()
    }

    // 触发搜索
    handleSearch()
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)

  // 添加回车键事件监听
  document.addEventListener('keyup', handleDocKeyup)

  // 加载列宽设置
  loadColumnWidths()

  // 加载持久化缓存
  loadPersistedCache()

  // 加载客户列表
  loadData()
})

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  document.removeEventListener('keyup', handleDocKeyup)
})

// 状态标签类型
const getStatusType = (status: AppointmentStatus) => {
  switch (status) {
    case 'confirmed':
      return 'success'
    case 'pending':
      return 'warning'
    case 'completed':
      return 'primary'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态显示文本
const getStatusText = (status: AppointmentStatus) => {
  return APPOINTMENT_STATUS_MAP[status] || status
}

// 格式化日期时间
const formatDateTime = (dateTimeString: string) => {
  if (!dateTimeString) return ''
  const date = new Date(dateTimeString)
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    .replace(/\//g, '-')
}

// 格式化客户ID为短格式，保证唯一性
const formatShortId = (id: string) => {
  // 截取6位随机字符
  if (!id || id.length < 6) return id

  // 确保前3位和后3位能形成6位ID
  const prefix = id.substring(0, 3)
  const suffix = id.substring(id.length - 3)

  return `DJIND-${prefix}${suffix}`
}

// 复制客户显示ID到剪贴板
const copyCustomerId = (id: string) => {
  const formattedId = formatShortId(id)
  // 创建临时input元素
  const input = document.createElement('input')
  input.value = formattedId
  document.body.appendChild(input)
  // 选择文本
  input.select()
  // 复制
  document.execCommand('copy')
  // 移除临时元素
  document.body.removeChild(input)
  // 提示
  ElMessage.success(`已复制: ${formattedId}`)
}

// 检查是否有套餐配置
const hasPackageConfig = (row: any) => {
  return (
    (row.packageInfo?.name || row.packageTypeName) ||
    (row.changeOptionInfo?.name || row.changeOptionName) ||
    (row.memoryCardInfo?.size || row.memoryCardName)
  )
}

// 移动端水平滑动相关
const currentCardIndex = ref(0)
const cardsWrapper = ref<HTMLElement>()

// 移动端卡片配置
const mobileCards = computed(() => [
  { id: 'basic', title: '基本信息' },
  { id: 'status', title: '状态信息' },
  { id: 'tags-remark', title: '标签备注' }
])

// 滚动到指定卡片
const scrollToCard = (index: number) => {
  if (!cardsWrapper.value) return

  const cardWidth = cardsWrapper.value.clientWidth
  const scrollLeft = index * cardWidth

  cardsWrapper.value.scrollTo({
    left: scrollLeft,
    behavior: 'smooth'
  })

  currentCardIndex.value = index
}

// 处理卡片滚动
const handleCardScroll = () => {
  if (!cardsWrapper.value) return

  const scrollLeft = cardsWrapper.value.scrollLeft
  const cardWidth = cardsWrapper.value.clientWidth
  const newIndex = Math.round(scrollLeft / cardWidth)

  if (newIndex !== currentCardIndex.value) {
    currentCardIndex.value = newIndex
  }
}

// 计算客户总价
const calculateCustomerTotalPrice = (customer: any) => {
  if (!customer) return 0

  // 优先使用latestAppointment中的totalPrice
  if (customer.latestAppointment?.totalPrice) {
    return customer.latestAppointment.totalPrice
  }

  // 如果没有latestAppointment，则计算各项价格之和
  let total = 0

  // 套餐价格
  if (customer.packageInfo?.price) {
    total += customer.packageInfo.price
  }

  // 随心换价格
  if (customer.changeOptionInfo?.price) {
    total += customer.changeOptionInfo.price
  }

  // 内存卡价格
  if (customer.memoryCardInfo?.price) {
    total += customer.memoryCardInfo.price
  }

  return total
}

// Excel导出相关
// 导出数据的计算属性
const exportData = computed(() => {
  return tableData.value.map(customer => ({
    customerId: formatShortId(customer._id),
    name: customer.name || '',
    phone: customer.phone || '',
    email: customer.email || '',
    address: customer.address || '',
    tags: customer.tags ? customer.tags.join(', ') : '',
    packageName: customer.packageInfo?.name || customer.packageTypeName || '',
    packagePrice: customer.packageInfo?.price || 0,
    changeOptionName: customer.changeOptionInfo?.name || customer.changeOptionName || '',
    changeOptionPrice: customer.changeOptionInfo?.price || 0,
    memoryCardName: customer.memoryCardInfo?.size || customer.memoryCardName || '',
    memoryCardPrice: customer.memoryCardInfo?.price || 0,
    totalPrice: customer.totalPrice || 0,
    appointmentTime: customer.appointmentTime ? formatDateTime(customer.appointmentTime) : '',
    appointmentRemark: customer.appointmentRemark || '',
    status: getStatusText(customer.status),
    remark: customer.remark || '',
    createdAt: customer.createdAt ? formatDateTime(customer.createdAt) : '',
    updatedAt: customer.updatedAt ? formatDateTime(customer.updatedAt) : ''
  }))
})

// 导出表头映射
const exportHeaders = {
  customerId: '客户ID',
  name: '客户姓名',
  phone: '联系电话',
  email: '电子邮箱',
  address: '客户地址',
  tags: '客户标签',
  packageName: '套餐名称',
  packagePrice: '套餐价格',
  changeOptionName: '随心换名称',
  changeOptionPrice: '随心换价格',
  memoryCardName: '内存卡规格',
  memoryCardPrice: '内存卡价格',
  totalPrice: '总价格',
  appointmentTime: '预约时间',
  appointmentRemark: '预约备注',
  status: '预约状态',
  remark: '客户备注',
  createdAt: '创建时间',
  updatedAt: '更新时间'
}

// 构建查询参数
const buildQueryParams = (): CustomerQueryParams => {
  const params: CustomerQueryParams = {
    page: 1, // 获取所有数据时设为1
    limit: 1000 // 设置足够大的值，获取所有数据
    // 不传递筛选参数，在前端进行筛选
  }

  return params
}

// 前端实现搜索筛选
const filterCustomers = () => {
  // 如果没有搜索条件，显示所有数据
  if (
    !searchForm.keyword &&
    !searchForm.tags &&
    !searchForm.appointmentStatus
  ) {
    tableData.value = allCustomers.value
    total.value = allCustomers.value.length

    return
  }

  // 根据搜索条件筛选数据
  let filteredData = [...allCustomers.value]

  // 关键词搜索 (姓名、电话、邮箱、处理过的订单号)
  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase()

    // 检查是否是处理过的订单号格式 (DJIND-XXXXXX)
    const isOrderIdFormat = /^DJIND-[a-zA-Z0-9]{6}$/i.test(keyword)

    if (isOrderIdFormat) {
      // 提取订单号中的6位字符部分
      const idPart = keyword.split('-')[1].toLowerCase()

      // 通过处理过的订单号查找客户
      filteredData = filteredData.filter((customer) => {
        if (!customer._id) return false

        // 从客户ID中提取前3位和后3位，与搜索的订单号匹配
        const prefix = customer._id.substring(0, 3).toLowerCase()
        const suffix = customer._id.substring(customer._id.length - 3).toLowerCase()
        const formattedId = (prefix + suffix).toLowerCase()

        return formattedId === idPart
      })
    } else {
      // 常规关键词搜索
      filteredData = filteredData.filter(
        (customer) =>
          (customer.name && customer.name.toLowerCase().includes(keyword)) ||
          (customer.phone && customer.phone.includes(keyword)) ||
          (customer.email && customer.email.toLowerCase().includes(keyword))
      )
    }
  }

  // 标签筛选
  if (searchForm.tags) {
    filteredData = filteredData.filter(
      (customer) => customer.tags && customer.tags.includes(searchForm.tags)
    )
  }

  // 预约状态筛选
  if (searchForm.appointmentStatus) {
    filteredData = filteredData.filter(
      (customer) => customer.status === searchForm.appointmentStatus
    )
  }



  // 移动端显示所有筛选后的数据，PC端使用分页
  if (isMobile.value) {
    // 移动端：显示所有筛选后的数据，不分页
    tableData.value = filteredData
    total.value = filteredData.length
  } else {
    // PC端：使用分页显示
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    tableData.value = filteredData.slice(startIndex, endIndex)
    total.value = filteredData.length
  }
}



// 加载数据方法
const loadData = async (useCache = true) => {
  loading.value = true

  try {
    const params = buildQueryParams()
    const cacheKey = generateCacheKey(params)

    // 尝试从缓存获取数据
    if (useCache) {
      const cachedData = getFromCache(cacheKey)
      if (cachedData) {
        allCustomers.value = cachedData
        filterCustomers()
        loading.value = false
        return
      }
    }

    const res = (await AppointmentService.getCustomerList(params)) as CustomerListResponse

    // 确保返回的数据结构正确
    if (res && res.items && Array.isArray(res.items)) {
      allCustomers.value = res.items
      // 保存到缓存
      saveToCache(res.items, cacheKey)
    } else {
      allCustomers.value = []
    }

    // 前端实现筛选和分页
    filterCustomers()
  } catch (error) {
    ElMessage.error('获取客户列表失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 刷新动画状态
const isRefreshing = ref(false)

// 处理刷新点击
const handleRefresh = async () => {
  if (isRefreshing.value) return

  // 移动端不显示动画，直接刷新
  if (isMobile.value) {
    refreshList()
    return
  }

  // PC端保持动画效果
  isRefreshing.value = true
  try {
    refreshList()
  } catch (error) {
    // 静默处理错误
  } finally {
    // 确保动画至少显示1秒
    setTimeout(() => {
      isRefreshing.value = false
    }, 1000)
  }
}

// 移动端卡片选择处理
const handleCardSelection = (item: any) => {
  const index = multipleSelection.value.findIndex(selected => selected._id === item._id)
  if (item.selected) {
    if (index === -1) {
      multipleSelection.value.push(item)
    }
  } else {
    if (index > -1) {
      multipleSelection.value.splice(index, 1)
    }
  }
}





// 刷新列表
const refreshList = () => {
  clearCache() // 清除缓存，强制重新获取数据
  allCustomers.value = [] // 清空缓存的客户数据
  loadData(false) // 重新加载数据，不使用缓存
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
  filterCustomers() // 直接在前端筛选
}

// 处理重置
const handleReset = () => {
  // 重置表单
  searchForm.keyword = ''
  searchForm.tags = ''
  searchForm.appointmentStatus = ''

  currentPage.value = 1 // 重置到第一页
  filterCustomers() // 使用重置后的条件筛选
}

// Excel导入成功处理
const handleImportSuccess = (data: any[]) => {
  try {
    if (!data || data.length === 0) {
      ElMessage.warning('导入的文件为空')
      return
    }

    // 验证导入数据格式
    const validData = data.filter(item => {
      // 至少需要客户姓名
      return item['客户姓名'] && item['客户姓名'].trim()
    })

    if (validData.length === 0) {
      ElMessage.error('导入数据格式不正确，请确保包含"客户姓名"列')
      return
    }

    // 转换导入数据格式
    const importedCustomers = validData.map((item, index) => {
      // 解析标签
      const tags = item['客户标签'] ?
        item['客户标签'].toString().split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag) :
        []

      // 解析预约状态
      let status: AppointmentStatus = 'pending'
      if (item['预约状态']) {
        const statusText = item['预约状态'].toString()
        const statusEntry = Object.entries(APPOINTMENT_STATUS_MAP).find(([key, value]) => value === statusText)
        if (statusEntry) {
          status = statusEntry[0] as AppointmentStatus
        }
      }

      return {
        _id: `import_${Date.now()}_${index}`, // 临时ID
        name: item['客户姓名'].toString().trim(),
        phone: item['联系电话'] ? item['联系电话'].toString().trim() : '',
        email: item['电子邮箱'] ? item['电子邮箱'].toString().trim() : '',
        address: item['客户地址'] ? item['客户地址'].toString().trim() : '',
        tags: tags,
        packageTypeName: item['套餐名称'] ? item['套餐名称'].toString().trim() : '',
        changeOptionName: item['随心换名称'] ? item['随心换名称'].toString().trim() : '',
        memoryCardName: item['内存卡规格'] ? item['内存卡规格'].toString().trim() : '',
        totalPrice: item['总价格'] ? Number(item['总价格']) || 0 : 0,
        appointmentTime: item['预约时间'] ? item['预约时间'].toString() : '',
        appointmentRemark: item['预约备注'] ? item['预约备注'].toString() : '',
        status: status,
        remark: item['客户备注'] ? item['客户备注'].toString() : '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        appointmentCount: 0
      } as CustomerItem
    })

    // 将导入的数据添加到现有数据中
    allCustomers.value = [...allCustomers.value, ...importedCustomers]

    // 重新筛选数据
    filterCustomers()

    ElMessage.success(`成功导入 ${importedCustomers.length} 条客户数据`)

    // 提示用户保存数据
    ElMessageBox.confirm(
      '导入的数据仅在当前页面有效，刷新页面后将丢失。是否需要手动保存重要数据？',
      '提示',
      {
        confirmButtonText: '我知道了',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => {
      // 用户取消，不做任何操作
    })

  } catch (error) {
    ElMessage.error('处理导入数据失败，请检查文件格式')
  }
}

// Excel导入失败处理
const handleImportError = (error: Error) => {
  ElMessage.error(`导入失败: ${error.message}`)
}

// Excel导出成功处理
const handleExportSuccess = () => {
  ElMessage.success('导出成功')
}

// Excel导出失败处理
const handleExportError = (error: Error) => {
  ElMessage.error(`导出失败: ${error.message}`)
}

// Excel组件引用
const excelImportRef = ref()
const excelExportRef = ref()

// Excel下拉菜单命令处理
const handleExcelCommand = (command: string) => {
  switch (command) {
    case 'import':
      // 触发导入组件的文件选择
      const importInput = excelImportRef.value?.$el?.querySelector('input[type="file"]')
      if (importInput) {
        importInput.click()
      }
      break
    case 'export':
      // 触发导出组件的导出功能
      if (exportData.value.length === 0) {
        ElMessage.warning('暂无数据可导出')
        return
      }
      excelExportRef.value?.$el?.click()
      break
    case 'template':
      downloadTemplate()
      break
  }
}

// 下载Excel模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    {
      '客户姓名': '张三',
      '联系电话': '13800138000',
      '电子邮箱': '<EMAIL>',
      '客户地址': '北京市朝阳区xxx街道xxx号',
      '客户标签': 'VIP客户,新客户',
      '套餐名称': '标准套餐',
      '随心换名称': '标准随心换',
      '内存卡规格': '32GB',
      '总价格': 2999,
      '预约时间': '2024-01-15 14:30:00',
      '预约备注': '客户要求下午拍摄',
      '预约状态': '待确认',
      '客户备注': '重要客户，需要特别关注'
    },
    {
      '客户姓名': '李四',
      '联系电话': '13900139000',
      '电子邮箱': '<EMAIL>',
      '客户地址': '上海市浦东新区xxx路xxx号',
      '客户标签': '老客户',
      '套餐名称': '高级套餐',
      '随心换名称': '高级随心换',
      '内存卡规格': '64GB',
      '总价格': 3999,
      '预约时间': '2024-01-16 10:00:00',
      '预约备注': '客户要求上午拍摄',
      '预约状态': '已确认',
      '客户备注': '回头客，服务态度要好'
    }
  ]

  // 使用ArtExcelExport组件导出模板
  try {
    // 创建临时的导出组件来下载模板
    const templateHeaders = {
      '客户姓名': '客户姓名',
      '联系电话': '联系电话',
      '电子邮箱': '电子邮箱',
      '客户地址': '客户地址',
      '客户标签': '客户标签',
      '套餐名称': '套餐名称',
      '随心换名称': '随心换名称',
      '内存卡规格': '内存卡规格',
      '总价格': '总价格',
      '预约时间': '预约时间',
      '预约备注': '预约备注',
      '预约状态': '预约状态',
      '客户备注': '客户备注'
    }

    // 手动创建Excel文件
    import('xlsx').then(XLSX => {
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.json_to_sheet(templateData)

      // 设置列宽
      const columnWidths = Object.keys(templateData[0]).map(() => ({ wch: 20 }))
      worksheet['!cols'] = columnWidths

      XLSX.utils.book_append_sheet(workbook, worksheet, '客户数据模板')

      const excelBuffer = XLSX.write(workbook, {
        bookType: 'xlsx',
        type: 'array'
      })

      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      // 使用FileSaver下载
      import('file-saver').then(FileSaver => {
        FileSaver.saveAs(blob, '预约客户导入模板.xlsx')
        ElMessage.success('模板下载成功')
      })
    })
  } catch (error) {
    ElMessage.error('下载模板失败')
  }
}



// 行点击事件
const handleRowClick = (row: CustomerItem, column: any) => {
  // 如果点击的是选择框列，不执行查看操作
  if (column.type === 'selection') {
    return
  }
  handleView(row)
}

// 新增客户
const handleAddCustomer = () => {
  dialogType.value = 'add'
  // 重置表单
  Object.assign(formData, {
    name: '',
    phone: '',
    email: '',
    region: [],
    addressDetail: '',
    address: '',
    tags: [],
    remark: '',
    packageType: '',
    changeOption: '',
    memoryCard: '',
    appointmentTime: '',
    appointmentRemark: '',
    status: 'pending'
  })

  // 重置预约表单数据
  Object.assign(appointmentData, {
    packageType: '',
    changeOption: '',
    memoryCard: '',
    totalPrice: '',
    appointmentTime: '',
    remark: '',
    status: 'pending'
  })

  // 重置步骤
  activeStep.value = 0

  dialogVisible.value = true
}

// 查看客户
const handleView = (row: CustomerItem) => {
  if (row) {
    selectedCustomer.value = row
    viewDialogVisible.value = true
  }
}

// 添加表单加载状态和提交加载状态
const formLoading = ref(false)
const submitLoading = ref(false)

// 编辑客户
const handleEdit = async (row: CustomerItem) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  formLoading.value = true

  try {
    // 加载套餐、随心换选项和内存卡数据
    await Promise.all([
      loadPackageTypes().catch(err => {
        // 添加测试数据，确保界面可以渲染
        packageOptions.value = [
          { _id: 'test1', name: '标准套餐', price: 3499, isActive: true, description: '' },
          { _id: 'test2', name: '全能套餐', price: 4499, isActive: true, description: '' }
        ];
      }),
      loadChangeOptions().catch(err => {
        // 添加测试数据，确保界面可以渲染
        changeOptions.value = [
          { _id: 'change1', name: '随心换', price: 219, isActive: true, description: '' },
          { _id: 'change2', name: '随心换', price: 349, isActive: true, description: '' }
        ];
      }),
      loadMemoryCards().catch(err => {
        // 添加测试数据，确保界面可以渲染
        memoryCards.value = [
          { _id: 'mem1', size: '128GB', price: 130, isActive: true, description: '' },
          { _id: 'mem2', size: '256GB', price: 220, isActive: true, description: '' }
        ];
      })
    ]);

    // 调用获取客户详情API
    const customerDetail = await CustomerService.getCustomerDetail(row._id)

    // 解析地址，尝试提取省市区
    const address = customerDetail.address || ''
    let region: string[] = []
    let addressDetail = ''

    // 保存原始数据，用于后续比较
    originalCustomerData = JSON.parse(JSON.stringify(customerDetail))

    // 填充表单数据
    Object.assign(formData, {
      _id: customerDetail._id,
      name: customerDetail.name,
      phone: customerDetail.phone,
      email: customerDetail.email,
      region: region,
      addressDetail: addressDetail,
      address: customerDetail.address,
      tags: customerDetail.tags,
      remark: customerDetail.remark,

      // 预约相关信息
      packageType: customerDetail.packageType || '',
      changeOption: customerDetail.changeOption || '',
      memoryCard: customerDetail.memoryCard || '',
      appointmentTime: customerDetail.appointmentTime || '',
      appointmentRemark: customerDetail.appointmentRemark || '',
      status: customerDetail.status || 'pending'
    })


  } catch (error) {
    ElMessage.error('获取客户详情失败')
  } finally {
    formLoading.value = false
  }
}

// 处理套餐变化
const handleEditPackageChange = () => {
  // 找到选中的套餐
  const selectedPackage = packageOptions.value.find(item => item._id === formData.packageType)
  if (selectedPackage) {
    // 更新packageInfo
    formData.packageInfo = {
      name: selectedPackage.name,
      price: selectedPackage.price
    }
  }
}

// 处理随心换选项变化
const handleEditChangeOptionChange = () => {
  // 找到选中的随心换选项
  const selectedChangeOption = changeOptions.value.find(item => item._id === formData.changeOption)
  if (selectedChangeOption) {
    // 更新changeOptionInfo
    formData.changeOptionInfo = {
      name: selectedChangeOption.name,
      price: selectedChangeOption.price
    }
  }
}

// 处理内存卡变化
const handleEditMemoryCardChange = () => {
  // 找到选中的内存卡
  const selectedMemoryCard = memoryCards.value.find(item => item._id === formData.memoryCard)
  if (selectedMemoryCard) {
    // 更新memoryCardInfo
    formData.memoryCardInfo = {
      size: selectedMemoryCard.size,
      price: selectedMemoryCard.price
    }
  }
}



// 新增/编辑客户对话框相关
const activeStep = ref(0)
const nextStepLoading = ref(false)
const appointmentFormRef = ref<FormInstance>()
const appointmentData = reactive<AppointmentFormData>({
  packageType: '',
  changeOption: '',
  memoryCard: '',
  totalPrice: '',
  appointmentTime: '',
  remark: '',
  status: 'pending' // 默认状态为待确认
})
const appointmentRules = {
  packageType: [{ required: true, message: '请选择拍摄套餐', trigger: 'change' }],
  changeOption: [{ required: true, message: '请选择随心换', trigger: 'change' }],
  memoryCard: [{ required: true, message: '请选择内存卡', trigger: 'change' }],
  appointmentTime: [{ required: true, message: '请选择预约时间', trigger: 'change' }],
  status: [{ required: true, message: '请选择预约状态', trigger: 'change' }],
  remark: [{ required: false, message: '请输入预约备注', trigger: 'blur' }]
}
const packageOptions = ref<PackageTypeOption[]>([])
const changeOptions = ref<ChangeOption[]>([])
const memoryCards = ref<MemoryCardOption[]>([])

// 添加动画控制变量
const isStepTransitioning = ref(false)
const animationDirection = ref<'forward' | 'backward'>('forward')

// 下一步逻辑
const goToNextStep = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      nextStepLoading.value = true
      try {
        // 加载套餐、随心换选项和内存卡数据
        await Promise.all([
          loadPackageTypes().catch(err => {
            // 添加测试数据，确保界面可以渲染
            packageOptions.value = [
              { _id: 'test1', name: '标准套餐', price: 1999, isActive: true, description: '' },
              { _id: 'test2', name: '高级套餐', price: 2999, isActive: true, description: '' }
            ];
          }),
          loadChangeOptions().catch(err => {
            // 添加测试数据，确保界面可以渲染
            changeOptions.value = [
              { _id: 'change1', name: '标准随心换', price: 299, isActive: true, description: '' },
              { _id: 'change2', name: '高级随心换', price: 599, isActive: true, description: '' }
            ];
          }),
          loadMemoryCards().catch(err => {
            // 添加测试数据，确保界面可以渲染
            memoryCards.value = [
              { _id: 'mem1', size: '32GB', price: 199, isActive: true, description: '' },
              { _id: 'mem2', size: '64GB', price: 399, isActive: true, description: '' }
            ];
          })
        ]);



        // 设置动画方向
        animationDirection.value = 'forward'

        // 延迟切换到下一步，等待动画完成
        setTimeout(() => {
          activeStep.value = 1
        }, 300)
      } catch (error) {
        ElMessage.error('加载预约选项数据失败，但您仍可以继续操作')

        // 即使失败也继续下一步，确保用户体验
        animationDirection.value = 'forward'
        setTimeout(() => {
          activeStep.value = 1
        }, 300)
      } finally {
        nextStepLoading.value = false
      }
    }
  })
}

// 上一步逻辑
const goToPreviousStep = () => {
  // 设置动画方向
  animationDirection.value = 'backward'

  // 延迟切换回上一步，等待动画完成
  setTimeout(() => {
    activeStep.value = 0
  }, 300)
}

// 加载套餐类型
const loadPackageTypes = async () => {
  try {
    const res = await AppointmentService.getPackageTypes();
    // 确保返回的数据是数组格式
    if (Array.isArray(res)) {
      packageOptions.value = res.map((item: any) => ({ ...item, isActive: item.isActive ?? true }));
    } else if (res && typeof res === 'object') {
      // 如果数据在res.data中
      const dataArray = (res as any).data;
      if (Array.isArray(dataArray)) {
        packageOptions.value = dataArray.map((item: any) => ({ ...item, isActive: item.isActive ?? true }));
      } else {
        packageOptions.value = [];
      }
    } else {
      packageOptions.value = [];
    }
  } catch (error) {
    packageOptions.value = []; // 确保即使失败也有一个空数组
    throw error;
  }
}

// 加载随心换选项
const loadChangeOptions = async () => {
  try {
    const res = await AppointmentService.getChangeOptions();
    // 确保返回的数据是数组格式
    if (Array.isArray(res)) {
      changeOptions.value = res.map((item: any) => ({ ...item, isActive: item.isActive ?? true }));
    } else if (res && typeof res === 'object') {
      // 如果数据在res.data中
      const dataArray = (res as any).data;
      if (Array.isArray(dataArray)) {
        changeOptions.value = dataArray.map((item: any) => ({ ...item, isActive: item.isActive ?? true }));
      } else {
        changeOptions.value = [];
      }
    } else {
      changeOptions.value = [];
    }
  } catch (error) {
    changeOptions.value = []; // 确保即使失败也有一个空数组
    throw error;
  }
}

// 加载内存卡
const loadMemoryCards = async () => {
  try {
    const res = await AppointmentService.getMemoryCards();
    // 确保返回的数据是数组格式
    if (Array.isArray(res)) {
      memoryCards.value = res.map((item: any) => ({ ...item, isActive: item.isActive ?? true }));
    } else if (res && typeof res === 'object') {
      // 如果数据在res.data中
      const dataArray = (res as any).data;
      if (Array.isArray(dataArray)) {
        memoryCards.value = dataArray.map((item: any) => ({ ...item, isActive: item.isActive ?? true }));
      } else {
        memoryCards.value = [];
      }
    } else {
      memoryCards.value = [];
    }
  } catch (error) {
    memoryCards.value = []; // 确保即使失败也有一个空数组
    throw error;
  }
}

// 处理套餐变化
const handlePackageChange = () => {
  // 找到选中的套餐
  const selectedPackage = packageOptions.value.find(item => item._id === appointmentData.packageType)
  if (selectedPackage) {
    // 更新总价格
    updateTotalPrice()
  }
}

// 处理随心换选项变化
const handleChangeOptionChange = () => {
  updateTotalPrice()
}

// 处理内存卡变化
const handleMemoryCardChange = () => {
  updateTotalPrice()
}

// 更新总价格
const updateTotalPrice = () => {
  let total = 0

  // 添加套餐价格
  const selectedPackage = packageOptions.value.find(item => item._id === appointmentData.packageType)
  if (selectedPackage) {
    total += selectedPackage.price
  }

  // 添加随心换选项价格
  const selectedChangeOption = changeOptions.value.find(item => item._id === appointmentData.changeOption)
  if (selectedChangeOption) {
    total += selectedChangeOption.price
  }

  // 添加内存卡价格
  const selectedMemoryCard = memoryCards.value.find(item => item._id === appointmentData.memoryCard)
  if (selectedMemoryCard) {
    total += selectedMemoryCard.price
  }

  // 更新总价格
  appointmentData.totalPrice = total
}

// 添加计算属性获取选中的套餐、随心换选项和内存卡信息
const selectedPackageInfo = computed(() => {
  return packageOptions.value.find(item => item._id === appointmentData.packageType)
})

const selectedChangeOptionInfo = computed(() => {
  return changeOptions.value.find(item => item._id === appointmentData.changeOption)
})

const selectedMemoryCardInfo = computed(() => {
  return memoryCards.value.find(item => item._id === appointmentData.memoryCard)
})

// 判断是否有选择项
const hasSelections = computed(() => {
  return (
    (selectedPackageInfo.value !== undefined && selectedPackageInfo.value !== null) ||
    (selectedChangeOptionInfo.value !== undefined && selectedChangeOptionInfo.value !== null) ||
    (selectedMemoryCardInfo.value !== undefined && selectedMemoryCardInfo.value !== null)
  )
})

// 判断是否有价格明细
const hasPriceDetails = computed(() => {
  return hasSelections.value
})

// 判断编辑模式是否有选择项目
const hasEditSelections = computed(() => {
  if (dialogType.value !== 'edit') return false
  return formData.packageInfo || formData.changeOptionInfo || formData.memoryCardInfo
})

// 显示确认提交对话框
const showConfirmSubmit = async () => {
  if (dialogType.value === 'edit') {
    // 编辑模式下先检查是否有变更
    if (!hasFormChanges()) {
      ElMessage.info('未做任何修改，无需更新');
      dialogVisible.value = false;
      return;
    }

    // 编辑模式下直接验证客户表单
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        confirmDialogVisible.value = true
      }
    })
  } else if (dialogType.value === 'add' && activeStep.value === 1) {
    // 新增模式下，在第二步验证预约表单
    if (!appointmentFormRef.value) return

    await appointmentFormRef.value.validate(async (valid) => {
      if (valid) {
        confirmDialogVisible.value = true
      }
    })
  } else if (dialogType.value === 'add' && activeStep.value === 0) {
    // 新增模式下的第一步，只添加客户
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        confirmDialogVisible.value = true
      }
    })
  }
}

// 提交确认对话框相关
const confirmDialogVisible = ref(false)

// 处理日期时间变化，确保时区正确
const handleDateTimeChange = (val: string) => {
  if (val) {
    // 确保时间格式正确，不做额外处理，使用原始选择的时间
    appointmentData.appointmentTime = val;
  }
}

// 检查表单是否有实际变更
const hasFormChanges = () => {
  // 如果是新增模式，则肯定有变更
  if (dialogType.value === 'add') return true;

  // 编辑模式下，比较表单数据与原始数据
  const currentData: Record<string, any> = {
    name: formData.name,
    phone: formData.phone,
    email: formData.email,
    address: formData.address,
    tags: formData.tags,
    remark: formData.remark,
    packageType: formData.packageType,
    changeOption: formData.changeOption,
    memoryCard: formData.memoryCard,
    appointmentTime: formData.appointmentTime,
    appointmentRemark: formData.appointmentRemark,
    status: formData.status
  };

  const originalData = originalCustomerData as Record<string, any>;

  // 比较当前数据和原始数据
  for (const key in currentData) {
    if (Object.prototype.hasOwnProperty.call(currentData, key)) {
      // 特殊处理数组类型（如tags）
      if (Array.isArray(currentData[key])) {
        // 如果数组长度不同，则有变更
        if (!originalData[key] ||
          (originalData[key] as any[]).length !== (currentData[key] as any[]).length) {
          return true;
        }

        // 比较数组内容
        const originalArray = originalData[key] as any[];
        const currentArray = currentData[key] as any[];

        for (let i = 0; i < currentArray.length; i++) {
          if (!originalArray.includes(currentArray[i])) {
            return true;
          }
        }
      }
      // 处理普通字段
      else if (currentData[key] !== originalData[key]) {
        return true;
      }
    }
  }

  // 没有检测到变更
  return false;
}

// 提交表单
const submitForm = async () => {
  submitLoading.value = true
  try {
    if (dialogType.value === 'add') {
      // 创建一个完整的载荷，包含客户信息和预约信息
      const payload: {
        [key: string]: any;
        appointment?: {
          packageType: string | null;
          packageInfo: any;
          changeOption: string | null;
          changeOptionInfo: any;
          memoryCard: string | null;
          memoryCardInfo: any;
          totalPrice: number;
          appointmentTime: string;
          remark: string;
          status: string;
        };
      } = {
        ...formData, // 客户基本信息
        // 如果是第二步且有预约信息，则添加预约相关数据
        appointment: activeStep.value === 1 ? {
          packageType: appointmentData.packageType || null,
          packageInfo: selectedPackageInfo.value ? {
            _id: selectedPackageInfo.value._id,
            name: selectedPackageInfo.value.name,
            price: selectedPackageInfo.value.price,
            isActive: true // 添加必要的isActive字段
          } : null,
          changeOption: appointmentData.changeOption || null,
          changeOptionInfo: selectedChangeOptionInfo.value ? {
            _id: selectedChangeOptionInfo.value._id,
            name: selectedChangeOptionInfo.value.name,
            price: selectedChangeOptionInfo.value.price,
            isActive: true // 添加必要的isActive字段
          } : null,
          memoryCard: appointmentData.memoryCard || null,
          memoryCardInfo: selectedMemoryCardInfo.value ? {
            _id: selectedMemoryCardInfo.value._id,
            size: selectedMemoryCardInfo.value.size,
            price: selectedMemoryCardInfo.value.price,
            isActive: true // 添加必要的isActive字段
          } : null,
          totalPrice: appointmentData.totalPrice || 0,
          // 直接使用el-date-picker组件提供的格式化值，避免时区转换
          appointmentTime: appointmentData.appointmentTime || '',
          remark: appointmentData.remark || '',
          status: appointmentData.status || 'pending' // 使用用户选择的状态
        } : null
      };

      // 如果没有预约信息，则不包含appointment字段
      if (activeStep.value === 0 || !payload.appointment?.packageType) {
        delete payload.appointment;
      }



      // 创建客户，同时提交预约信息
      const customerRes = await CustomerService.createCustomer(payload);
      ElMessage.success(payload.appointment ? '新增客户及预约成功' : '新增客户成功');
    } else {
      // 更新客户逻辑
      // 创建一个包含预约信息的完整载荷
      const payload: Partial<CustomerItem> = {
        ...formData, // 客户基本信息
        // 预约相关信息
        packageType: formData.packageType || undefined,
        changeOption: formData.changeOption || undefined,
        memoryCard: formData.memoryCard || undefined,
        appointmentTime: formData.appointmentTime || undefined,
        appointmentRemark: formData.appointmentRemark || undefined,
        status: formData.status || undefined
      };



      await CustomerService.updateCustomer(formData._id as string, payload);
      ElMessage.success('更新客户成功');
    }

    confirmDialogVisible.value = false;
    dialogVisible.value = false;
    // 重置步骤为第一步，确保下次打开对话框时从第一步开始
    activeStep.value = 0;
    // 重置预约数据
    Object.assign(appointmentData, {
      packageType: '',
      changeOption: '',
      memoryCard: '',
      totalPrice: '',
      appointmentTime: '',
      remark: ''
    });
    refreshList();
  } catch (error) {
    ElMessage.error(dialogType.value === 'add' ? '新增失败' : '更新失败');
  } finally {
    submitLoading.value = false;
  }
}

// 删除客户
const handleDelete = (row: CustomerItem) => {
  ElMessageBox.confirm(`确定要删除客户 "${row.name}" 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        // 显示加载状态
        loading.value = true
        // 调用删除API
        await CustomerService.deleteCustomer(row._id)
        ElMessage.success('删除客户成功')
        // 重新加载列表数据
        refreshList()
      } catch (error: any) {

        // 处理特定错误情况
        if (error.response?.data) {
          const errorData = error.response.data;

          // 处理关联预约的情况
          if (errorData.message === '无法删除客户，该客户有关联的预约记录') {
            ElMessageBox.confirm(
              `该客户有 ${errorData.relatedAppointments || '多个'} 条关联的预约记录。\n${errorData.solution || ''}`,
              '无法删除客户',
              {
                confirmButtonText: '同时删除关联预约',
                cancelButtonText: '取消',
                type: 'warning',
                distinguishCancelAndClose: true
              }
            ).then(async () => {
              // 用户选择同时删除关联预约
              try {
                loading.value = true;
                await CustomerService.deleteCustomer(row._id, true);
                ElMessage.success('已删除客户及其关联的预约记录');
                refreshList();
              } catch (innerError: any) {
                ElMessage.error(innerError.response?.data?.message || '删除客户及关联预约失败');
              } finally {
                loading.value = false;
              }
            }).catch(() => {
              // 用户取消操作
            });
            return;
          }

          // 显示后端返回的错误信息
          ElMessage.error(errorData.message || '删除客户失败');
        } else {
          ElMessage.error('删除客户失败');
        }
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 检查是否可以显示确认提交按钮
const showSubmitButton = computed(() => {
  return (dialogType.value === 'edit') || // 编辑模式显示
    (dialogType.value === 'add' && activeStep.value === 1) || // 新增模式第二步显示
    (dialogType.value === 'add' && activeStep.value === 0); // 新增模式第一步也显示
})

// 地区选项 - 使用完整的地区数据（所有城市都有区县）
const regionOptions = ref(fullAreaConverter.toCascaderFormat())

// 处理地区选择变化
const handleRegionChange = (value: string[]) => {
  if (value && value.length > 0) {
    // 使用新的转换器获取完整地址
    const regionAddress = fullAreaConverter.getFullAddress(value)
    const detailAddress = formData.addressDetail || ''
    formData.address = regionAddress + (detailAddress ? ' ' + detailAddress : '')
  } else {
    formData.address = formData.addressDetail || ''
  }
}

// 监听详细地址变化
const watchAddressDetail = (val: string) => {
  if (formData.region && formData.region.length > 0) {
    handleRegionChange(formData.region)
  } else {
    formData.address = val
  }
}

// 更新完整地址
const updateFullAddress = () => {
  if (formData.region && formData.region.length > 0) {
    handleRegionChange(formData.region)
  } else {
    formData.address = formData.addressDetail || ''
  }
}

// 计算总价格
const calculateTotalPrice = () => {
  let total = 0

  // 添加套餐价格
  if (formData.packageInfo) {
    total += formData.packageInfo.price || 0
  }

  // 添加随心换选项价格
  if (formData.changeOptionInfo) {
    total += formData.changeOptionInfo.price || 0
  }

  // 添加内存卡价格
  if (formData.memoryCardInfo) {
    total += formData.memoryCardInfo.price || 0
  }

  return total
}

// 标签页
const activeTab = ref('basic')

// 批量操作相关
const multipleSelection = ref<CustomerItem[]>([])

// 批量删除
const handleBatchDelete = () => {
  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 位客户吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        // 显示加载状态
        loading.value = true

        // 调用批量删除API
        try {
          const result = await CustomerService.deleteCustomers(multipleSelection.value.map(item => item._id))

          // 显示删除结果
          if (result.invalidIds?.length || result.notFoundIds?.length) {
            // 有无效ID或未找到的ID
            let message = `成功删除 ${result.deletedCount} 个客户`;
            if (result.invalidIds?.length) {
              message += `，${result.invalidIds.length} 个无效ID`;
            }
            if (result.notFoundIds?.length) {
              message += `，${result.notFoundIds.length} 个客户未找到`;
            }
            ElMessage.warning(message);
          } else {
            // 全部删除成功
            ElMessage.success(result.message || `成功删除 ${result.deletedCount} 个客户`);
          }
        } catch (error: any) {
          // 如果有关联预约的错误
          if (error.response?.data?.message === '无法删除客户，部分客户有关联的预约记录') {
            ElMessageBox.confirm(
              `部分客户有关联的预约记录。是否同时删除这些预约记录？`,
              '无法删除客户',
              {
                confirmButtonText: '同时删除关联预约',
                cancelButtonText: '取消',
                type: 'warning',
                distinguishCancelAndClose: true
              }
            ).then(async () => {
              // 用户选择同时删除关联预约
              try {
                const result = await CustomerService.deleteCustomers(multipleSelection.value.map(item => item._id), true)
                ElMessage.success(result.message || `成功删除 ${result.deletedCount} 个客户及其关联的预约记录`);
              } catch (innerError: any) {
                ElMessage.error(innerError.response?.data?.message || '删除客户及关联预约失败')
              }
            }).catch(() => {
              // 用户取消操作
            })
          } else {
            // 其他错误
            ElMessage.error(error.response?.data?.message || '批量删除客户失败')
          }
        }

        // 重新加载列表数据
        refreshList()
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 处理选择变化
const handleSelectionChange = (val: CustomerItem[]) => {
  multipleSelection.value = val
}

// 全选/取消全选
const isAllSelected = computed(() => {
  return multipleSelection.value.length === tableData.value.length && tableData.value.length > 0
})
const tableRef = ref()
const handleToggleAllSelection = () => {
  if (isAllSelected.value) {
    // 取消全选
    tableRef.value?.clearSelection()
  } else {
    // 全选
    tableRef.value?.toggleAllSelection()
  }
}
</script>

<style lang="scss" scoped>
@import 'animate.css/animate.min.css';

.appointment-list-container {
  padding: 20px;

  .list-card {
    margin-top: 20px;
    overflow: hidden;
    background-color: var(--art-main-bg-color);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;


      .left-section {
        display: flex;
        align-items: center;

        .title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .data-count {
          margin: 5.5px 0 0 10px;
        }
      }

      .right-section {
        display: flex;
        gap: 12px;
        align-items: center;
        justify-content: flex-end;

        .refresh-button {
          margin-left: auto;
          margin-bottom: 0;
          /* 确保PC端没有底部边距 */
        }
      }
    }

    :deep(.el-card__body) {
      overflow-x: auto;
    }
  }

  .operation-buttons {
    display: flex;
    gap: 12px; // PC端增大间距
    justify-content: center;

    // PC端默认样式
    .action-btn {
      min-height: auto !important;
      min-width: auto !important;
      width: auto !important;
      height: auto !important;
      padding: 8px !important; // PC端正常内边距

      .el-icon {
        font-size: 16px; // PC端正常图标大小
      }
    }

    &.mobile-buttons {
      flex-wrap: nowrap; // 不换行，保持在一行
      gap: 4px; // 减小间距
      justify-content: center;
      align-items: center;
      padding: 0 4px; // 减小左右内边距
      width: 100%;
      box-sizing: border-box;

      .el-button {
        margin: 0; // 移除额外的margin
        flex-shrink: 0; // 防止按钮被压缩
        min-width: 28px !important; // 设置最小宽度
        width: 28px !important; // 固定宽度
        height: 28px !important; // 固定高度
        padding: 0 !important;

        .el-icon {
          font-size: 12px;
        }
      }
    }

    // iOS设备特殊优化（只在移动端生效）
    &.ios-buttons.mobile-buttons {
      gap: 4px;
      padding: 0 8px;

      .action-btn {
        min-height: 32px !important;
        min-width: 32px !important;
        width: 32px;
        height: 32px;
        padding: 0 !important;
        margin: 1px;

        .el-icon {
          font-size: 14px;
        }
      }
    }

    // iOS移动端组合样式
    &.mobile-buttons.ios-buttons {
      gap: 2px; // 进一步减小间距
      padding: 0 2px; // 减小内边距
      justify-content: center;
      align-items: center;

      .action-btn {
        min-height: 26px !important;
        min-width: 26px !important;
        width: 26px !important;
        height: 26px !important;
        padding: 0 !important;
        margin: 0 !important;
        flex-shrink: 0;

        .el-icon {
          font-size: 11px;
        }
      }
    }
  }

  // PC端操作按钮专门优化
  @media screen and (min-width: 769px) {
    .operation-buttons {
      gap: 12px !important; // PC端更大的间距

      .action-btn {
        min-height: auto !important;
        min-width: auto !important;
        width: auto !important;
        height: auto !important;
        padding: 8px !important; // PC端正常内边距
        margin: 0 !important;

        .el-icon {
          font-size: 16px !important; // PC端正常图标大小
        }
      }

      // 确保PC端不受iOS样式影响
      &.ios-buttons:not(.mobile-buttons) {
        gap: 12px !important;
        padding: 0 !important;

        .action-btn {
          min-height: auto !important;
          min-width: auto !important;
          width: auto !important;
          height: auto !important;
          padding: 8px !important;
          margin: 0 !important;

          .el-icon {
            font-size: 16px !important;
          }
        }
      }
    }
  }



  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    justify-content: center;

    .tag-item {
      max-width: 100%;
      margin: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      transition: all 0.2s ease-in-out;

      &:hover {
        box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
        transform: translateY(-2px);
      }
    }

    :deep(.el-tag) {
      height: 22px;
      padding: 0 8px;
      line-height: 22px;
      border-radius: 4px;
    }
  }

  .customer-detail {
    // 客户详情容器优化
    max-width: 100%;

    .latest-appointment {
      margin-top: 20px;

      h3 {
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 500;
      }

      // 预约项目和备注的布局容器
      .appointment-layout {
        display: flex;
        gap: 20px;
        align-items: flex-start;

        // 移动端布局
        &.mobile-layout {
          flex-direction: column;
          gap: 12px;

          // 移动端标题优化
          h3 {
            font-size: 15px;
            margin-bottom: 10px;
            padding-bottom: 6px;
            border-bottom: 1px solid var(--el-border-color-lighter);
          }
        }
      }

      // 左侧预约项目
      .appointment-left {
        flex: 1;
        min-width: 0; // 防止flex项目溢出
      }

      // 右侧预约备注
      .appointment-right {
        flex: 1;
        min-width: 0; // 防止flex项目溢出

        .appointment-remark {
          background: var(--el-fill-color-extra-light);
          border-radius: 8px;
          padding: 16px;
          min-height: 120px;

          // 移动端优化
          @media (max-width: 768px) {
            padding: 12px;
            min-height: 100px;
            border: 1px solid var(--el-border-color-lighter);
          }

          .remark-content {
            color: var(--el-text-color-primary);
            line-height: 1.6;
            word-wrap: break-word;
            white-space: pre-wrap; // 保持换行格式
            font-size: 14px;

            // 移动端优化
            @media (max-width: 768px) {
              font-size: 15px;
              line-height: 1.7;
            }

            // 空状态样式
            &:empty::before {
              content: '暂无备注';
              color: var(--el-text-color-placeholder);
              font-style: italic;
            }
          }
        }
      }
    }

    :deep(.el-descriptions__cell) {
      .tag-list {
        justify-content: flex-start;
      }
    }

    .detail-tag-list {
      .tag-item {
        margin-bottom: 4px;
      }
    }
  }

  .customer-id {
    position: relative;
    display: inline-block;
    padding: 2px 6px;
    font-family: monospace;
    font-size: 0.9em;
    font-weight: 500;
    color: var(--el-text-color-secondary);
    letter-spacing: 0.5px;
    cursor: pointer;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      color: var(--el-text-color-primary);
      background-color: var(--el-fill-color-dark);

      &::before {
        position: absolute;
        top: -4px;
        left: 50%;
        z-index: 101;
        width: 8px;
        height: 8px;
        content: '';
        background-color: var(--el-color-info-light-9);
        transform: translateX(-50%) rotate(45deg);
      }

      &::after {
        position: absolute;
        top: -30px;
        left: 50%;
        z-index: 100;
        padding: 3px 8px;
        font-size: 10px;
        font-weight: normal;
        color: var(--el-color-info-dark-2);
        white-space: nowrap;
        content: '点击复制';
        background: var(--el-color-info-light-9);
        border-radius: 4px;
        box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
        transform: translateX(-50%);
      }
    }
  }
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .appointment-list-container {
    padding: 10px 10px 80px 10px;
    /* 底部留出操作栏空间 */

    .list-card {
      :deep(.el-table) {
        width: 100%;

        // 移动端操作列优化
        .el-table__cell:last-child {
          padding: 4px 2px !important;

          .cell {
            padding: 0 !important;
            overflow: visible !important;
          }

          .operation-buttons {
            width: 100%;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;

            &.mobile-buttons {
              max-width: 110px; // 限制最大宽度
              margin: 0 auto; // 居中显示
            }
          }
        }

        .el-table__body-wrapper {
          overflow-x: auto !important;
        }

        .tag-list {
          gap: 3px;

          .tag-item {
            height: 20px;
            padding: 0 5px;
            font-size: 11px;
            line-height: 20px;
          }
        }

        /* 移动端套餐配置样式优化 */
        .package-config {
          gap: 6px;

          .config-item {
            padding: 3px 6px;
            gap: 6px;
          }

          .config-icon {
            width: 16px;
            height: 16px;
            font-size: 10px;
          }

          .config-label {
            font-size: 9px;
          }

          .config-value {
            font-size: 11px;
          }

          .no-config {
            padding: 8px;
            font-size: 11px;

            .no-config-icon {
              font-size: 12px;
            }
          }
        }
      }
    }

    .card-header {
      /* 移动端不需要改变flex方向，因为使用了template条件渲染 */
      padding: 0;
    }

    .right-section {
      width: 100%;
      height: 48px;
      /* 增加高度提供更多空间 */
      display: flex;
      align-items: center;
      position: relative;
      padding: 0;
      box-sizing: border-box;

      .el-button {
        padding: 8px 12px;
        font-size: 12px;

        .el-icon {
          margin-right: 4px;
        }
      }

      /* 三按钮通用样式 */
      .excel-dropdown,
      .add-button,
      .refresh-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }

      /* 设置统一按钮尺寸，确保居中计算准确 */
      .excel-dropdown .el-button.is-circle,
      .add-button.is-circle,
      .refresh-button.is-circle {
        width: 36px !important;
        height: 36px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      /* Excel按钮 - 固定在左侧 */
      .excel-dropdown {
        left: 10px;
      }

      /* 新增按钮 - 精确居中显示 */
      .add-button {
        left: 50%;
        transform: translate(-50%, -50%);
        /* 水平和垂直居中 */
        z-index: 2;
        /* 确保新增按钮在最上层 */
      }
    }
  }

  /* 移动端头部样式 */
  .mobile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 16px 0;

    .mobile-title-section {
      flex: 1;

      .mobile-title {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 4px 0;
        line-height: 1.2;
      }

      .mobile-subtitle {
        font-size: 13px;
        color: #6b7280;
        font-weight: 400;
      }
    }

    .mobile-stats-section {
      flex-shrink: 0;

      .stats-card {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-radius: 12px;
        padding: 12px 16px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        min-width: 80px;

        .stats-number {
          font-size: 18px;
          font-weight: 700;
          color: white;
          line-height: 1;
          margin-bottom: 2px;
        }

        .stats-label {
          font-size: 11px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
          letter-spacing: 0.3px;
        }
      }
    }
  }

  /* 移动端卡片列表样式 */
  .mobile-card-list {
    margin-top: 16px;

    .mobile-skeleton {
      .mobile-skeleton-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .skeleton-header {
          display: flex;
          align-items: center;
          gap: 12px;

          .skeleton-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }

    .card-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .customer-card {
      background: white;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid #f1f5f9;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
        border-color: #e2e8f0;
      }

      &.selected {
        border-color: #3b82f6;
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
      }

      .card-header-mobile {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .customer-info {
          display: flex;
          align-items: center;
          gap: 12px;
          flex: 1;

          .customer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .avatar-text {
              color: white;
              font-size: 16px;
              font-weight: 600;
            }
          }

          .customer-details {
            flex: 1;
            min-width: 0;

            .customer-name {
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .customer-id {
              font-size: 12px;
              color: #6b7280;
              font-family: 'Monaco', 'Menlo', monospace;
            }
          }
        }

        .card-actions {
          flex-shrink: 0;

          .card-checkbox {
            margin: 0;
          }
        }
      }

      .contact-info {
        margin-bottom: 12px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f8fafc;
          border-radius: 8px;

          .info-icon {
            color: #6b7280;
            font-size: 14px;
            flex-shrink: 0;

            &.phone-icon {
              color: #10b981;
            }
          }

          .info-text {
            font-size: 14px;
            color: #374151;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .tags-section {
        margin-bottom: 12px;

        .tag-list-mobile {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;

          .tag-item-mobile {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 4px;
          }
        }
      }

      .package-section {
        margin-bottom: 16px;

        .section-title {
          font-size: 13px;
          font-weight: 600;
          color: #374151;
          margin-bottom: 8px;
          padding-left: 2px;
        }

        .package-config-mobile {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .config-item-mobile {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 10px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid transparent;

            &:has(.package-icon) {
              border-left-color: #3b82f6;
            }

            &:has(.change-icon) {
              border-left-color: #10b981;
            }

            &:has(.memory-icon) {
              border-left-color: #f59e0b;
            }

            .config-icon-mobile {
              width: 24px;
              height: 24px;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;

              &.package-icon {
                background: rgba(59, 130, 246, 0.1);
                color: #3b82f6;
              }

              &.change-icon {
                background: rgba(16, 185, 129, 0.1);
                color: #10b981;
              }

              &.memory-icon {
                background: rgba(245, 158, 11, 0.1);
                color: #f59e0b;
              }

              .el-icon {
                font-size: 12px;
              }
            }

            .config-content-mobile {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 2px;

              .config-label-mobile {
                font-size: 11px;
                color: #6b7280;
                font-weight: 500;
              }

              .config-value-mobile {
                font-size: 13px;
                color: #374151;
                font-weight: 500;
              }
            }
          }

          .no-config-mobile {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px;
            background: #fef3c7;
            border-radius: 8px;
            border-left: 3px solid #f59e0b;

            .no-config-icon-mobile {
              color: #d97706;
              font-size: 14px;
            }

            .no-config-text-mobile {
              font-size: 12px;
              color: #92400e;
              font-weight: 500;
            }
          }
        }
      }

      .card-footer {
        padding: 12px 16px;
        border-top: 1px solid var(--el-border-color-lighter);

        .action-buttons-mobile {
          display: flex;
          gap: 6px;
          justify-content: center;
          align-items: center;
          padding: 0 4px;

          .el-button {
            flex: 1;
            max-width: 80px; // 限制最大宽度防止溢出
            height: 32px; // 减小高度
            border-radius: 16px;
            font-size: 12px;
            padding: 0 8px; // 减小内边距
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
            cursor: pointer;
            user-select: none;
            white-space: nowrap; // 防止文字换行
            overflow: hidden; // 防止内容溢出

            // iOS设备特殊优化
            &:not(.action-btn) {
              min-height: auto !important;
              min-width: auto !important;
            }

            .el-icon {
              margin-right: 2px;
              font-size: 12px;
            }

            span {
              font-size: 11px;
            }
          }

          // iOS设备移动端按钮特殊优化
          &.ios-mobile-buttons {
            gap: 4px;
            padding: 0 2px;

            .mobile-action-btn {
              max-width: 70px !important;
              height: 30px !important;
              padding: 0 6px !important;
              font-size: 11px !important;
              border-radius: 15px !important;

              .el-icon {
                margin-right: 1px;
                font-size: 11px;
              }

              span {
                font-size: 10px;
              }
            }
          }
        }
      }
    }


  }

  /* 卡片列表动画 */
  .card-list-enter-active,
  .card-list-leave-active {
    transition: all 0.3s ease;
  }

  .card-list-enter-from {
    opacity: 0;
    transform: translateY(20px);
  }

  .card-list-leave-to {
    opacity: 0;
    transform: translateY(-20px);
  }

  .card-list-move {
    transition: transform 0.3s ease;
  }
}

/* 移动端客户详情弹窗样式 */
.mobile-detail-dialog {
  .el-dialog__body {
    padding: 0;
    height: 60vh;
    overflow: hidden;
  }
}

/* 移动端水平滑动容器样式 */
.mobile-swiper-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .swiper-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 12px 0;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;

    .indicator-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #cbd5e1;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #3b82f6;
        transform: scale(1.2);
      }

      &:hover {
        background: #64748b;
      }
    }
  }

  .mobile-cards-wrapper {
    flex: 1;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;

    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;

    .mobile-card {
      flex: 0 0 100%;
      width: 100%;
      padding: 20px;
      scroll-snap-align: start;
      display: flex;
      flex-direction: column;
      gap: 16px;
      background: white;
      box-sizing: border-box;

      .card-title-mobile {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e2e8f0;

        .title-icon {
          font-size: 20px;
          color: #3b82f6;
        }
      }

      .card-content-mobile {
        flex: 1;
        overflow-y: auto;
      }

      /* 基本信息卡片样式 */
      &.basic-info-card {
        .card-header-mobile {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 20px;
          padding: 20px;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border-radius: 12px;
          border: 1px solid #bae6fd;

          .customer-avatar-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

            .avatar-text-large {
              color: white;
              font-size: 24px;
              font-weight: 700;
            }
          }

          .customer-main-info {
            flex: 1;

            .customer-name-large {
              font-size: 20px;
              font-weight: 700;
              color: #1f2937;
              margin: 0 0 4px 0;
            }

            .customer-id-large {
              font-size: 13px;
              color: #6b7280;
              font-family: 'Monaco', 'Menlo', monospace;
              cursor: pointer;
              padding: 4px 8px;
              background: rgba(255, 255, 255, 0.7);
              border-radius: 6px;
              display: inline-block;

              &:hover {
                background: rgba(255, 255, 255, 0.9);
              }
            }
          }
        }

        .info-grid {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid #3b82f6;

            .info-icon {
              font-size: 20px;
              flex-shrink: 0;
            }

            .info-text {
              flex: 1;

              .info-label {
                font-size: 12px;
                color: #6b7280;
                font-weight: 500;
                margin-bottom: 2px;
              }

              .info-value {
                font-size: 14px;
                color: #374151;
                font-weight: 500;
                word-break: break-all;
              }
            }
          }
        }
      }

      /* 状态信息卡片样式 */
      &.status-card {
        .status-grid {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .status-item {
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid #10b981;

            .status-label {
              font-size: 12px;
              color: #6b7280;
              font-weight: 500;
              margin-bottom: 6px;
            }

            .status-value {
              font-size: 14px;
              color: #374151;
              font-weight: 500;
            }

            .status-tag {
              margin-top: 4px;
            }
          }
        }
      }

      /* 标签备注卡片样式 */
      &.tags-remark-card {

        .tags-section,
        .remark-section {
          margin-bottom: 20px;

          .section-label {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
          }

          .mobile-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;

            .mobile-tag {
              font-size: 12px;
              padding: 4px 8px;
            }
          }

          .remark-text {
            font-size: 14px;
            color: #374151;
            line-height: 1.6;
            background: #f8fafc;
            padding: 12px;
            border-radius: 8px;
            border-left: 3px solid #3b82f6;
          }
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 20px;
          text-align: center;

          .empty-icon {
            font-size: 48px;
            margin-bottom: 12px;
            opacity: 0.5;
          }

          .empty-text {
            font-size: 14px;
            color: #6b7280;
          }
        }
      }
    }
  }
}

/* 详情卡片通用样式 - 仅用于移动端 */
.detail-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f5f9;

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;

    .title-icon {
      font-size: 18px;
      color: #3b82f6;
    }
  }

  .card-content {
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f1f5f9;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
      }

      .info-value {
        font-size: 14px;
        color: #374151;
        font-weight: 500;
        text-align: right;
        flex: 1;
        margin-left: 12px;
        word-break: break-all;
      }
    }

    .mobile-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .mobile-tag {
        font-size: 12px;
        padding: 4px 8px;
      }
    }

    .remark-text {
      font-size: 14px;
      color: #374151;
      line-height: 1.6;
      background: #f8fafc;
      padding: 12px;
      border-radius: 8px;
      border-left: 3px solid #3b82f6;
    }
  }
}

/* PC端套餐配置样式 - 确保不被覆盖 */
.package-config {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
  min-height: 60px;
  justify-content: center;

  .config-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    background: var(--el-fill-color-extra-light);
    border-radius: 6px;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;

    &:hover {
      background: var(--el-fill-color-light);
      transform: translateX(2px);
    }

    .config-icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: white;
      flex-shrink: 0;

      &.package-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-left-color: #667eea;
      }

      &.change-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-left-color: #f093fb;
      }

      &.memory-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border-left-color: #4facfe;
      }
    }

    .config-content {
      display: flex;
      flex-direction: column;
      gap: 2px;
      flex: 1;
      min-width: 0;

      .config-label {
        font-size: 10px;
        color: var(--el-text-color-secondary);
        font-weight: 500;
        line-height: 1;
      }

      .config-value {
        font-size: 12px;
        color: var(--el-text-color-primary);
        font-weight: 500;
        line-height: 1.2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .no-config {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 12px;
    color: var(--el-text-color-placeholder);
    font-size: 12px;
    background: var(--el-fill-color-blank);
    border: 1px dashed var(--el-border-color-light);
    border-radius: 6px;

    .no-config-icon {
      font-size: 14px;
    }

    .no-config-text {
      font-weight: 500;
    }
  }
}

/* PC端预约项目样式 - 确保不被覆盖 */
.latest-appointment {
  margin-top: 20px;

  h3 {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
  }

  .appointment-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;

    .appointment-left {
      flex: 1;
      min-width: 0;

      .appointment-items {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 4px 0;

        .appointment-item {
          display: flex;
          align-items: center;
          gap: 10px;

          .item-tag {
            width: 60px;
            text-align: center;
          }

          .item-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 8px;
            border-bottom: 1px dashed rgba(0, 0, 0, 0.06);

            .item-details {
              display: flex;
              justify-content: space-between;
              align-items: center;
              width: 100%;
              padding: 0 10px;

              .item-name {
                font-size: 14px;
                max-width: 70%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .item-price {
                font-size: 14px;
                font-weight: 500;
                color: var(--el-color-danger);
                margin-left: 10px;
              }
            }
          }
        }

        .appointment-total {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          margin-top: 8px;
          font-size: 14px;

          .total-price {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-color-danger);
            margin-left: 5px;
          }
        }
      }
    }

    .appointment-right {
      flex: 1;
      min-width: 0;

      .appointment-remark {
        background: var(--el-fill-color-extra-light);
        border-radius: 8px;
        padding: 16px;
        min-height: 120px;

        .remark-content {
          color: var(--el-text-color-primary);
          line-height: 1.6;
          word-wrap: break-word;
          white-space: pre-wrap;
          font-size: 14px;

          &:empty::before {
            content: '暂无备注';
            color: var(--el-text-color-placeholder);
            font-style: italic;
          }
        }
      }
    }
  }
}

/* 基本信息卡片特殊样式 */
.detail-card.basic-info-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;

  .card-header {
    display: flex;
    align-items: center;
    gap: 16px;

    .customer-avatar-large {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      .avatar-text-large {
        color: white;
        font-size: 24px;
        font-weight: 700;
      }
    }

    .customer-main-info {
      flex: 1;

      .customer-name-large {
        font-size: 20px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 4px 0;
      }

      .customer-id-large {
        font-size: 13px;
        color: #6b7280;
        font-family: 'Monaco', 'Menlo', monospace;
        cursor: pointer;
        padding: 4px 8px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 6px;
        display: inline-block;

        &:hover {
          background: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }
}

/* 预约项目卡片特殊样式 */
.detail-card.appointment-card {
  .appointment-item-mobile {
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 8px;
    border-left: 3px solid transparent;

    &:last-of-type {
      margin-bottom: 12px;
    }

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      .item-price {
        font-size: 14px;
        font-weight: 600;
        color: #059669;
      }
    }

    .item-name {
      font-size: 13px;
      color: #374151;
      font-weight: 500;
    }

    /* 不同类型的左边框颜色 */
    &:has(.el-tag--primary) {
      border-left-color: #3b82f6;
    }

    &:has(.el-tag--success) {
      border-left-color: #10b981;
    }

    &:has(.el-tag--warning) {
      border-left-color: #f59e0b;
    }
  }

  .total-price-mobile {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-radius: 8px;
    border-left: 3px solid #f59e0b;

    .total-label {
      font-size: 16px;
      font-weight: 600;
      color: #92400e;
    }

    .total-amount {
      font-size: 18px;
      font-weight: 700;
      color: #d97706;
    }
  }
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .appointment-list-container {
    padding: 10px 10px 80px 10px;
    /* 底部留出操作栏空间 */


    /* 移动端底部操作栏样式 */
    .mobile-action-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
      padding: env(safe-area-inset-bottom, 0) 0 0 0;

      .action-bar-container {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 12px 20px;
        max-width: 100%;

        .action-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          padding: 8px 12px;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          user-select: none;
          min-width: 60px;

          &:active {
            transform: scale(0.95);
          }

          .action-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &.excel-icon {
              background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
              color: #16a34a;
              border: 1px solid #86efac;

              &:hover {
                background: linear-gradient(135deg, #bbf7d0 0%, #86efac 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
              }
            }

            &.add-icon {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              color: white;
              box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);

              &:hover {
                background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
              }
            }

            &.refresh-icon {
              background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
              color: #d97706;
              border: 1px solid #fbbf24;

              &:hover {
                background: linear-gradient(135deg, #fde68a 0%, #fbbf24 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
              }
            }
          }

          .action-label {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
            letter-spacing: 0.3px;
          }

          &.primary-action {
            .action-label {
              color: #1e40af;
              font-weight: 600;
            }
          }

          &:hover {
            background: rgba(0, 0, 0, 0.02);
          }
        }
      }
    }

    /* 移动端Excel下拉菜单样式 */
    .mobile-excel-menu {
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      border: 1px solid rgba(0, 0, 0, 0.06);
      overflow: hidden;

      .mobile-excel-item {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;

        .menu-icon {
          font-size: 16px;
          color: #6b7280;
        }

        &:hover {
          background: #f8fafc;
          color: #1e40af;

          .menu-icon {
            color: #1e40af;
          }
        }
      }
    }

    // 添加加载样式
    :deep(.el-loading-spinner) {
      .el-loading-text {
        margin-top: 10px;
        font-size: 14px;
        color: var(--el-color-primary);
      }

      .path {
        stroke: var(--el-color-primary);
      }
    }

    // 样式定义
    :deep(.confirm-content) {
      text-align: center;

      .confirm-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 24px;

        .confirm-icon {
          font-size: 48px !important;
          margin-bottom: 12px;
        }

        .confirm-title {
          font-size: 18px !important;
          font-weight: 600 !important;
          color: var(--el-text-color-primary) !important;
          margin: 0;
        }
      }

      .confirm-message {
        margin-top: 20px;

        p {
          font-size: 14px !important;
          color: var(--el-text-color-regular) !important;
          margin: 0;
        }
      }
    }



    .preview-content {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;

      img {
        max-width: 100%;
        max-height: 80vh;
      }
    }

    .upload-list-item {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;

      &-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &-actions {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        background-color: rgba(0, 0, 0, 0.4);
        opacity: 0;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }

        .el-icon {
          color: #fff;
          font-size: 18px;
          cursor: pointer;
        }
      }
    }

    .upload-hint {
      display: block;
      margin-top: 5px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }

    .el-upload__text {
      margin-top: 5px;
      font-size: 12px;
      color: var(--el-text-color-primary);
    }

    .el-upload-list__item {
      margin-top: 5px !important;
    }

    // 步骤容器
    .steps-container {
      position: relative;
      min-height: 350px; // 确保容器有足够的高度
    }

    // 步骤内容样式
    .step-content {
      position: absolute;
      width: 100%;
      left: 0;
      top: 0;
    }

    // 添加可滚动的步骤内容
    .step-content-scrollable {
      position: relative;
      max-height: 500px;
      overflow-y: auto;
      padding-right: 10px;

      // 移动端优化
      @media screen and (max-width: 768px) {
        max-height: 60vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; // iOS平滑滚动

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 2px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }
    }

    // 确认弹窗样式
    .confirm-footer {
      display: flex;
      justify-content: center;
      gap: 16px;
    }

  }
}

// 移动端地址选择器样式
:deep(.el-cascader) {
  width: 100%;
}

// 移动端样式优化
@media screen and (max-width: 768px) {
  :deep(.el-cascader-panel) {
    max-height: 300px;
    overflow-y: auto;
  }

  :deep(.el-cascader__dropdown) {
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 auto;
    border-radius: 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    position: fixed !important;
    bottom: 0 !important;
    top: auto !important;
    transform: none !important;
    max-height: 80vh !important;
    overflow: hidden !important;

    .el-scrollbar {
      height: 300px;
    }

    .el-cascader-menu {
      height: 100%;
      max-height: none;
      border-right: 1px solid #ebeef5;
      min-width: 33.33% !important;
      width: 33.33% !important;
      flex: 1 !important;

      &:last-child {
        border-right: none;
      }

      .el-cascader-node {
        padding: 12px 20px;
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.is-active {
          color: var(--el-color-primary);
          font-weight: bold;
        }
      }
    }
  }

  :deep(.el-popper) {
    width: 100% !important;
    max-width: 100% !important;
    transform: none !important;
  }

  :deep(.el-cascader__dropdown.el-popper[data-popper-placement^=bottom]) {
    margin-top: 0 !important;
  }

  :deep(.el-cascader-menus) {
    display: flex !important;
    flex-direction: row !important;
    height: 300px !important;
  }
}

// 设置Animate.css动画持续时间
.animate__animated {
  --animate-duration: 0.4s;
}

// 表格容器样式
.table-container {
  width: 100%;
  height: calc(100vh - 300px); // 为ArtTable组件提供合适的高度
  min-height: 400px;

  // 确保表格内容居中
  :deep(.el-table) {
    width: 100% !important;
    table-layout: auto; // 让表格自动调整列宽

    .el-table__cell {
      text-align: center;
    }

    .el-table__header-wrapper .el-table__cell {
      text-align: center;
      font-weight: 600;
    }
  }

  // PC端表格宽度优化
  @media (min-width: 769px) {
    :deep(.el-table) {
      table-layout: auto; // 自动布局，让列宽自适应内容
      min-width: 100%; // 确保表格至少占满容器宽度
    }

    // 让表格列能够自动扩展
    :deep(.el-table__body-wrapper),
    :deep(.el-table__header-wrapper) {
      width: 100% !important;
    }
  }

  // 标签列居中
  .tag-list {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
  }

  // 操作按钮居中
  .operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;

    // 移动端特殊处理
    @media screen and (max-width: 768px) {
      &.mobile-buttons {
        width: 100% !important;
        max-width: 110px !important;
        margin: 0 auto !important;
        padding: 0 2px !important;
        gap: 2px !important;

        .el-button {
          flex: 0 0 auto !important;
          min-width: 26px !important;
          width: 26px !important;
          height: 26px !important;

          &.action-btn {
            min-width: 26px !important;
            width: 26px !important;
            height: 26px !important;
          }
        }
      }
    }

    // 移动端按钮内缩
    &.mobile-buttons {
      padding: 0 12px;
    }
  }

  // 套餐配置样式
  .package-config {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px 0;
    min-height: 60px;
    justify-content: center;
  }

  .config-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    background: var(--el-fill-color-extra-light);
    border-radius: 6px;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;

    &:hover {
      background: var(--el-fill-color-light);
      transform: translateX(2px);
    }
  }

  .config-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    flex-shrink: 0;

    &.package-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-left-color: #667eea;
    }

    &.change-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      border-left-color: #f093fb;
    }

    &.memory-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      border-left-color: #4facfe;
    }
  }

  .config-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
    min-width: 0;
  }

  .config-label {
    font-size: 10px;
    color: var(--el-text-color-secondary);
    font-weight: 500;
    line-height: 1;
  }

  .config-value {
    font-size: 12px;
    color: var(--el-text-color-primary);
    font-weight: 500;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .no-config {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 12px;
    color: var(--el-text-color-placeholder);
    font-size: 12px;
    background: var(--el-fill-color-blank);
    border: 1px dashed var(--el-border-color-light);
    border-radius: 6px;

    .no-config-icon {
      font-size: 14px;
    }

    .no-config-text {
      font-weight: 500;
    }
  }
}

// 设置表格最小宽度，确保可滑动
:deep(.el-table) {
  min-width: 800px;
}

// 表单行样式
.form-row {
  display: flex;
  gap: 20px;

  :deep(.el-form-item) {
    flex: 1;
    margin-bottom: 18px;
  }
}

// 移动端表单适配
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  // 移动端表单容器样式
  .mobile-form-container {
    max-height: 70vh; // 增加高度以容纳更多内容
    overflow-y: auto;
    padding-right: 5px;
    -webkit-overflow-scrolling: touch; // iOS平滑滚动

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--el-color-primary-light-5);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background-color: var(--el-fill-color-lighter);
      border-radius: 4px;
    }

    // 确保预约时间等组件可见
    .step-content-scrollable {
      max-height: none; // 移除高度限制
      padding-bottom: 30px; // 底部留白，确保最后的组件可见
    }

    // 移动端日期选择器输入框样式
    .el-date-editor {
      .el-input__wrapper {
        font-size: 14px !important;
        padding: 8px 12px !important;

        .el-input__inner {
          font-size: 14px !important;
          height: auto !important;
          line-height: 1.4 !important;
        }

        .el-input__suffix {
          .el-input__suffix-inner {
            .el-icon {
              font-size: 16px !important;
            }
          }
        }
      }
    }

    // 移动端表单项标签样式
    .el-form-item {
      margin-bottom: 16px !important;

      .el-form-item__label {
        font-size: 14px !important;
        line-height: 1.4 !important;
        padding-bottom: 4px !important;
      }

      .el-form-item__content {
        line-height: 1.4 !important;
      }
    }

    // 移动端日期选择器专用样式
    .mobile-date-item {
      .el-form-item__content {
        .el-date-editor {
          height: 44px !important; // 增加触摸友好的高度

          .el-input__wrapper {
            height: 44px !important;
            padding: 10px 12px !important;
            border-radius: 6px !important;

            .el-input__inner {
              font-size: 15px !important;
              height: 24px !important;
              line-height: 24px !important;
            }

            .el-input__suffix {
              .el-input__suffix-inner {
                .el-icon {
                  font-size: 18px !important;
                  color: var(--el-color-primary) !important;
                }
              }
            }
          }

          &:hover .el-input__wrapper {
            box-shadow: 0 0 0 1px var(--el-color-primary-light-7) inset !important;
          }

          &.is-focus .el-input__wrapper {
            box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
          }
        }
      }
    }
  }

  // PC端日期选择器样式
  .pc-date-picker {
    z-index: 9999 !important;

    .el-picker-panel {
      border-radius: 8px !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
      border: 1px solid var(--el-border-color-light) !important;
      max-height: 500px !important;
      overflow-y: auto !important;

      .el-picker-panel__body {
        padding: 12px !important;
      }

      .el-picker-panel__footer {
        padding: 8px 12px !important;
        border-top: 1px solid var(--el-border-color-lighter) !important;

        .el-button {
          font-size: 13px !important;
          padding: 6px 15px !important;
        }
      }
    }
  }

  // 全局日期选择器弹出层样式修复
  :global(.el-popper.is-pure) {
    z-index: 9999 !important;

    &.pc-date-picker {
      .el-picker-panel {
        max-height: 500px !important;
        overflow-y: auto !important;
      }
    }
  }

  // 移动端日期选择器样式
  .mobile-date-picker {
    @media screen and (max-width: 768px) {
      position: fixed !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      z-index: 9999 !important;
      max-width: 95vw !important;
      max-height: 85vh !important;
      overflow-y: auto !important;
      border-radius: 8px !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;

      .el-picker-panel {
        max-width: none !important;
        max-height: none !important;
        border-radius: 8px !important;

        .el-picker-panel__body {
          padding: 8px !important;
        }

        .el-date-picker__header {
          padding: 8px 12px !important;

          .el-picker-panel__icon-btn {
            font-size: 14px !important;
            width: 32px !important;
            height: 32px !important;
          }

          .el-date-picker__header-label {
            font-size: 14px !important;
            padding: 0 4px !important;
          }
        }

        .el-picker-panel__content {
          .el-date-table {
            th {
              padding: 4px !important;
              font-size: 12px !important;
            }

            td {
              padding: 2px !important;

              .el-date-table__cell {
                height: 32px !important;
                width: 32px !important;
                font-size: 12px !important;
                line-height: 32px !important;
              }
            }
          }

          .el-time-panel {
            .el-time-spinner {
              .el-scrollbar__view {
                .el-time-spinner__item {
                  height: 32px !important;
                  line-height: 32px !important;
                  font-size: 12px !important;
                }
              }
            }
          }
        }

        .el-picker-panel__footer {
          padding: 8px 12px !important;

          .el-button {
            font-size: 12px !important;
            padding: 6px 12px !important;
          }
        }
      }
    }
  }
}

.option-price {
  color: var(--el-color-danger);
  font-size: 13px;
  margin-left: 5px;
}

.appointment-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 4px 0;
}

.appointment-item {
  display: flex;
  align-items: center;
  gap: 10px;

  // 移动端优化
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    background: var(--el-fill-color-extra-light);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
  }
}

.item-tag {
  width: 60px;
  text-align: center;

  // 移动端优化
  @media (max-width: 768px) {
    width: auto;
    text-align: left;
    margin-bottom: 6px;
  }
}

.item-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.06);

  // 移动端优化
  @media (max-width: 768px) {
    padding-bottom: 0;
    border-bottom: none;
  }
}

.item-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 10px;

  // 移动端优化
  @media (max-width: 768px) {
    padding: 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

.item-name {
  font-size: 14px;
  max-width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  // 移动端优化
  @media (max-width: 768px) {
    max-width: 100%;
    font-size: 15px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.item-price {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-color-danger);
  margin-left: 10px;

  // 移动端优化
  @media (max-width: 768px) {
    margin-left: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.appointment-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 8px;
  font-size: 14px;

  // 移动端优化
  @media (max-width: 768px) {
    justify-content: center;
    background: var(--el-color-primary-light-9);
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}

.total-price {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-danger);
  margin-left: 5px;
}

// 表格中最近预约列样式
.latest-appointment-cell {
  display: flex;
  flex-direction: column;
  gap: 5px;

  .appointment-main {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .package-name {
      font-weight: 500;
      margin-bottom: 3px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .appointment-items-mini {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;

    .mini-item {
      :deep(.el-tag) {
        padding: 0 4px;
        height: 18px;
        line-height: 18px;
        font-size: 10px;
      }
    }
  }

  .appointment-price {
    margin-top: 2px;

    .price-tag {
      font-size: 12px;
      font-weight: 500;
      color: var(--el-color-danger);
    }
  }
}

.form-section-divider {
  margin: 20px 0;
  border-top: 1px solid var(--el-border-color-light);
  position: relative;
  text-align: center;
  height: 30px;
}

.form-section-divider span {
  position: absolute;
  top: -10px;
  background-color: var(--art-main-bg-color);
  padding: 0 10px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.price-preview {
  margin-top: 20px;
  padding: 16px;
  border-radius: 8px;
  background-color: rgba(var(--el-color-primary-rgb), 0.05);

  .price-preview-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 14px;
      background-color: var(--el-color-primary);
      border-radius: 2px;
    }
  }

  .price-preview-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .price-preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .price-preview-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding: 12px 16px;
    background-color: rgba(var(--el-color-primary-rgb), 0.05);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .total-price {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-color-danger);
  }
}

.edit-tabs {
  margin-bottom: 20px;

  :deep(.el-tabs__header) {
    margin-bottom: 15px;
  }

  :deep(.el-tabs__item) {
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    font-weight: 500;

    &.is-active {
      font-weight: 600;
    }
  }

  :deep(.el-tabs__content) {
    padding: 0;
    overflow: visible;
  }

  :deep(.el-tab-pane) {
    padding: 10px 0;
  }
}

.batch-operation {
  margin-bottom: 15px;

  .batch-operation-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px;

    .batch-info {
      display: flex;
      align-items: center;

      .el-icon {
        margin-right: 8px;
      }
    }

    .batch-actions {
      display: flex;
      gap: 10px;

      .el-button {
        margin-left: 10px;

        .el-icon {
          margin-right: 4px;
        }
      }
    }

    // iOS设备批量操作优化
    &.ios-batch-content {
      padding: 0 8px;
      flex-wrap: wrap;
      gap: 8px;

      .batch-info {
        flex-shrink: 0;
        min-width: 120px;
      }

      .batch-actions {
        &.ios-batch-actions {
          gap: 6px;
          flex-wrap: wrap;

          .batch-btn {
            min-height: 32px !important;
            padding: 6px 12px !important;
            font-size: 12px;
            white-space: nowrap;

            .el-icon {
              margin-right: 2px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  // iOS设备整体优化
  &.ios-batch-operation {
    margin-bottom: 12px;

    :deep(.el-alert) {
      padding: 8px 12px;
    }

    :deep(.el-alert__content) {
      padding: 0;
    }
  }
}

// iOS设备表格多选框优化
:deep(.ios-selection-column) {
  .el-checkbox {
    .el-checkbox__input {
      .el-checkbox__inner {
        width: 16px !important;
        height: 16px !important;
        border-radius: 3px !important;

        &:after {
          width: 4px !important;
          height: 8px !important;
          left: 5px !important;
          top: 1px !important;
        }
      }
    }

    .el-checkbox__label {
      display: none;
    }
  }
}

// iOS设备表格操作列优化
@supports (-webkit-touch-callout: none) {
  .appointment-list-container {
    :deep(.el-table) {
      .el-table__cell {
        // 确保表格单元格在iOS上正确显示
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
      }

      // iOS设备多选框列优化
      .ios-selection-column {
        padding: 8px 4px !important;

        .cell {
          padding: 0 !important;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      // iOS设备操作按钮列优化
      .el-table__cell:last-child {
        .operation-buttons.ios-buttons {
          padding: 4px 2px;

          .action-btn {
            -webkit-tap-highlight-color: transparent !important;
            -webkit-touch-callout: none !important;
            touch-action: manipulation !important;
          }
        }
      }
    }
  }
}

/* Excel下拉菜单样式 */
:deep(.excel-dropdown-menu) {
  min-width: 140px;
  padding: 6px 0;

  .excel-dropdown-item {
    padding: 0;

    .dropdown-item-content {
      display: flex;
      align-items: center;
      padding: 10px 12px;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--el-color-primary-light-9);
      }

      .dropdown-icon {
        width: 24px;
        height: 24px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 14px;

        &.upload-icon {
          background-color: var(--el-color-success-light-8);
          color: var(--el-color-success);
        }

        &.download-icon {
          background-color: var(--el-color-primary-light-8);
          color: var(--el-color-primary);
        }

        &.template-icon {
          background-color: var(--el-color-warning-light-8);
          color: var(--el-color-warning);
        }
      }

      .dropdown-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        line-height: 1.2;
        white-space: nowrap;
      }
    }
  }
}

/* 移动端按钮布局优化 */
.mobile-right-section {
  display: flex;
  align-items: center;
  gap: 8px; // 增加按钮间距，让布局更舒适
  padding: 0 4px; // 额外的内边距，确保按钮不贴边

  .excel-dropdown {
    .excel-button {
      background: linear-gradient(135deg, var(--el-color-success) 0%, var(--el-color-success-light-3) 100%);
      border: none;
      box-shadow: 0 2px 8px rgba(var(--el-color-success-rgb), 0.3);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      .el-icon {
        margin: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(var(--el-color-success-rgb), 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  .add-button {
    background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;

    .el-icon {
      margin: 0 !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* 移动端圆形按钮特殊处理 */
    &.is-circle {
      position: relative;

      .el-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 18px;
        height: 18px;

        svg {
          width: 18px !important;
          height: 18px !important;
          stroke-width: 2.5;
          display: block;
        }
      }
    }

    &:hover {
      transform: translateY(-50%) scale(1.05);
      box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.4);
    }

    &:active {
      transform: translateY(-50%) scale(0.98);
    }
  }

  .refresh-button {
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    color: #606266;
    transition: all 0.3s ease;
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);

    .el-icon {
      font-size: 18px;
      transition: transform 0.6s ease;
      margin: 0 !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:hover {
      background: #ecf5ff;
      border-color: #b3d8ff;
      color: #409eff;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }

    &.refreshing {
      .el-icon {
        animation: spin 1s linear infinite;
      }

      /* 确保刷新动画时保持垂直居中 */
      transform: translateY(-50%);
    }

    &:active {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.8;
      cursor: not-allowed;
      transform: translateY(-50%);
      /* 确保禁用状态下也保持居中 */
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
}

/* 移动端下拉菜单优化 */
@media (max-width: 768px) {
  :deep(.excel-dropdown-menu) {
    min-width: 120px;
    border-radius: 10px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
    border: 1px solid var(--el-border-color-lighter);

    .excel-dropdown-item .dropdown-item-content {
      padding: 8px 10px;
      border-radius: 6px;
      margin: 2px 4px;

      &:hover {
        background-color: var(--el-color-primary-light-9);
        transform: translateX(2px);
      }

      .dropdown-icon {
        width: 22px;
        height: 22px;
        margin-right: 8px;
        font-size: 12px;
        border-radius: 4px;
      }

      .dropdown-title {
        font-size: 13px;
        font-weight: 500;
      }
    }
  }

  /* 移动端右侧按钮区域整体优化 */
  .right-section {
    padding: 4px 12px; // 左右增加内边距，避免按钮太靠边
    background: rgba(var(--el-color-primary-rgb), 0.02);
    border-radius: 12px;
    border: 1px solid var(--el-border-color-extra-light);
  }

  /* 移动端圆形按钮图标居中修复 */
  .mobile-right-section {
    .el-button.is-circle {
      width: 32px !important;
      height: 32px !important;
      padding: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      position: relative;

      /* 确保按钮内容不会溢出 */
      overflow: hidden;

      /* 重置所有内部元素的定位 */
      * {
        margin: 0 !important;
        padding: 0 !important;
      }

      .el-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;

        svg {
          display: block;
          margin: 0;
          padding: 0;
        }
      }

      /* 隐藏文字内容 */
      span:not(.el-icon) {
        display: none !important;
      }
    }

    /* Excel按钮图标 */
    .excel-button.is-circle .el-icon {
      width: 16px;
      height: 16px;

      svg {
        width: 16px !important;
        height: 16px !important;
      }
    }

    /* 加号按钮图标 - 稍大一些 */
    .add-button.is-circle .el-icon {
      width: 18px;
      height: 18px;

      svg {
        width: 18px !important;
        height: 18px !important;
        stroke-width: 2.5 !important;
      }
    }

    /* 刷新按钮响应式 */
    .refresh-button {
      width: 32px;
      height: 32px;
      margin-left: auto;
      /* 移除偏下的margin，使用与其他按钮一致的垂直居中策略 */
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%);

      .el-icon {
        font-size: 16px;
      }

      &:hover {
        transform: translateY(-50%) scale(1.05);
        box-shadow: 0 3px 8px rgba(64, 158, 255, 0.15);
      }
    }
  }
}

@media (max-width: 480px) {
  .appointment-list {
    .refresh-button {
      width: 28px;
      height: 28px;
      /* 小屏幕上更靠近边缘 */
      right: 8px;
      /* 确保移动端也保持垂直居中 */
      position: absolute;
      top: 50%;
      transform: translateY(-50%);

      .el-icon {
        font-size: 14px;
      }

      &:hover {
        box-shadow: 0 2px 6px rgba(64, 158, 255, 0.12);
        /* 悬停时保持垂直居中 */
        transform: translateY(-50%) scale(1.05);
      }

      /* 移动端不显示刷新动画 */
    }
  }
}

/* 预约状态选择器样式 */
.status-option {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    font-size: 14px;
  }

  span {
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
}



/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px dashed var(--el-border-color-light);
}

.empty-illustration {
  margin-bottom: 24px;
}

.empty-icon {
  font-size: 64px;
  color: var(--el-color-info-light-5);
  opacity: 0.6;
}

.empty-content {
  max-width: 400px;
}

.empty-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.empty-description {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

.empty-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 骨架屏样式 */
.table-skeleton {
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.skeleton-header {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.skeleton-rows {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-row {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: var(--el-fill-color-extra-light);
  border-radius: 4px;
}

/* 动画效果 */
.table-fade-enter-active,
.table-fade-leave-active {
  transition: all 0.3s ease;
}

.table-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.table-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.animated-table {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表格行动画 */
:deep(.el-table__row) {
  transition: all 0.2s ease;
}

:deep(.el-table__row:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 状态标签动画 */
:deep(.el-tag) {
  transition: all 0.2s ease;
}

:deep(.el-tag:hover) {
  transform: scale(1.05);
}

/* 按钮动画 */
:deep(.el-button) {
  transition: all 0.2s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}

:deep(.el-button:active) {
  transform: translateY(0);
}

/* 客户详情对话框优化 */
.customer-detail-dialog {

  // 确保对话框本身充分利用空间
  .el-dialog {
    margin: 0 auto;

    @media (min-width: 769px) {
      max-width: 1000px;
      width: 1000px;
    }
  }

  .el-dialog__body {
    padding: 20px 24px;

    // 桌面端优化
    @media (min-width: 769px) {
      padding: 24px 32px;
    }
  }

  // 确保对话框内容充分利用空间
  .customer-detail {
    width: 100%;
    max-width: 100%;

    // 基础信息区域也要充分利用空间
    .el-descriptions {
      width: 100%;
    }

    .latest-appointment {
      width: 100%;

      .appointment-layout {
        width: 100%;

        // 桌面端增加间距
        @media (min-width: 769px) {
          gap: 32px;
        }
      }
    }

    // 预约项目和备注区域优化
    .appointment-left,
    .appointment-right {
      width: 100%;
      max-width: 100%;

      // 桌面端确保充分利用空间
      @media (min-width: 769px) {
        flex: 1;
        min-width: 0;
        max-width: none;
      }

      h3 {
        width: 100%;
      }
    }

    // 预约项目区域优化
    .appointment-items {
      width: 100%;

      .appointment-item {
        width: 100%;

        .item-content {
          width: 100%;

          .item-details {
            width: 100%;
          }
        }
      }
    }

    // 预约备注区域增大
    .appointment-remark {
      width: 100%;

      @media (min-width: 769px) {
        min-height: 150px;
        padding: 20px;
      }

      .remark-content {
        width: 100%;
      }
    }
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .empty-state {
    padding: 60px 16px;
  }

  .empty-icon {
    font-size: 48px;
  }

  .empty-title {
    font-size: 16px;
  }

  .empty-description {
    font-size: 13px;
  }

  .skeleton-row {
    gap: 8px;
    padding: 8px;
  }

  .skeleton-row .el-skeleton-item:nth-child(n+6) {
    display: none;
    /* 移动端隐藏部分骨架屏元素 */
  }
}

/* 加载状态优化 */
.table-container {
  position: relative;
  min-height: 400px;
}

/* 虚拟滚动优化 */
:deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color) transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: var(--el-border-color);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background-color: transparent;
}

/* PC端表格宽度铺满优化 */
.full-width-table {
  width: 100% !important;

  :deep(.el-table) {
    width: 100% !important;
    table-layout: auto !important;
  }

  :deep(.el-table__header-wrapper),
  :deep(.el-table__body-wrapper),
  :deep(.el-table__footer-wrapper) {
    width: 100% !important;
  }

  // PC端特殊优化
  @media (min-width: 769px) {
    :deep(.el-table) {
      table-layout: auto !important;
      width: 100% !important;
      min-width: 100% !important;
    }

    // 确保表格列能够自动扩展填充剩余空间
    :deep(.el-table__header),
    :deep(.el-table__body) {
      width: 100% !important;
      table-layout: auto !important;
    }

    // 让没有固定宽度的列自动扩展
    :deep(.el-table-column:not([width])) {
      width: auto !important;
    }
  }
}

/* 移动端优化样式 */
@media (max-width: 768px) {
  .mobile-header {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px 0;

    .mobile-title-section {
      text-align: center;

      .mobile-title {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 4px 0;
      }

      .mobile-subtitle {
        font-size: 14px;
        color: var(--el-text-color-regular);
        margin: 0;
      }
    }

    .mobile-stats-section {
      display: flex;
      justify-content: center;

      .stats-card {
        background: linear-gradient(135deg, var(--el-color-primary-light-9), var(--el-color-primary-light-8));
        border-radius: 12px;
        padding: 16px 24px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .stats-number {
          font-size: 24px;
          font-weight: 700;
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }

        .stats-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .mobile-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    padding: 0 16px 16px;

    .mobile-add-btn {
      flex: 1;
      max-width: 200px;
      height: 40px;
      border-radius: 20px;
      font-weight: 500;
    }

    .mobile-refresh-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;

      &.refreshing {
        :deep(.el-icon) {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .mobile-card-list {
    /* 移动端虚拟滚动容器 */
    height: calc(100vh - 280px);
    /* 减去头部、搜索栏、底部操作栏的高度 */
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    /* iOS 平滑滚动 */
    overscroll-behavior: contain;
    /* 防止过度滚动 */
    touch-action: pan-y;
    /* 只允许垂直滚动 */

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.3);
    }

    .customer-card {
      margin-bottom: 16px;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }

      /* 最后一个卡片增加底部间距，避免被底部操作栏遮挡 */
      &:last-child {
        margin-bottom: 80px;
      }

      .card-header-mobile {
        padding: 16px;
        border-bottom: 1px solid var(--el-border-color-lighter);

        .customer-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .customer-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
            display: flex;
            align-items: center;
            justify-content: center;

            .avatar-text {
              color: white;
              font-size: 18px;
              font-weight: 600;
            }
          }

          .customer-details {
            flex: 1;

            .customer-name {
              font-size: 16px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              margin-bottom: 4px;
            }

            .customer-id {
              font-size: 12px;
              color: var(--el-text-color-regular);
              font-family: 'Courier New', monospace;
            }
          }
        }
      }

      .contact-info {
        padding: 12px 16px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .info-icon {
            color: var(--el-color-primary);
            font-size: 16px;
          }

          .info-text {
            font-size: 14px;
            color: var(--el-text-color-regular);
          }
        }
      }

      .tags-section {
        padding: 0 16px 12px;

        .tag-list-mobile {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;

          .tag-item-mobile {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
          }
        }
      }

      .package-section {
        padding: 12px 16px;

        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
        }

        .package-config-mobile {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .config-item-mobile {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: var(--el-fill-color-lighter);
            border-radius: 8px;

            .config-icon-mobile {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;

              &.package-icon {
                background: var(--el-color-primary-light-8);
                color: var(--el-color-primary);
              }

              &.change-icon {
                background: var(--el-color-success-light-8);
                color: var(--el-color-success);
              }

              &.memory-icon {
                background: var(--el-color-warning-light-8);
                color: var(--el-color-warning);
              }
            }

            .config-content-mobile {
              flex: 1;

              .config-label-mobile {
                font-size: 12px;
                color: var(--el-text-color-regular);
                margin-right: 8px;
              }

              .config-value-mobile {
                font-size: 13px;
                color: var(--el-text-color-primary);
                font-weight: 500;
              }
            }
          }

          .no-config-mobile {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 16px;
            color: var(--el-text-color-placeholder);
            font-size: 13px;

            .no-config-icon-mobile {
              font-size: 16px;
            }
          }
        }
      }


    }


  }

  /* 移动端搜索栏优化 */
  :deep(.art-search-bar) {
    .search-form {
      .el-form-item {
        margin-bottom: 12px;

        .el-form-item__content {

          .el-input,
          .el-select {
            height: 40px;

            .el-input__inner,
            .el-select__wrapper {
              height: 40px;
              border-radius: 20px;
            }
          }
        }
      }

      .search-buttons {
        .el-button {
          height: 40px;
          border-radius: 20px;
          padding: 0 20px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
// 确认提交框全局样式
.confirm-selection-preview {
  margin: 20px 0 !important;
  padding: 20px !important;
  background: var(--el-bg-color-page) !important;
  border-radius: 12px !important;
  border: 1px solid var(--el-border-color-light) !important;
  text-align: left !important;

  .confirm-preview-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: var(--el-text-color-primary) !important;
    margin-bottom: 16px !important;
    text-align: center !important;
    position: relative !important;

    &::after {
      content: '' !important;
      position: absolute !important;
      bottom: -8px !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      width: 40px !important;
      height: 3px !important;
      background-color: var(--el-color-primary) !important;
      border-radius: 2px !important;
    }
  }

  .confirm-preview-items {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
  }

  .confirm-preview-item {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    padding: 12px !important;
    background: var(--el-fill-color-blank) !important;
    border-radius: 8px !important;
    border: 1px solid var(--el-border-color-lighter) !important;
    transition: all 0.3s ease !important;

    &:hover {
      border-color: var(--el-color-primary-light-5) !important;
      box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.1) !important;
    }
  }

  .confirm-item-tag {
    width: 60px !important;
    text-align: center !important;
    flex-shrink: 0 !important;

    .el-tag {
      width: 100% !important;
      display: inline-block !important;
      font-weight: 500 !important;
    }
  }

  .confirm-item-content {
    flex: 1 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
  }

  .confirm-item-name {
    font-size: 14px !important;
    color: var(--el-text-color-primary) !important;
    font-weight: 500 !important;
  }

  .confirm-item-price {
    font-size: 14px !important;
    color: var(--el-color-primary) !important;
    font-weight: 600 !important;
    min-width: 70px !important;
    text-align: right !important;
  }

  .confirm-total {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-top: 16px !important;
    padding: 16px !important;
    background: linear-gradient(135deg, var(--el-color-primary-light-9), var(--el-color-success-light-9)) !important;
    border-radius: 10px !important;
    border: 2px solid var(--el-color-primary-light-7) !important;

    .confirm-total-label {
      font-size: 16px !important;
      font-weight: 600 !important;
      color: var(--el-text-color-primary) !important;
    }

    .confirm-total-price {
      font-size: 20px !important;
      font-weight: 700 !important;
      color: var(--el-color-danger) !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }
  }
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .confirm-selection-preview {
    margin: 15px 0 !important;
    padding: 15px !important;
    border-radius: 10px !important;

    .confirm-preview-title {
      font-size: 15px !important;
      margin-bottom: 12px !important;

      &::after {
        width: 30px !important;
        height: 2px !important;
        bottom: -6px !important;
      }
    }

    .confirm-preview-items {
      gap: 10px !important;
    }

    .confirm-preview-item {
      padding: 10px !important;
      gap: 10px !important;
      border-radius: 6px !important;
    }

    .confirm-item-tag {
      width: 50px !important;

      .el-tag {
        font-size: 11px !important;
        padding: 2px 4px !important;
      }
    }

    .confirm-item-name {
      font-size: 13px !important;
    }

    .confirm-item-price {
      font-size: 13px !important;
      min-width: 60px !important;
    }

    .confirm-total {
      margin-top: 12px !important;
      padding: 12px !important;
      border-radius: 8px !important;
      border-width: 1px !important;

      .confirm-total-label {
        font-size: 14px !important;
      }

      .confirm-total-price {
        font-size: 18px !important;
      }
    }
  }
}

/* 超小屏幕优化（小于480px） */
@media (max-width: 480px) {
  .confirm-selection-preview {
    margin: 10px 0 !important;
    padding: 12px !important;

    .confirm-preview-title {
      font-size: 14px !important;
      margin-bottom: 10px !important;
    }

    .confirm-preview-items {
      gap: 8px !important;
    }

    .confirm-preview-item {
      padding: 8px !important;
      gap: 8px !important;
      flex-direction: column !important;
      align-items: flex-start !important;
    }

    .confirm-item-tag {
      width: auto !important;
      align-self: flex-start !important;

      .el-tag {
        font-size: 10px !important;
        padding: 1px 3px !important;
      }
    }

    .confirm-item-content {
      width: 100% !important;
      margin-top: 4px !important;
    }

    .confirm-item-name {
      font-size: 12px !important;
    }

    .confirm-item-price {
      font-size: 12px !important;
      min-width: 50px !important;
    }

    .confirm-total {
      margin-top: 10px !important;
      padding: 10px !important;
      flex-direction: column !important;
      gap: 6px !important;
      text-align: center !important;

      .confirm-total-label {
        font-size: 13px !important;
      }

      .confirm-total-price {
        font-size: 16px !important;
      }
    }
  }
}

.package-option {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
  padding: 4px 0 !important;

  .package-price {
    color: var(--el-color-primary) !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    margin-left: 12px !important;
  }
}
</style>
