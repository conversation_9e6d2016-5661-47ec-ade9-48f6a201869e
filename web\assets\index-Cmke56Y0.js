var r=Object.defineProperty,e=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,m=(e,t,o)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;import{k as p}from"./index-BOCMSBcY.js";/* empty css               *//* empty css               */import l from"./CardList-BnAm59JM.js";import a from"./ActiveUser-B888jGyd.js";import j from"./SalesOverview-Cs9VOnrN.js";import n from"./NewUser-6vXYMgmS.js";import d from"./Dynamic-BMqj8R5p.js";import u from"./TodoList-geoSK84i.js";import c from"./AboutProject-CawxSZ0w.js";import{k as f,O as _,C as v,x as b,D as g,aY as y,aX as x}from"./vendor-9ydHGNSq.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./vue3-count-to.esm-Dsfbf00q.js";import"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";import"./index-CAIJlOgO.js";import"./ArtTable-ClHSxhXb.js";/* empty css                      *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-CT2bh8-V.js";/* empty css                    *//* empty css                       *//* empty css                        */import"./avatar1-CutlWZf5.js";import"./avatar6-C8uJx9vz.js";const w={class:"console"},P=f((C=((r,e)=>{for(var t in e||(e={}))s.call(e,t)&&m(r,t,e[t]);if(o)for(var t of o(e))i.call(e,t)&&m(r,t,e[t]);return r})({},{name:"Console"}),e(C,t({__name:"index",setup:r=>(p().scrollToTop(),(r,e)=>{const t=y,o=x;return v(),_("div",w,[b(l),b(o,{gutter:20},{default:g((()=>[b(t,{sm:24,md:12,lg:10},{default:g((()=>[b(a)])),_:1}),b(t,{sm:24,md:12,lg:14},{default:g((()=>[b(j)])),_:1})])),_:1}),b(o,{gutter:20},{default:g((()=>[b(t,{sm:24,md:24,lg:12},{default:g((()=>[b(n)])),_:1}),b(t,{sm:24,md:12,lg:6},{default:g((()=>[b(d)])),_:1}),b(t,{sm:24,md:12,lg:6},{default:g((()=>[b(u)])),_:1})])),_:1}),b(c)])})}))));var C;const D=O(P,[["__scopeId","data-v-ce1d1679"]]);export{D as default};
