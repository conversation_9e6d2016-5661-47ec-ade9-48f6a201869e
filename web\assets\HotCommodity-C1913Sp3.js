import{_ as s}from"./index-CAIJlOgO.js";import{k as t,O as e,C as i,S as l,x as a,F as o,Z as r,W as c,X as n}from"./vendor-9ydHGNSq.js";import{_ as d}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./index-DEP0vMzR.js";import"./useChart-DM-2b2dH.js";const u={class:"card art-custom-card weekly-card",style:{height:"28.2rem"}},p={class:"content"},m={class:"text"},v={class:"title"},x={class:"subtitle"},h=d(t({__name:"HotCommodity",setup(t){const d=[{icon:"&#xe718;",title:"智能手表Pro",subtitle:"电子产品",value:"1,286件",color:"bg-primary"},{icon:"&#xe70c;",title:"时尚连衣裙",subtitle:"女装服饰",value:"892件",color:"bg-success"},{icon:"&#xe813;",title:"厨房小家电",subtitle:"家居用品",value:"756件",color:"bg-error"}];return(t,h)=>{const b=s;return i(),e("div",u,[h[1]||(h[1]=l("div",{class:"card-header"},[l("p",{class:"title"},"热销商品"),l("p",{class:"subtitle"},"本周销售排行")],-1)),a(b,{showAxisLabel:!1,showAxisLine:!1,showSplitLine:!1,showAreaColor:!0,data:[8,40,82,35,90,52,35],height:"9rem"}),l("div",p,[(i(),e(o,null,r(d,(s=>l("div",{class:"item",key:s.title},[l("div",{class:c(["icon",s.color])},h[0]||(h[0]=[l("i",{class:"iconfont-sys"},"",-1)]),2),l("div",m,[l("p",v,n(s.title),1),l("span",x,n(s.subtitle),1)]),l("div",{class:c(["value",s.color])},[l("span",null,"+"+n(s.value),1)],2)]))),64))])])}}}),[["__scopeId","data-v-c3550835"]]);export{h as default};
