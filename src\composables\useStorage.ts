import { ref, watch, type Ref } from 'vue'
import { 
  useLocalStorage as useVueUseLocalStorage,
  useSessionStorage as useVueUseSessionStorage,
  type RemovableRef
} from '@vueuse/core'

export interface StorageOptions<T> {
  /** 默认值 */
  defaultValue?: T
  /** 序列化函数 */
  serializer?: {
    read: (value: string) => T
    write: (value: T) => string
  }
  /** 是否在写入失败时抛出错误 */
  throwOnError?: boolean
  /** 存储变化时的回调 */
  onChanged?: (newValue: T, oldValue: T) => void
}

/**
 * 增强的本地存储Hook
 * 基于VueUse的useLocalStorage，添加了类型安全和错误处理
 */
export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions<T> = {}
): RemovableRef<T> {
  const {
    serializer,
    throwOnError = false,
    onChanged
  } = options

  try {
    const storage = useVueUseLocalStorage(key, defaultValue, {
      serializer,
      throwOnError
    })

    // 监听变化
    if (onChanged) {
      watch(storage, (newValue, oldValue) => {
        onChanged(newValue, oldValue)
      }, { deep: true })
    }

    return storage
  } catch (error) {
    return ref(defaultValue) as RemovableRef<T>
  }
}

/**
 * 增强的会话存储Hook
 * 基于VueUse的useSessionStorage，添加了类型安全和错误处理
 */
export function useSessionStorage<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions<T> = {}
): RemovableRef<T> {
  const {
    serializer,
    throwOnError = false,
    onChanged
  } = options

  try {
    const storage = useVueUseSessionStorage(key, defaultValue, {
      serializer,
      throwOnError
    })

    // 监听变化
    if (onChanged) {
      watch(storage, (newValue, oldValue) => {
        onChanged(newValue, oldValue)
      }, { deep: true })
    }

    return storage
  } catch (error) {
    return ref(defaultValue) as RemovableRef<T>
  }
}

/**
 * 存储管理器
 * 提供统一的存储操作接口
 */
export class StorageManager {
  private static prefix = 'app_'

  /**
   * 生成带前缀的键名
   */
  private static getKey(key: string): string {
    return `${this.prefix}${key}`
  }

  /**
   * 设置本地存储
   */
  static setLocal<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(this.getKey(key), serializedValue)
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 获取本地存储
   */
  static getLocal<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(this.getKey(key))
      if (item === null) return defaultValue ?? null
      return JSON.parse(item)
    } catch (error) {
      return defaultValue ?? null
    }
  }

  /**
   * 删除本地存储
   */
  static removeLocal(key: string): void {
    try {
      localStorage.removeItem(this.getKey(key))
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 设置会话存储
   */
  static setSession<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value)
      sessionStorage.setItem(this.getKey(key), serializedValue)
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 获取会话存储
   */
  static getSession<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = sessionStorage.getItem(this.getKey(key))
      if (item === null) return defaultValue ?? null
      return JSON.parse(item)
    } catch (error) {
      return defaultValue ?? null
    }
  }

  /**
   * 删除会话存储
   */
  static removeSession(key: string): void {
    try {
      sessionStorage.removeItem(this.getKey(key))
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 清空本地存储（仅清空带前缀的项）
   */
  static clearLocal(): void {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 清空会话存储（仅清空带前缀的项）
   */
  static clearSession(): void {
    try {
      const keys = Object.keys(sessionStorage)
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          sessionStorage.removeItem(key)
        }
      })
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 获取存储大小（字节）
   */
  static getStorageSize(type: 'local' | 'session' = 'local'): number {
    try {
      const storage = type === 'local' ? localStorage : sessionStorage
      let size = 0
      
      for (const key in storage) {
        if (storage.hasOwnProperty(key) && key.startsWith(this.prefix)) {
          size += storage[key].length + key.length
        }
      }
      
      return size
    } catch (error) {
      return 0
    }
  }

  /**
   * 检查存储是否可用
   */
  static isStorageAvailable(type: 'local' | 'session' = 'local'): boolean {
    try {
      const storage = type === 'local' ? localStorage : sessionStorage
      const testKey = '__storage_test__'
      storage.setItem(testKey, 'test')
      storage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }
}

/**
 * 用户偏好设置Hook
 */
export function useUserPreferences() {
  const theme = useLocalStorage('user_theme', 'light')
  const language = useLocalStorage('user_language', 'zh-CN')
  const sidebarCollapsed = useLocalStorage('sidebar_collapsed', false)
  const tablePageSize = useLocalStorage('table_page_size', 10)

  return {
    theme,
    language,
    sidebarCollapsed,
    tablePageSize
  }
}

/**
 * 临时数据Hook（使用sessionStorage）
 */
export function useTempData<T>(key: string, defaultValue: T) {
  return useSessionStorage(key, defaultValue)
}

/**
 * 持久化数据Hook（使用localStorage）
 */
export function usePersistentData<T>(key: string, defaultValue: T) {
  return useLocalStorage(key, defaultValue)
}
