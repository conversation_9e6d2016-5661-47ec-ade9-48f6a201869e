var t=Object.defineProperty,s=Object.defineProperties,e=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,o=(s,e,a)=>e in s?t(s,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[e]=a;import"./index-BOCMSBcY.js";/* empty css                  *//* empty css                     */import{k as l,c,V as n,O as p,C as m,S as d,x as u,a2 as b,R as v,X as y,ah as f,Q as _,u as j,D as O,F as h,Z as x,W as w,B as C,a6 as g,a3 as k}from"./vendor-9ydHGNSq.js";import{_ as P}from"./_plugin-vue_export-helper-BCo6x5W8.js";const B={class:"basic-list-card"},M={class:"art-card art-custom-card"},D={class:"card-header"},L={class:"card-title"},H={class:"card-subtitle"},I=["innerHTML"],S={class:"item-content"},T={class:"item-title"},q={class:"item-status"},A={class:"item-time"},E=l((F=((t,s)=>{for(var e in s||(s={}))r.call(s,e)&&o(t,e,s[e]);if(a)for(var e of a(s))i.call(s,e)&&o(t,e,s[e]);return t})({},{name:"ArtDataListCard"}),Q={__name:"index",props:{list:{},title:{},subtitle:{},maxCount:{default:5},showMoreButton:{type:Boolean}},emits:["more"],setup(t,{emit:s}){const e=t,a=c((()=>66*e.maxCount+"px")),r=s,i=()=>r("more");return(t,s)=>{const e=f,r=k,o=n("ripple");return m(),p("div",B,[d("div",M,[d("div",D,[d("p",L,y(t.title),1),d("p",H,y(t.subtitle),1)]),u(e,{style:_({height:j(a)})},{default:O((()=>[(m(!0),p(h,null,x(t.list,((t,s)=>(m(),p("div",{key:s,class:"list-item"},[t.icon?(m(),p("div",{key:0,class:w(["item-icon",t.class])},[d("i",{class:"iconfont-sys",innerHTML:t.icon},null,8,I)],2)):v("",!0),d("div",S,[d("div",T,y(t.title),1),d("div",q,y(t.status),1)]),d("div",A,y(t.time),1)])))),128))])),_:1},8,["style"]),t.showMoreButton?b((m(),C(r,{key:0,class:"more-btn",onClick:i},{default:O((()=>s[0]||(s[0]=[g("查看更多")]))),_:1,__:[0]})),[[o]]):v("",!0)])])}}},s(F,e(Q))));var F,Q;const R=P(E,[["__scopeId","data-v-c3238f47"]]);export{R as _};
