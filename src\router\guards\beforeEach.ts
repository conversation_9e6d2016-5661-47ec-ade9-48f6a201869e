import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import NProgress from 'nprogress'
import { useSettingStore } from '@/store/modules/setting'
import { useUserStore } from '@/store/modules/user'
import { useMenuStore } from '@/store/modules/menu'
import { setWorktab } from '@/utils/navigation'
import { setPageTitle, setSystemTheme } from '../utils/utils'
import { menuService } from '@/api/menuApi'
import { registerDynamicRoutes } from '../utils/registerRoutes'
import { AppRouteRecord } from '@/types/router'
import { RoutesAlias, HOME_PAGE } from '../routesAlias'
import { menuDataToRouter } from '../utils/menuToRouter'
import { asyncRoutes } from '../routes/asyncRoutes'
import { loadingService } from '@/utils/ui'
import { useCommon } from '@/composables/useCommon'
import { useWorktabStore } from '@/store/modules/worktab'

// 是否已注册动态路由
export const isRouteRegistered = ref(false)

/**
 * 路由全局前置守卫
 * 处理进度条、获取菜单列表、动态路由注册、404 检查、工作标签页及页面标题设置
 */
export function setupBeforeEachGuard(router: Router): void {
  router.beforeEach(
    async (
      to: RouteLocationNormalized,
      from: RouteLocationNormalized,
      next: NavigationGuardNext
    ) => {
      try {
        await handleRouteGuard(to, from, next, router)
      } catch (error) {
        // 路由守卫处理失败
        next('/exception/500')
      }
    }
  )
}

/**
 * 处理路由守卫逻辑
 */
async function handleRouteGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
  router: Router
): Promise<void> {
  const settingStore = useSettingStore()
  const userStore = useUserStore()

  // 处理进度条
  if (settingStore.showNprogress) {
    NProgress.start()
  }

  // 设置系统主题
  setSystemTheme(to)

  // 处理登录状态
  if (!(await handleLoginStatus(to, userStore, next))) {
    return
  }

  // 处理动态路由注册
  if (!isRouteRegistered.value && userStore.isLogin) {
    await handleDynamicRoutes(to, router, next)
    return
  }

  // 处理已知的匹配路由
  if (to.matched.length > 0) {
    setWorktab(to)
    setPageTitle(to)
    next()
    return
  }

  // 尝试刷新路由重新注册
  if (userStore.isLogin) {
    isRouteRegistered.value = false
    await handleDynamicRoutes(to, router, next)
    return
  }

  // 如果以上都不匹配，跳转到404
  next(RoutesAlias.Exception404)
}

/**
 * 处理登录状态
 */
async function handleLoginStatus(
  to: RouteLocationNormalized,
  userStore: ReturnType<typeof useUserStore>,
  next: NavigationGuardNext
): Promise<boolean> {
  // 检查token是否存在
  const hasToken = !!userStore.accessToken

  // 如果用户登录状态为true但token不存在，说明token可能已过期或被删除
  if (userStore.isLogin && !hasToken) {
    userStore.logOut()
    next(RoutesAlias.Login)
    return false
  }

  // 处理根路径访问
  if (to.path === '/') {
    if (userStore.isLogin) {
      next(HOME_PAGE)
    } else {
      next(RoutesAlias.Login)
    }
    return false
  }

  // 如果用户已登录且尝试访问登录页面，则重定向到首页
  if (userStore.isLogin && to.path === RoutesAlias.Login) {
    next(HOME_PAGE)
    return false
  }

  // 如果用户未登录且尝试访问需要登录的页面，则重定向到登录页
  if (!userStore.isLogin && to.path !== RoutesAlias.Login && !to.meta.noLogin) {
    userStore.logOut()
    next(RoutesAlias.Login)
    return false
  }
  return true
}

/**
 * 处理动态路由注册
 */
async function handleDynamicRoutes(
  to: RouteLocationNormalized,
  router: Router,
  next: NavigationGuardNext
): Promise<void> {
  try {
    await getMenuData(router)
    next({
      path: to.path,
      query: to.query,
      hash: to.hash,
      replace: true
    })
  } catch (error) {
    next('/exception/500')
  }
}

/**
 * 获取菜单数据
 * @param router 路由实例
 */
async function getMenuData(router: Router): Promise<void> {
  try {
    if (useCommon().isFrontendMode.value) {
      await processFrontendMenu(router) // 前端控制模式
    } else {
      await processBackendMenu(router) // 后端控制模式
    }
  } catch (error) {
    handleMenuError(error)
  }
}

/**
 * 处理前端控制模式的菜单逻辑
 */
async function processFrontendMenu(router: Router): Promise<void> {
  const closeLoading = loadingService.showLoading()

  // 每次都重新生成菜单列表，避免缓存问题
  const menuList = [...asyncRoutes].map((route) => menuDataToRouter(route))
  const userStore = useUserStore()
  const roles = userStore.info.roles

  if (!roles || roles.length === 0) {
    closeLoading()
    throw new Error('获取用户角色失败')
  }

  const filteredMenuList = filterMenuByRoles(menuList, roles)
  await new Promise((resolve) => setTimeout(resolve, 300))
  await registerAndStoreMenu(router, filteredMenuList, closeLoading)
}

/**
 * 处理后端控制模式的菜单逻辑
 */
async function processBackendMenu(router: Router): Promise<void> {
  const closeLoading = loadingService.showLoading()
  const { menuList } = await menuService.getMenuList()
  await registerAndStoreMenu(router, menuList, closeLoading)
}

/**
 * 注册路由并存储菜单数据
 */
async function registerAndStoreMenu(
  router: Router,
  menuList: AppRouteRecord[],
  closeLoading: () => void
): Promise<void> {
  if (!isValidMenuList(menuList)) {
    closeLoading()
    throw new Error('获取菜单列表失败，请重新登录')
  }

  const menuStore = useMenuStore()
  menuStore.setMenuList(menuList)
  registerDynamicRoutes(router, menuList)
  isRouteRegistered.value = true
  useWorktabStore().validateWorktabs(router)
  closeLoading()
}

/**
 * 处理菜单相关错误
 */
function handleMenuError(error: unknown): void {
  // 菜单处理失败
  useUserStore().logOut()
  throw error instanceof Error ? error : new Error('获取菜单列表失败，请重新登录')
}

// 简化权限检查函数（路由守卫版本）
const checkAdvancedPermissionInGuard = (requiredPerm: string, userPerms: string[]): boolean => {
  // 1. 直接权限匹配
  if (userPerms.includes(requiredPerm)) {
    return true
  }

  // 2. 检查层级权限格式（父菜单:子菜单）
  const hierarchicalPerms = userPerms.filter(p => p.includes(':'))
  for (const hierarchicalPerm of hierarchicalPerms) {
    const [parent, child] = hierarchicalPerm.split(':')
    if (child === requiredPerm || parent === requiredPerm) {
      return true
    }
  }

  return false
}

/**
 * 根据角色过滤菜单
 */
const filterMenuByRoles = (menu: AppRouteRecord[], roles: string[]): AppRouteRecord[] => {
  const userStore = useUserStore()
  const userPerms = userStore.getUserInfo.perms || []

  return menu.reduce((acc: AppRouteRecord[], item) => {
    let hasPermission = false

    // 如果是超级管理员，显示所有菜单
    if (roles?.includes('superadmin')) {
      hasPermission = true
    } else {
      // 检查权限：优先使用 perms，如果没有则使用 roles
      if (item.meta?.perms) {
        // 首页对所有登录用户可见
        if (item.meta.perms.includes('Home')) {
          hasPermission = true
        } else {
          // 使用高级权限检查
          hasPermission = item.meta.perms.some((perm: string) => {
            return checkAdvancedPermissionInGuard(perm, userPerms)
          })
        }
      } else if (item.meta?.roles) {
        // 兼容旧的 roles 权限控制
        hasPermission = item.meta.roles.some((role: string) => roles?.includes(role))
      } else {
        // 如果没有设置权限，则所有用户都可以访问
        hasPermission = true
      }
    }

    if (hasPermission) {
      const filteredItem = { ...item }
      if (filteredItem.children?.length) {
        filteredItem.children = filterMenuByRoles(filteredItem.children, roles)
        // 如果父菜单有权限但所有子菜单都被过滤掉了，则隐藏父菜单
        if (filteredItem.children.length === 0 && item.meta?.perms) {
          hasPermission = false
        }
      }

      if (hasPermission) {
        acc.push(filteredItem)
      }
    }

    return acc
  }, [])
}

/**
 * 验证菜单列表是否有效
 */
function isValidMenuList(menuList: AppRouteRecord[]): boolean {
  return Array.isArray(menuList) && menuList.length > 0
}

/**
 * 重置路由相关状态
 */
export function resetRouterState(router: Router): void {
  isRouteRegistered.value = false

  // 获取所有路由名称
  const allRoutes = router.getRoutes()

  // 清理动态注册的路由（除了静态路由）
  allRoutes.forEach((route) => {
    // 保留静态路由，清理动态路由
    const isStaticRoute = [
      'Home', 'Login', 'Register', 'ForgetPassword',
      'Exception403', 'Exception404', 'Exception500'
    ].includes(route.name as string)

    if (!isStaticRoute && route.name) {
      try {
        router.removeRoute(route.name)
      } catch (error) {
        // 忽略移除路由时的错误
        console.warn(`Failed to remove route: ${route.name}`, error)
      }
    }
  })

  // 清空菜单数据
  const menuStore = useMenuStore()
  menuStore.setMenuList([])

  // 清理工作标签
  const worktabStore = useWorktabStore()
  worktabStore.opened = []

  // 清理会话存储
  sessionStorage.removeItem('iframeRoutes')
}
