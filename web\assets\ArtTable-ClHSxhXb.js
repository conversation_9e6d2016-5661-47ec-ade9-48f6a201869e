import"./index-BOCMSBcY.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import{h as e,r as a,k as t,c as l,s as n,O as o,C as i,S as r,R as s,a2 as d,aG as g,B as p,a_ as u,u as c,D as h,P as f,aJ as m,x as y,bn as x,ai as z,W as v,i as b,aW as w,Q as S,n as k}from"./vendor-9ydHGNSq.js";import{T as B}from"./formEnum-BLgiZVxV.js";import{a as j}from"./index-CT2bh8-V.js";import{_}from"./_plugin-vue_export-helper-BCo6x5W8.js";const T=e("tableStore",(()=>{const e=a(B.DEFAULT),t=a(!1),l=a(!1),n=a(!1);return{tableSize:e,isZebra:t,isBorder:l,isHeaderBackground:n,setTableSize:a=>e.value=a,setIsZebra:e=>t.value=e,setIsBorder:e=>l.value=e,setIsHeaderBackground:e=>n.value=e}}),{persist:{key:"table",storage:localStorage}}),C={class:"table-container"},H=_(t({__name:"ArtTable",props:{data:{default:()=>[]},loading:{type:Boolean,default:!1},rowKey:{default:"id"},border:{type:[Boolean,null],default:null},stripe:{type:[Boolean,null],default:null},index:{type:Boolean,default:!1},indexFixed:{type:Boolean,default:!0},height:{default:"100%"},maxHeight:{},showHeader:{type:Boolean,default:!0},highlightCurrentRow:{type:Boolean,default:!1},emptyText:{default:"暂无数据"},pagination:{type:Boolean,default:!0},total:{default:0},currentPage:{default:1},pageSize:{default:20},pageSizes:{default:()=>[10,20,30,50]},hideOnSinglePage:{type:Boolean,default:!0},paginationAlign:{default:"center"},paginationSize:{default:"default"},paginationLayout:{default:""},showHeaderBackground:{type:[Boolean,null],default:null},marginTop:{default:20},size:{}},emits:["update:currentPage","update:pageSize","row-click","size-change","current-change","selection-change"],setup(e,{expose:t,emit:B}){const{width:_}=j(),H=l((()=>_.value<500)),P=e,E=l((()=>P.paginationLayout?P.paginationLayout:H.value?"prev, pager, next, sizes":"total, sizes, prev, pager, next, jumper")),R=B,A=T(),{tableSize:I}=n(A),L=a();t({expandAll:()=>{const e=L.value;if(!e)return;const a=t=>{t.forEach((t=>{var l;(null==(l=t.children)?void 0:l.length)>0&&(e.toggleRowExpansion(t,!0),a(t.children))}))};a(P.data)},collapseAll:()=>{const e=L.value;if(!e)return;const a=t=>{t.forEach((t=>{var l;(null==(l=t.children)?void 0:l.length)>0&&(e.toggleRowExpansion(t,!1),a(t.children))}))};a(P.data)}});const Z=l((()=>P.size||I.value)),D=l((()=>null===P.stripe?A.isZebra:P.stripe)),F=l((()=>null===P.border?A.isBorder:P.border)),O=l((()=>null===P.showHeaderBackground?A.isHeaderBackground:P.showHeaderBackground)),U=l((()=>{if(!P.pagination||P.total>P.data.length)return P.data;const e=(P.currentPage-1)*P.pageSize,a=e+P.pageSize;return P.data.slice(e,a)})),W=l({get:()=>P.currentPage,set:e=>R("update:currentPage",e)}),K=l({get:()=>P.pageSize,set:e=>R("update:pageSize",e)}),$=(e,a,t)=>{R("row-click",e,a,t)},q=e=>{R("selection-change",e)},G=e=>{R("size-change",e)},J=e=>{R("current-change",e),Q()},Q=()=>{k((()=>{L.value&&L.value.setScrollTop(0)}))};return(e,a)=>{const t=m,l=x,n=u,k=w,B=g;return i(),o("div",{class:v(["art-table",{"header-background":c(O)}]),style:S({marginTop:e.marginTop+"px",height:e.total?"calc(100% - 90px)":"calc(100% - 25px)"})},[r("div",C,[d((i(),p(n,{ref_key:"tableRef",ref:L,data:c(U),"row-key":e.rowKey,height:e.height,"max-height":e.maxHeight,"show-header":e.showHeader,"highlight-current-row":e.highlightCurrentRow,size:c(Z),stripe:c(D),border:c(F),"header-cell-style":{backgroundColor:c(O)?"var(--el-fill-color-lighter)":"",fontWeight:"500"},onRowClick:$,onSelectionChange:q},{empty:h((()=>[d(y(l,{description:e.emptyText},null,8,["description"]),[[z,!e.loading]])])),default:h((()=>[e.index&&c(U).length>0?(i(),p(t,{key:0,type:"index",width:"60",label:"序号",align:"center",fixed:!!e.indexFixed&&"left"},null,8,["fixed"])):s("",!0),c(U).length?f(e.$slots,"default",{key:1},void 0,!0):s("",!0)])),_:3},8,["data","row-key","height","max-height","show-header","highlight-current-row","size","stripe","border","header-cell-style"])),[[B,e.loading]])]),e.pagination&&c(U).length>0?(i(),o("div",{key:0,class:v(["table-pagination",e.paginationAlign])},[y(k,{"current-page":c(W),"onUpdate:currentPage":a[0]||(a[0]=e=>b(W)?W.value=e:null),"page-size":c(K),"onUpdate:pageSize":a[1]||(a[1]=e=>b(K)?K.value=e:null),"page-sizes":e.pageSizes,"pager-count":c(H)?5:7,total:e.total,background:!0,size:e.paginationSize,layout:c(E),onSizeChange:G,onCurrentChange:J,"hide-on-single-page":e.hideOnSinglePage},null,8,["current-page","page-size","page-sizes","pager-count","total","size","layout","hide-on-single-page"])],2)):s("",!0)],6)}}}),[["__scopeId","data-v-eb242e98"]]);export{H as _,T as u};
