import App from './App.vue'
import { createApp } from 'vue'
import { initStore } from './store'                 // Store
import { initRouter } from './router'               // Router

// 初始化控制台日志过滤器
import { initConsoleFilter } from '@/utils/sys/console-filter'
// 智能字体加载
import { lazyLoadFonts } from '@/utils/browser/font-loader'
import { initFontMonitor } from '@/utils/browser/font-monitor'
// iOS兼容性处理
import { initIOSCompatibility } from '@/utils/browser/ios-detector'
initConsoleFilter({
  enabled: true,
  enableInDev: false, // 开发环境禁用过滤，便于调试
  filterKeywords: [
    'Image URL being set',
    'store installed',
    '🍍',
    '🆕',
    'was preloaded using link preload but not used',
    'iconfont.woff',
    'The resource'
  ]
})
import '@styles/reset.scss'                         // 重置HTML样式
import '@styles/app.scss'                           // 全局样式
import '@styles/el-ui.scss'                         // 优化element样式
import '@styles/mobile.scss'                        // 移动端样式优化
import '@styles/ios-fixes.scss'                     // iOS设备兼容性修复
import '@styles/element-ios-fixes.scss'             // Element Plus iOS兼容性修复
import '@styles/ios-fixes.scss'                     // iOS设备兼容性修复
import '@styles/change.scss'                        // 主题切换过渡优化
import '@styles/theme-animation.scss'               // 主题切换动画
import '@styles/el-light.scss'                      // Element 自定义主题（亮色）
import '@styles/el-dark.scss'                       // Element 自定义主题（暗色）
import '@styles/dark.scss'                          // 系统主题
import '@icons/system/iconfont.js'                  // 系统彩色图标
import '@icons/system/iconfont.css'                 // 系统图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { setupGlobDirectives } from './directives'
import language from './locales'

// iOS设备触摸事件优化
document.addEventListener(
  'touchstart',
  function () {},
  { passive: false }
)

// 防止iOS双击缩放
let lastTouchEnd = 0
document.addEventListener('touchend', function (event) {
  const now = (new Date()).getTime()
  if (now - lastTouchEnd <= 300) {
    event.preventDefault()
  }
  lastTouchEnd = now
}, false)

// 防止iOS长按选择文本
document.addEventListener('selectstart', function (event) {
  event.preventDefault()
}, false)

// 优化iOS滚动性能
if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
  document.addEventListener('touchmove', function (event) {
    // 允许在可滚动元素内滚动
    const target = event.target as HTMLElement
    const scrollableParent = target.closest('.scrollable, .el-scrollbar__wrap, .el-table__body-wrapper, .mobile-card-list, .overflow-auto, .overflow-y-auto')

    // 只在非滚动区域阻止默认行为
    if (!scrollableParent) {
      event.preventDefault()
    }
  }, { passive: false })
}

const app = createApp(App)
initStore(app)
initRouter(app)
setupGlobDirectives(app)

app.use(language)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.mount('#app')

// 启用延迟字体加载，避免预加载警告
lazyLoadFonts()

// 初始化字体监控（仅开发环境）
if (import.meta.env.DEV) {
  initFontMonitor()
}

// 初始化iOS兼容性处理
initIOSCompatibility()

