import{_ as s}from"./index-MUIx2wft.js";import{b as t}from"./lf_icon2-BooRsIsW.js";import{d as e}from"./index-BOCMSBcY.js";import{k as a,c as o,B as r,C as i,D as n,S as l,a6 as c,u as m}from"./vendor-9ydHGNSq.js";import{_ as d}from"./_plugin-vue_export-helper-BCo6x5W8.js";const f=d(a({__name:"Banner",setup(a){const d=e(),f=o((()=>d.getUserInfo)),p=()=>{};return(e,a)=>{const o=s;return i(),r(o,{class:"banner",height:"13.3rem",title:`欢迎回来 ${m(f).userName}`,backgroundColor:"var(--el-color-primary-light-9)",titleColor:"var(--art-gray-900)",decoration:!1,meteorConfig:{enabled:!0,count:10},buttonConfig:{show:!1,text:""},imageConfig:{src:m(t),width:"18rem",bottom:"-7.5rem"},onClick:p},{default:n((()=>a[0]||(a[0]=[l("div",{class:"banner-slot"},[l("div",{class:"item"},[l("p",{class:"title"},[c("¥2,340"),l("i",{class:"iconfont-sys text-success"},"")]),l("p",{class:"subtitle"},"今日销售额")]),l("div",{class:"item"},[l("p",{class:"title"},[c("35%"),l("i",{class:"iconfont-sys text-success"},"")]),l("p",{class:"subtitle"},"较昨日")])],-1)]))),_:1,__:[0]},8,["title","imageConfig"])}}}),[["__scopeId","data-v-a80f1cdd"]]);export{f as default};
