var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,l=(t,r,a)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[r]=a;import"./index-BOCMSBcY.js";/* empty css                        *//* empty css                    *//* empty css                  */import{_ as i}from"./ArtTable-ClHSxhXb.js";import{k as p,r as n,O as m,C as c,S as d,x as j,a6 as b,D as u,aJ as f,R as v,X as y,F as g,Z as _}from"./vendor-9ydHGNSq.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                  *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-CT2bh8-V.js";const w={class:"page-content"},x={class:"title"},h={key:0,style:{"margin-top":"10px"}},P=p((k=((e,t)=>{for(var r in t||(t={}))o.call(t,r)&&l(e,r,t[r]);if(a)for(var r of a(t))s.call(t,r)&&l(e,r,t[r]);return e})({},{name:"ChangeLog"}),t(k,r({__name:"index",setup(e){const t=n([{version:"1.0.0",title:"系统初始版本",date:"2024-01-01",content:"系统正式上线"}]);return(e,r)=>{const a=f;return c(),m("div",w,[r[0]||(r[0]=d("h3",{class:"table-title"},[d("i",{class:"iconfont-sys"},""),b("更新日志")],-1)),j(i,{data:t.value,pagination:!1},{default:u((()=>[j(a,{label:"版本号",prop:"version",width:"200"}),j(a,{label:"内容"},{default:u((e=>[d("div",x,y(e.row.title),1),e.row.detail?(c(),m("div",h,[(c(!0),m(g,null,_(e.row.detail,((e,t)=>(c(),m("div",{class:"detail-item",key:t},y(t+1)+". "+y(e),1)))),128))])):v("",!0)])),_:1}),j(a,{label:"时间",prop:"date"})])),_:1},8,["data"])])}}}))));var k;const C=O(P,[["__scopeId","data-v-88ee1ef1"]]);export{C as default};
