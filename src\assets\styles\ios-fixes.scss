// iOS设备兼容性修复样式

// iOS设备CSS变量
:root {
  --vh: 1vh; // 动态视口高度
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
}

// iOS设备标识类样式
.ios-device {
  // 使用动态视口高度
  .full-height,
  .min-h-screen {
    height: calc(var(--vh, 1vh) * 100) !important;
    min-height: calc(var(--vh, 1vh) * 100) !important;
  }

  // 安全区域适配
  .safe-area-top {
    padding-top: var(--safe-area-inset-top) !important;
  }

  .safe-area-bottom {
    padding-bottom: var(--safe-area-inset-bottom) !important;
  }

  .safe-area-left {
    padding-left: var(--safe-area-inset-left) !important;
  }

  .safe-area-right {
    padding-right: var(--safe-area-inset-right) !important;
  }
}

// iOS Safari特定修复
@supports (-webkit-touch-callout: none) {
  // 这个选择器只在iOS Safari中生效
  
  // 修复按钮点击问题
  button,
  .el-button,
  [role="button"],
  .clickable {
    cursor: pointer !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;

    // 防止双击缩放
    touch-action: manipulation !important;

    // 只对非表格操作按钮应用最小尺寸
    &:not(.action-btn):not(.el-table .el-button) {
      min-height: 44px;
      min-width: 44px;
    }
  }
  
  // 修复输入框问题
  input,
  textarea,
  select,
  .el-input__inner,
  .el-textarea__inner {
    -webkit-appearance: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    border-radius: 0 !important; // 防止iOS默认圆角
    
    // 防止输入时页面缩放
    font-size: 16px !important;
  }
  
  // 修复滚动问题
  .el-scrollbar__wrap,
  .el-table__body-wrapper,
  .el-select-dropdown__wrap,
  .scrollable {
    -webkit-overflow-scrolling: touch !important;
    overflow-scrolling: touch !important;
    
    // 防止滚动时的弹性效果影响布局
    overscroll-behavior: contain !important;
  }
  
  // 修复弹窗和抽屉滚动
  .el-dialog__wrapper,
  .el-drawer__wrapper,
  .el-overlay {
    -webkit-overflow-scrolling: touch !important;
    overflow-scrolling: touch !important;
  }
  
  // 修复表格滚动
  .el-table {
    .el-table__body-wrapper {
      -webkit-overflow-scrolling: touch !important;
      
      // 防止表格滚动时出现空白
      transform: translateZ(0) !important;
      -webkit-transform: translateZ(0) !important;
    }
  }
  
  // 修复下拉菜单
  .el-select-dropdown,
  .el-dropdown-menu,
  .el-picker-panel {
    -webkit-overflow-scrolling: touch !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
  }
  
  // 修复日期选择器
  .el-date-picker,
  .el-time-picker {
    -webkit-overflow-scrolling: touch !important;
  }
  
  // 修复固定定位元素
  .el-affix,
  .fixed-element {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
  }
  
  // 修复flex布局问题
  .el-row,
  .el-col,
  .flex,
  .d-flex {
    // 防止flex项目在iOS上出现布局问题
    min-height: 0 !important;
    min-width: 0 !important;
  }
  
  // 修复卡片和容器
  .el-card,
  .art-custom-card,
  .page-content {
    // 防止iOS上的渲染问题
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    
    // 确保边框正确显示
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }
  
  // 修复图片显示问题
  img {
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    
    // 防止图片拖拽
    -webkit-user-drag: none !important;
    user-drag: none !important;
  }
  
  // 修复链接点击问题
  a {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    touch-action: manipulation !important;
  }
  
  // 修复表单元素
  .el-form-item {
    // 防止表单项在iOS上出现布局问题
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
  }
  
  // 修复侧边栏滚动
  .layout-sidebar {
    -webkit-overflow-scrolling: touch !important;
    overflow-scrolling: touch !important;
  }
  
  // 修复主内容区域滚动
  .layout-main {
    -webkit-overflow-scrolling: touch !important;
    overflow-scrolling: touch !important;
    
    // 防止滚动时的性能问题
    will-change: scroll-position !important;
  }
}

// 通用iOS修复（不依赖@supports）
@media screen and (max-width: 768px) {
  // 防止iOS Safari地址栏影响视口高度
  .full-height {
    height: 100vh !important;
    height: -webkit-fill-available !important;
  }
  
  // 修复iOS上的点击延迟
  * {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
  }
  
  // 确保所有可点击元素都有正确的触摸处理
  button,
  .el-button,
  .el-link,
  [role="button"],
  .clickable,
  .el-menu-item,
  .el-submenu__title {
    touch-action: manipulation !important;
    cursor: pointer !important;
  }
  
  // 修复输入框在iOS上的问题
  input,
  textarea,
  select {
    // 防止输入时页面缩放
    font-size: 16px !important;
    
    // 移除iOS默认样式
    -webkit-appearance: none !important;
    appearance: none !important;
    border-radius: 0 !important;
  }
  
  // 修复iOS上的滚动性能
  .scrollable,
  .el-scrollbar__wrap,
  .overflow-auto,
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch !important;
    overflow-scrolling: touch !important;
    
    // 优化滚动性能
    will-change: scroll-position !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
  }
}

// 修复iOS 15+的新问题
@supports (height: 100dvh) {
  .full-height {
    height: 100dvh !important;
  }
}

// 修复iOS上的字体渲染问题
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  * {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }
}
