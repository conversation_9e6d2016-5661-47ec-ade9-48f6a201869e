<template>
  <div class="user-management">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="请选择角色" clearable>
            <el-option v-for="role in roleOptions" :key="role.value" :label="role.label" :value="role.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" v-ripple>搜索</el-button>
          <el-button @click="handleReset" v-ripple>重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" :icon="Plus" @click="handleAdd" v-if="canCreateUser" v-ripple>
        新增用户
      </el-button>
      <el-button @click="refresh" :loading="loading" v-ripple>
        刷新
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="tableData" border stripe @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="username" label="用户名" min-width="120" />
      <el-table-column prop="nickname" label="昵称" min-width="120" />
      <el-table-column prop="email" label="邮箱" min-width="150" />
      <el-table-column prop="roles" label="角色" width="120">
        <template #default="{ row }">
          <el-tag v-for="role in row.roles" :key="role" :type="getRoleTagType(role)" size="small" class="role-tag">
            {{ getRoleDisplayName(role) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isActive" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isActive ? 'success' : 'danger'">
            {{ row.isActive ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="lastLoginAt" label="最后登录" width="150" align="center">
        <template #default="{ row }">
          {{ formatDate(row.lastLoginAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)" v-ripple>
            编辑
          </el-button>
          <el-button type="warning" size="small" @click="handleResetPassword(row)" v-if="canResetPassword" v-ripple>
            重置密码
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)" v-ripple>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handlePageChange" />
    </div>

    <!-- 用户表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑用户' : '新增用户'" width="500px" :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="formData.nickname" placeholder="请输入昵称" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="formData.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>

        <el-form-item label="角色" prop="roles">
          <el-select v-model="formData.roles" multiple placeholder="请选择角色">
            <el-option v-for="role in availableRoles" :key="role.value" :label="role.label" :value="role.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-radio-group v-model="formData.isActive">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting" v-ripple>
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="重置密码" width="400px" :close-on-click-modal="false">
      <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
        <el-form-item label="用户名">
          <el-input :value="currentUser?.username" disabled />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" show-password />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handlePasswordSubmit" :loading="passwordSubmitting" v-ripple>
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { UserManageService, type UserItem } from '@/api/userManageApi'
import { RoleService, type Role } from '@/api/roleApi'
// 显式导入增强版本的Hooks
import { useTable, useForm, usePermission } from '@/composables'

defineOptions({ name: 'UserManagement' })

// 搜索表单
const searchForm = reactive({
  username: '',
  role: '',
  status: ''
})

// 角色选项
const roleOptions = ref([
  { label: '超级管理员', value: 'superadmin' },
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' }
])

// 权限检查
const { hasPermission, hasRole, isSuperAdmin } = usePermission()
const canCreateUser = computed(() => hasRole(['admin', 'superadmin']))
const canResetPassword = computed(() => isSuperAdmin.value)

// 可分配的角色
const availableRoles = computed(() => {
  if (isSuperAdmin.value) {
    return roleOptions.value.filter(role => role.value !== 'superadmin')
  } else if (hasRole('admin')) {
    return roleOptions.value.filter(role => role.value === 'user')
  }
  return []
})

// 表格管理
const {
  tableData,
  loading,
  pagination,
  selectedRows,
  refresh,
  search,
  handlePageChange,
  handleSizeChange,
  handleSelectionChange
} = useTable({
  api: (params) => UserManageService.getUserList(params),
  deleteApi: (id) => UserManageService.deleteUser(id),
  immediate: true
})

// 表单管理
const {
  formRef,
  formData,
  rules,
  submitting,
  submit,
  setCreateMode,
  setEditMode
} = useForm<UserItem>({
  initialData: {
    username: '',
    nickname: '',
    email: '',
    password: '',
    roles: [],
    isActive: true
  },
  rules: {
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    roles: [{ required: true, message: '请选择角色', trigger: 'change' }]
  },
  submitApi: (data) => {
    return formData._id
      ? UserManageService.updateUser(formData._id, data)
      : UserManageService.createUser(data)
  },
  onSuccess: () => {
    dialogVisible.value = false
    refresh()
  }
})

// 对话框状态
const dialogVisible = ref(false)
const isEdit = computed(() => !!formData._id)

// 重置密码相关
const passwordDialogVisible = ref(false)
const passwordSubmitting = ref(false)
const currentUser = ref<UserItem | null>(null)
const passwordFormRef = ref()
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 操作处理
const handleAdd = () => {
  setCreateMode()
  dialogVisible.value = true
}

const handleEdit = (row: UserItem) => {
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = async (row: UserItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${row.username}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await UserManageService.deleteUser(row._id)
    ElMessage.success('删除成功')
    refresh()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleResetPassword = (row: UserItem) => {
  currentUser.value = row
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordDialogVisible.value = true
}

const handleSubmit = async () => {
  await submit()
}

const handlePasswordSubmit = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordSubmitting.value = true

    await UserManageService.resetPassword(currentUser.value!._id, {
      newPassword: passwordForm.newPassword
    })

    ElMessage.success('密码重置成功')
    passwordDialogVisible.value = false
  } catch (error) {
    ElMessage.error('密码重置失败')
  } finally {
    passwordSubmitting.value = false
  }
}

const handleSearch = () => {
  search(searchForm)
}

const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    role: '',
    status: ''
  })
  search({})
}

// 工具函数
const getRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    superadmin: 'danger',
    admin: 'warning',
    user: 'success'
  }
  return typeMap[role] || 'info'
}

const getRoleDisplayName = (role: string) => {
  const nameMap: Record<string, string> = {
    superadmin: '超级管理员',
    admin: '管理员',
    user: '普通用户'
  }
  return nameMap[role] || role
}

const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.user-management {
  padding: 20px;

  .search-bar {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 0;
        margin-right: 16px;
      }
    }
  }

  .action-bar {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }

  .el-table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .role-tag {
      margin-right: 4px;
      margin-bottom: 2px;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .user-management {
    padding: 10px;

    .search-bar {
      padding: 15px;

      .search-form {
        .el-form-item {
          margin-right: 0;
          margin-bottom: 15px;
          width: 100%;

          :deep(.el-form-item__content) {
            width: 100%;
          }
        }
      }
    }

    .action-bar {
      .el-button {
        flex: 1;
        min-width: 120px;
      }
    }

    .el-table {
      font-size: 14px;

      .el-table__cell {
        padding: 8px 4px;
      }
    }

    .pagination-wrapper {
      padding: 15px;

      :deep(.el-pagination) {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }
}
</style>
