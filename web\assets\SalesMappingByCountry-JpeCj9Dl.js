import{_ as s}from"./index-Ds1tx7vI.js";import{_ as a}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as r,C as e,S as o,x as t}from"./vendor-9ydHGNSq.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";const c={class:"custom-card art-custom-card sales-mapping-country"},d={class:"custom-card-body"};const i=a({},[["render",function(a,i){const n=s;return e(),r("div",c,[i[0]||(i[0]=o("div",{class:"custom-card-header"},[o("span",{class:"title"},"全国销售分布")],-1)),o("div",d,[t(n,{data:[{value:30,name:"北京"},{value:25,name:"上海"},{value:45,name:"广州"}],color:["#4C87F3","#93F1B4","#8BD8FC"],radius:["46%","60%"]})])])}],["__scopeId","data-v-1c8c1e5b"]]);export{i as default};
