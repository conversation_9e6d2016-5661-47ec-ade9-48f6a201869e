import"./index-BOCMSBcY.js";/* empty css               *//* empty css               *//* empty css                *//* empty css                  */import{k as a,r as e,V as t,O as l,C as s,S as i,x as r,D as p,F as c,Z as v,B as d,aY as u,b7 as n,W as o,X as b,Y as x,u as f,be as m,av as _,a2 as y,a3 as S,a6 as h,aX as j}from"./vendor-9ydHGNSq.js";import{_ as g}from"./_plugin-vue_export-helper-BCo6x5W8.js";const k={class:"pricing-container"},P={class:"pricing-cards"},w={class:"card-header"},X={class:"description"},Y={class:"price"},q={class:"amount"},B={class:"features"},C={class:"card-footer"},D=g(a({__name:"index",setup(a){const g=e([{type:"single",title:"单次使用版",description:"适用于单个最终产品，最终用户无需付费。",price:349,isPopular:!1,features:[{text:"完整源代码",available:!0},{text:"技术文档",available:!0},{text:"SaaS应用授权",available:!1},{text:"单个项目使用",available:!0},{text:"一年技术支持",available:!0},{text:"一年免费更新",available:!0}]},{type:"multiple",title:"多次使用版",description:"适用于无限个最终产品，最终用户无需付费。",price:629,isPopular:!1,features:[{text:"完整源代码",available:!0},{text:"技术文档",available:!0},{text:"SaaS应用授权",available:!1},{text:"无限项目使用",available:!0},{text:"一年技术支持",available:!0},{text:"一年免费更新",available:!0}]},{type:"extended",title:"扩展授权版",description:"适用于单个最终产品，最终用户需要付费。",price:2099,isPopular:!1,features:[{text:"完整源代码",available:!0},{text:"技术文档",available:!0},{text:"SaaS应用授权",available:!0},{text:"单个项目使用",available:!0},{text:"一年技术支持",available:!0},{text:"一年免费更新",available:!0}]},{type:"unlimited",title:"无限授权版",description:"适用于无限个最终产品，最终用户需要付费。",price:3499,isPopular:!1,features:[{text:"完整源代码",available:!0},{text:"技术文档",available:!0},{text:"SaaS应用授权",available:!0},{text:"无限项目使用",available:!0},{text:"一年技术支持",available:!0},{text:"一年免费更新",available:!0}]}]);return(a,e)=>{const D=x,F=S,I=n,O=u,V=j,W=t("ripple");return s(),l("div",k,[e[2]||(e[2]=i("div",{class:"pricing-header"},[i("h1",{class:"title"},"超过 53,476 位信赖的开发者"),i("h2",{class:"subtitle"},"以及众多科技巨头的选择")],-1)),i("div",P,[r(V,{gutter:20,justify:"center"},{default:p((()=>[(s(!0),l(c,null,v(g.value,(a=>(s(),d(O,{key:a.type,xs:24,sm:12,md:6},{default:p((()=>[r(I,{class:o(["pricing-card",{popular:a.isPopular}]),shadow:"never"},{default:p((()=>[i("div",w,[i("h3",null,b(a.title),1),i("p",X,b(a.description),1),i("div",Y,[i("span",q,"¥"+b(a.price),1),e[0]||(e[0]=i("span",{class:"period"},"/一次性付款",-1))])]),i("div",B,[(s(!0),l(c,null,v(a.features,((a,e)=>(s(),l("div",{key:e,class:"feature-item"},[r(D,{class:o(a.available?"available":"unavailable")},{default:p((()=>[a.available?(s(),d(f(m),{key:0})):(s(),d(f(_),{key:1}))])),_:2},1032,["class"]),i("span",null,b(a.text),1)])))),128))]),i("div",C,[y((s(),d(F,{type:"primary",class:"purchase-btn"},{default:p((()=>e[1]||(e[1]=[h("立即购买")]))),_:1,__:[1]})),[[W]])])])),_:2},1032,["class"])])),_:2},1024)))),128))])),_:1})])])}}}),[["__scopeId","data-v-62a0d536"]]);export{D as default};
