var e=(e,n,t)=>new Promise(((o,r)=>{var l=e=>{try{i(t.next(e))}catch(n){r(n)}},a=e=>{try{i(t.throw(e))}catch(n){r(n)}},i=e=>e.done?o(e.value):Promise.resolve(e.value).then(l,a);i((t=t.apply(e,n)).next())}));import{e as n,f as t}from"./index-CT2bh8-V.js";import{r as o,d as r,p as l}from"./vendor-9ydHGNSq.js";function a(a={}){const{autoReconnect:i=!0,maxRetries:c=3,retryInterval:u=5e3,onReconnect:v,onOffline:s,checkUrl:f="https://www.google.com/favicon.ico"}=a,w=n(),d=t(),h=o(0),y=o(!1),m=o(null),p=o(null),g=()=>{i&&"function"==typeof v&&v(),k()},R=()=>{"function"==typeof s&&s(),i&&x()},x=()=>{null===p.value&&(h.value=0,y.value=!0,p.value=window.setInterval((()=>{w.value?k():(h.value++,E().then((e=>{e?("function"==typeof v&&v(),k()):h.value>=c&&k()})).catch((()=>{h.value>=c&&k()})))}),u))},k=()=>{null!==p.value&&(clearInterval(p.value),p.value=null),y.value=!1,h.value=0},E=()=>e(this,null,(function*(){try{yield fetch("https://www.google.com/favicon.ico?"+Date.now(),{method:"HEAD",mode:"no-cors",cache:"no-store"});return!0}catch(e){return!1}})),L=()=>e(this,null,(function*(){const e=yield E();return m.value=new Date,e!==w.value&&(e&&"function"==typeof v&&v(),e||"function"!=typeof s||s()),e}));return r((()=>{window.addEventListener("online",g),window.addEventListener("offline",R),L()})),l((()=>{window.removeEventListener("online",g),window.removeEventListener("offline",R),k()})),{isOnline:w,isRetrying:y,retryCount:h,maxRetries:c,lastCheckTime:m,network:d,checkNetworkConnection:L,reconnect:()=>{"function"==typeof v&&v()},startRetrying:x,stopRetrying:k}}export{a as u};
