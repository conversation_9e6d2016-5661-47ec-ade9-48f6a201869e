import{k as a,aa as e,O as s,C as t,bp as r,S as c,F as p,Z as d,X as n,u as l}from"./vendor-9ydHGNSq.js";import{_ as u}from"./_plugin-vue_export-helper-BCo6x5W8.js";const i={class:"card art-custom-card"},f={class:"list"},v={class:"user"},m={class:"type"},o={class:"target"},y=u(a({__name:"Dynamic",setup(a){const u=e([{username:"中小鱼",type:"关注了",target:"誶誶淰"},{username:"何小荷",type:"发表文章",target:"Vue3 + Typescript + Vite 项目实战笔记"},{username:"誶誶淰",type:"提出问题",target:"主题可以配置吗"},{username:"发呆草",type:"兑换了物品",target:"《奇特的一生》"},{username:"甜筒",type:"关闭了问题",target:"发呆草"},{username:"冷月呆呆",type:"兑换了物品",target:"《高效人士的七个习惯》"}]);return(a,e)=>(t(),s("div",i,[e[0]||(e[0]=r('<div class="card-header" data-v-6ef00fc6><div class="title" data-v-6ef00fc6><h4 class="box-title" data-v-6ef00fc6>动态</h4><p class="subtitle" data-v-6ef00fc6>新增<span class="text-success" data-v-6ef00fc6>+6</span></p></div></div>',1)),c("div",f,[(t(!0),s(p,null,d(l(u),((a,e)=>(t(),s("div",{key:e},[c("span",v,n(a.username),1),c("span",m,n(a.type),1),c("span",o,n(a.target),1)])))),128))])]))}}),[["__scopeId","data-v-6ef00fc6"]]);export{y as default};
