import"./index-BOCMSBcY.js";/* empty css                  */import{_ as e}from"./ArtTable-ClHSxhXb.js";/* empty css                        *//* empty css                    *//* empty css                  *//* empty css               *//* empty css                */import{k as a,r as l,O as t,C as o,x as s,D as r,b7 as d,u as n,i as m,F as p,Z as u,X as i,S as c,aN as _,aX as f,aJ as h,a3 as j,a6 as g}from"./vendor-9ydHGNSq.js";import{l as b}from"./vue-draggable-plus-KL8EvIWr.js";import{_ as v}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                  *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-CT2bh8-V.js";const V={class:"page-content"},x=v(a({__name:"index",setup(a){const v=l([{name:"孙悟空",role:"斗战胜佛"},{name:"猪八戒",role:"净坛使者"},{name:"沙僧",role:"金身罗汉"},{name:"唐僧",role:"旃檀功德佛"}]);return(a,l)=>{const x=d,w=f,y=h,k=e,U=j;return o(),t("div",V,[s(w,null,{default:r((()=>[s(x,{shadow:"never",style:{width:"300px","margin-right":"20px"}},{header:r((()=>l[4]||(l[4]=[c("span",{class:"card-header"},"基础示例",-1)]))),default:r((()=>[s(n(b),{ref:"el",modelValue:n(v),"onUpdate:modelValue":l[0]||(l[0]=e=>m(v)?v.value=e:null)},{default:r((()=>[(o(!0),t(p,null,u(n(v),(e=>(o(),t("div",{class:"demo1-item",key:e.name},i(e.name),1)))),128))])),_:1},8,["modelValue"])])),_:1}),s(x,{shadow:"never",style:{width:"300px"}},{header:r((()=>l[5]||(l[5]=[c("span",{class:"card-header"},"过渡动画",-1)]))),default:r((()=>[s(n(b),{modelValue:n(v),"onUpdate:modelValue":l[1]||(l[1]=e=>m(v)?v.value=e:null),target:".sort-target",scroll:!0},{default:r((()=>[s(_,{type:"transition",tag:"ul",name:"fade",class:"sort-target"},{default:r((()=>[(o(!0),t(p,null,u(n(v),(e=>(o(),t("li",{key:e.name,class:"demo1-item"},i(e.name),1)))),128))])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1}),s(x,{shadow:"never"},{header:r((()=>l[6]||(l[6]=[c("span",{class:"card-header"},"表格拖拽排序",-1)]))),default:r((()=>[s(n(b),{target:"tbody",modelValue:n(v),"onUpdate:modelValue":l[2]||(l[2]=e=>m(v)?v.value=e:null),animation:150},{default:r((()=>[s(k,{data:n(v),pagination:!1},{default:r((()=>[s(y,{label:"姓名",prop:"name"}),s(y,{label:"角色",prop:"role"})])),_:1},8,["data"])])),_:1},8,["modelValue"])])),_:1}),s(x,{shadow:"never"},{header:r((()=>l[7]||(l[7]=[c("span",{class:"card-header"},"指定元素拖拽排序",-1)]))),default:r((()=>[s(n(b),{target:"tbody",handle:".handle",modelValue:n(v),"onUpdate:modelValue":l[3]||(l[3]=e=>m(v)?v.value=e:null),animation:150},{default:r((()=>[s(k,{data:n(v),pagination:!1},{default:r((()=>[s(y,{label:"姓名",prop:"name"}),s(y,{label:"角色",prop:"role"}),s(y,{label:"操作",width:"100"},{default:r((()=>[s(U,{size:"default",class:"handle"},{default:r((()=>l[8]||(l[8]=[g(" 移动 ")]))),_:1,__:[8]})])),_:1})])),_:1},8,["data"])])),_:1},8,["modelValue"])])),_:1})])}}}),[["__scopeId","data-v-e789137e"]]);export{x as default};
