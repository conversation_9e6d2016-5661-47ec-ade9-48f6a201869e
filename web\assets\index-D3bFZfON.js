var a=Object.defineProperty,e=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,r=(e,t,o)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,d=(a,e)=>{for(var t in e||(e={}))s.call(e,t)&&r(a,t,e[t]);if(o)for(var t of o(e))l.call(e,t)&&r(a,t,e[t]);return a},i=(a,o)=>e(a,t(o));import{O as n}from"./index-BOCMSBcY.js";/* empty css               */import{_ as c}from"./index.vue_vue_type_script_setup_true_lang-BYz43fXJ.js";/* empty css                   */import{_ as u}from"./index-DIaAr_TV.js";import{u as p,a as m}from"./useChart-DM-2b2dH.js";import{k as h,a2 as g,aG as v,O as y,C as f,Q as x,B as b,R as w,u as A,c as L,r as _,S,x as k,D as B,aY as D,aX as C}from"./vendor-9ydHGNSq.js";import{_ as j}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{_ as E}from"./index-Ds1tx7vI.js";import{L as O}from"./index-DEP0vMzR.js";import{_ as T}from"./index-CAIJlOgO.js";/* empty css               */import{_ as Z}from"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";const P=j(h(i(d({},{name:"ArtKLineChart"}),{__name:"index",props:{data:{default:()=>[]},showDataZoom:{type:Boolean,default:!1},dataZoomStart:{default:0},dataZoomEnd:{default:100},height:{default:p().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>p().colors}},setup(a){const e=a,{chartRef:t,getAxisLineStyle:o,getAxisLabelStyle:s,getAxisTickStyle:l,getSplitLineStyle:r,getAnimationConfig:i,getTooltipStyle:n,isEmpty:c}=m({props:e,checkEmpty:()=>{var a;return!(null==(a=e.data)?void 0:a.length)||e.data.every((a=>0===a.open&&0===a.close&&0===a.high&&0===a.low))},watchSources:[()=>e.data,()=>e.colors,()=>e.showDataZoom,()=>e.dataZoomStart,()=>e.dataZoomEnd],generateOptions:()=>{const{upColor:a,downColor:t}={upColor:(null==(c=e.colors)?void 0:c[0])||"#4C87F3",downColor:(null==(u=e.colors)?void 0:u[1])||"#8BD8FC"};var c,u;return{grid:{top:20,right:20,bottom:e.showDataZoom?80:20,left:20,containLabel:!0},tooltip:n("axis",{axisPointer:{type:"cross"},formatter:a=>{const e=a[0],t=e.data;return`\n              <div style="padding: 5px;">\n                <div><strong>时间：</strong>${e.name}</div>\n                <div><strong>开盘：</strong>${t[0]}</div>\n                <div><strong>收盘：</strong>${t[1]}</div>\n                <div><strong>最低：</strong>${t[2]}</div>\n                <div><strong>最高：</strong>${t[3]}</div>\n              </div>\n            `}}),xAxis:{type:"category",data:e.data.map((a=>a.time)),axisTick:l(),axisLine:o(!0),axisLabel:s(!0)},yAxis:{type:"value",scale:!0,axisLabel:s(!0),axisLine:o(!0),splitLine:r(!0)},series:[d({type:"candlestick",data:e.data.map((a=>[a.open,a.close,a.low,a.high])),itemStyle:{color:a,color0:t,borderColor:a,borderColor0:t,borderWidth:1},emphasis:{itemStyle:{borderWidth:2,shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.3)"}}},i())],dataZoom:e.showDataZoom?[{type:"inside",start:e.dataZoomStart,end:e.dataZoomEnd},{show:!0,type:"slider",top:"90%",start:e.dataZoomStart,end:e.dataZoomEnd}]:void 0}}});return(a,o)=>{const s=u,l=v;return g((f(),y("div",{ref_key:"chartRef",ref:t,style:x({height:e.height})},[A(c)?(f(),b(s,{key:0})):w("",!0)],4)),[[l,e.loading]])}}})),[["__scopeId","data-v-3df68787"]]),R=j(h(i(d({},{name:"ArtRadarChart"}),{__name:"index",props:{indicator:{default:()=>[]},data:{default:()=>[]},height:{default:p().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>p().colors},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"bottom"}},setup(a){const e=a,{chartRef:t,isDark:o,getAnimationConfig:s,getTooltipStyle:l,isEmpty:r}=m({props:e,checkEmpty:()=>{var a;return!(null==(a=e.data)?void 0:a.length)||e.data.every((a=>a.value.every((a=>0===a))))},watchSources:[()=>e.data,()=>e.indicator,()=>e.colors],generateOptions:()=>({tooltip:e.showTooltip?l("item"):void 0,radar:{indicator:e.indicator,center:["50%","50%"],radius:"70%",axisName:{color:o.value?"#ccc":"#666",fontSize:12},splitLine:{lineStyle:{color:o.value?"#444":"#e6e6e6"}},axisLine:{lineStyle:{color:o.value?"#444":"#e6e6e6"}},splitArea:{show:!0,areaStyle:{color:o.value?["rgba(255, 255, 255, 0.02)","rgba(255, 255, 255, 0.05)"]:["rgba(0, 0, 0, 0.02)","rgba(0, 0, 0, 0.05)"]}}},series:[d({type:"radar",data:e.data.map(((a,t)=>({name:a.name,value:a.value,symbolSize:4,lineStyle:{width:2,color:e.colors[t%e.colors.length]},itemStyle:{color:e.colors[t%e.colors.length]},areaStyle:{color:e.colors[t%e.colors.length],opacity:.1},emphasis:{areaStyle:{opacity:.25},lineStyle:{width:3}}})))},s(200,1800))]})});return(a,o)=>{const s=u,l=v;return g((f(),y("div",{ref_key:"chartRef",ref:t,style:x({height:e.height})},[A(r)?(f(),b(s,{key:0})):w("",!0)],4)),[[l,e.loading]])}}})),[["__scopeId","data-v-f5db2bd8"]]),W=j(h(i(d({},{name:"ArtScatterChart"}),{__name:"index",props:{data:{default:()=>[{value:[0,0]},{value:[0,0]}]},symbolSize:{default:14},height:{default:p().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>p().colors},showAxisLabel:{type:Boolean,default:!0},showAxisLine:{type:Boolean,default:!0},showSplitLine:{type:Boolean,default:!0},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"bottom"}},setup(a){const e=a,{chartRef:t,isDark:o,getAxisLineStyle:s,getAxisLabelStyle:l,getAxisTickStyle:r,getSplitLineStyle:i,getAnimationConfig:c,getTooltipStyle:p,isEmpty:h}=m({props:e,checkEmpty:()=>{var a;return!(null==(a=e.data)?void 0:a.length)||e.data.every((a=>a.value.every((a=>0===a))))},watchSources:[()=>e.data,()=>e.colors,()=>e.symbolSize],generateOptions:()=>{const a=e.colors[0]||n("--el-color-primary");return{grid:{top:20,right:20,bottom:20,left:20,containLabel:!0},tooltip:e.showTooltip?p("item",{formatter:a=>{const[e,t]=a.value;return`X: ${e}<br/>Y: ${t}`}}):void 0,xAxis:{type:"value",axisLabel:l(e.showAxisLabel),axisLine:s(e.showAxisLine),axisTick:r(),splitLine:i(e.showSplitLine)},yAxis:{type:"value",axisLabel:l(e.showAxisLabel),axisLine:s(e.showAxisLine),axisTick:r(),splitLine:i(e.showSplitLine)},series:[d({type:"scatter",data:e.data,symbolSize:e.symbolSize,itemStyle:{color:a,shadowBlur:6,shadowColor:o.value?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)",shadowOffsetY:2},emphasis:{itemStyle:{shadowBlur:12,shadowColor:o.value?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},scale:!0}},c())]}}});return(a,o)=>{const s=u,l=v;return g((f(),y("div",{ref_key:"chartRef",ref:t,class:"art-scatter-chart",style:x({height:e.height})},[A(h)?(f(),b(s,{key:0})):w("",!0)],4)),[[l,e.loading]])}}})),[["__scopeId","data-v-80ddf407"]]),z=j(h(i(d({},{name:"ArtHBarChart"}),{__name:"index",props:{data:{default:()=>[0,0,0,0,0,0,0]},xAxisData:{default:()=>[]},barWidth:{default:"36%"},stack:{type:Boolean,default:!1},borderRadius:{},height:{default:p().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>p().colors},showAxisLabel:{type:Boolean,default:!0},showAxisLine:{type:Boolean,default:!0},showSplitLine:{type:Boolean,default:!0},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"bottom"}},setup(a){const e=a,t=L((()=>Array.isArray(e.data)&&e.data.length>0&&"object"==typeof e.data[0]&&"name"in e.data[0])),o=(a,t)=>a||(void 0!==t?e.colors[t%e.colors.length]:new O(0,0,1,0,[{offset:0,color:n("--el-color-primary")},{offset:1,color:n("--el-color-primary-light-4")}])),s=a=>new O(0,0,1,0,[{offset:0,color:a},{offset:1,color:a}]),l=a=>{const t=_();return d({name:a.name,data:a.data,type:"bar",stack:a.stack,itemStyle:(o=a.color,{borderRadius:4,color:"string"==typeof o?s(o):o}),barWidth:a.barWidth||e.barWidth},t);var o},{chartRef:r,getAxisLineStyle:i,getAxisLabelStyle:c,getAxisTickStyle:p,getSplitLineStyle:h,getAnimationConfig:_,getTooltipStyle:S,getLegendStyle:k,getGridWithLegend:B,isEmpty:D}=m({props:e,checkEmpty:()=>{if(Array.isArray(e.data)&&"number"==typeof e.data[0]){const a=e.data;return!a.length||a.every((a=>0===a))}if(Array.isArray(e.data)&&"object"==typeof e.data[0]){const a=e.data;return!a.length||a.every((a=>{var e;return!(null==(e=a.data)?void 0:e.length)||a.data.every((a=>0===a))}))}return!0},watchSources:[()=>e.data,()=>e.xAxisData,()=>e.colors],generateOptions:()=>{const a={grid:B(e.showLegend&&t.value,e.legendPosition,{top:15,right:0,left:0}),tooltip:e.showTooltip?S():void 0,xAxis:{type:"value",axisTick:p(),axisLine:i(e.showAxisLine),axisLabel:c(e.showAxisLabel),splitLine:h(e.showSplitLine)},yAxis:{type:"category",data:e.xAxisData,axisTick:p(),axisLabel:c(e.showAxisLabel),axisLine:i(e.showAxisLine)}};if(e.showLegend&&t.value&&(a.legend=k(e.legendPosition)),t.value){const t=e.data;a.series=t.map(((a,t)=>{const s=o(e.colors[t],t);return l({name:a.name,data:a.data,color:s,barWidth:a.barWidth,stack:e.stack?a.stack||"total":void 0})}))}else{const t=e.data,s=o();a.series=[l({data:t,color:s})]}return a}});return(a,t)=>{const o=u,s=v;return g((f(),y("div",{ref_key:"chartRef",ref:r,style:x({height:e.height})},[A(D)?(f(),b(o,{key:0})):w("",!0)],4)),[[s,e.loading]])}}})),[["__scopeId","data-v-960790bc"]]),$={class:"charts"},I={class:"card art-custom-card"},H={class:"card art-custom-card"},F={class:"card art-custom-card"},N={class:"card art-custom-card"},Q={class:"card art-custom-card"},Y={class:"card art-custom-card"},G={class:"card art-custom-card"},X={class:"card art-custom-card"},q={class:"card art-custom-card"},K={class:"card art-custom-card"},J={class:"card art-custom-card"},M={class:"card art-custom-card"},U={class:"card art-custom-card"},V={class:"card art-custom-card"},aa={class:"card art-custom-card"},ea={class:"card art-custom-card"},ta=j(h({__name:"index",setup(a){const e=[{name:"销售额",data:[120,132,101,134,90,130],areaStyle:{startOpacity:.1,endOpacity:0}},{name:"利润",data:[80,92,71,94,60,80],areaStyle:{startOpacity:.1,endOpacity:0}}],t=_([120,200,150,80,70,110,130]),o=_(["周一","周二","周三","周四","周五","周六","周日"]),s=_([{name:"销售额",data:[120,200,150,80,70,110,130]},{name:"利润",data:[20,50,30,15,10,25,35]}]),l=_([{name:"Q1",data:[20,25,30,35,40],stack:"total"},{name:"Q2",data:[30,35,40,45,50],stack:"total"}]),r=_(["产品A","产品B","产品C","产品D","产品E"]);return(a,d)=>{const i=Z,n=D,u=T,p=z,m=E,h=W,g=R,v=P,x=c,b=C;return f(),y("div",$,[d[16]||(d[16]=S("h1",{class:"page-title"},"图表",-1)),k(b,{gutter:20},{default:B((()=>[k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",I,[d[0]||(d[0]=S("div",{class:"card-header"},[S("span",null,"柱状图（单数据）")],-1)),k(i,{data:t.value,xAxisData:o.value,showLegend:!0,legendPosition:"right"},null,8,["data","xAxisData"])])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",H,[d[1]||(d[1]=S("div",{class:"card-header"},[S("span",null,"柱状图（多组数据）")],-1)),k(i,{data:s.value,xAxisData:o.value,showLegend:!0,barWidth:"26%"},null,8,["data","xAxisData"])])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",F,[d[2]||(d[2]=S("div",{class:"card-header"},[S("span",null,"柱状图（堆叠）")],-1)),k(i,{data:l.value,xAxisData:r.value,showLegend:!0,stack:!0,barWidth:"26%"},null,8,["data","xAxisData"])])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",N,[d[3]||(d[3]=S("div",{class:"card-header"},[S("span",null,"折线图")],-1)),k(u,{data:[58,15,82,35,120,62,45],xAxisData:["一月","二月","三月","四月","五月","六月","七月"],symbol:"none",symbolSize:7})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",Q,[d[4]||(d[4]=S("div",{class:"card-header"},[S("span",null,"折线图（渐变背景）")],-1)),k(u,{data:[28,45,82,35,100,32,55],xAxisData:["一月","二月","三月","四月","五月","六月","七月"],showAreaColor:!0})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",Y,[d[5]||(d[5]=S("div",{class:"card-header"},[S("span",null,"折线图（多组数据）")],-1)),k(u,{data:e,xAxisData:["1月","2月","3月","4月","5月","6月"],showLegend:!0})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",G,[d[6]||(d[6]=S("div",{class:"card-header"},[S("span",null,"柱状图（水平）")],-1)),k(p,{data:[50,80,120,90,60],xAxisData:["产品A","产品B","产品C","产品D","产品E"]})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",X,[d[7]||(d[7]=S("div",{class:"card-header"},[S("span",null,"柱状图（水平）")],-1)),k(p,{data:[{name:"系列1",data:[10,20,30]},{name:"系列2",data:[15,25,35]}],xAxisData:["类目1","类目2","类目3"],showLegend:!0,barWidth:"30%"})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",q,[d[8]||(d[8]=S("div",{class:"card-header"},[S("span",null,"柱状图（水平堆叠）")],-1)),k(p,{data:[{name:"系列1",data:[10,20,30]},{name:"系列2",data:[15,25,35]}],xAxisData:["类目1","类目2","类目3"],showLegend:!0,stack:!0,barWidth:"30%"})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",K,[d[9]||(d[9]=S("div",{class:"card-header"},[S("span",null,"环形图")],-1)),k(m,{data:[{value:35,name:"分类A"},{value:28,name:"分类B"},{value:42,name:"分类C"}],radius:["54%","70%"],legendPosition:"bottom"})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",J,[d[10]||(d[10]=S("div",{class:"card-header"},[S("span",null,"环形图")],-1)),k(m,{data:[{value:35,name:"分类A"},{value:28,name:"分类B"},{value:42,name:"分类C"},{value:32,name:"分类D"},{value:26,name:"分类E"},{value:37,name:"分类F"}],radius:["54%","70%"],borderRadius:0,showLegend:!0,legendPosition:"bottom",centerText:"¥300,458"})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",M,[d[11]||(d[11]=S("div",{class:"card-header"},[S("span",null,"饼图")],-1)),k(m,{data:[{value:30,name:"分类A"},{value:25,name:"分类B"},{value:45,name:"分类C"}],radius:["0%","70%"],showLegend:!0,legendPosition:"right"})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",U,[d[12]||(d[12]=S("div",{class:"card-header"},[S("span",null,"散点图")],-1)),k(h,{data:[{value:[1,3]},{value:[2,4]},{value:[3,5]},{value:[4,6]},{value:[5,7]},{value:[6,8]},{value:[7,7]},{value:[8,9]},{value:[9,8]},{value:[10,6]},{value:[11,7]},{value:[12,8]}]})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",V,[d[13]||(d[13]=S("div",{class:"card-header"},[S("span",null,"雷达图")],-1)),k(g,{indicator:[{name:"销售",max:100},{name:"管理",max:100},{name:"技术",max:100},{name:"客服",max:100},{name:"开发",max:100}],data:[{name:"预算分配",value:[80,70,90,85,75]},{name:"实际开销",value:[70,75,85,80,70]}]})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",aa,[d[14]||(d[14]=S("div",{class:"card-header"},[S("span",null,"k线图")],-1)),k(v,{data:[{time:"2024-01-01",open:20,close:23,high:25,low:18},{time:"2024-01-02",open:23,close:21,high:24,low:20},{time:"2024-01-03",open:21,close:25,high:26,low:21}],showDataZoom:!1,dataZoomStart:0,dataZoomEnd:100})])])),_:1}),k(n,{xs:24,md:12,lg:8},{default:B((()=>[S("div",ea,[d[15]||(d[15]=S("div",{class:"card-header"},[S("span",null,"双向堆叠柱状图")],-1)),k(x,{positiveData:[50,28,80,65,68,70,60,55],negativeData:[50,28,40,45,38,50,42,48],xAxisData:["0-4岁","5-14岁","15-24岁","25-34岁","35-44岁","45-54岁","55-64岁","65岁以上"],positiveName:"男性年龄分布",negativeName:"女性年龄分布"})])])),_:1})])),_:1})])}}}),[["__scopeId","data-v-5079e56e"]]);export{ta as default};
