import { AxiosError } from 'axios'
import { ElMessage } from 'element-plus'
import { ApiStatus } from './status'
import { $t } from '@/locales'

// 错误响应接口
export interface ErrorResponse {
  code: number
  msg?: string
  message?: string
  data?: unknown
}

// 错误日志数据接口
export interface ErrorLogData {
  code: number
  message: string
  data?: unknown
  timestamp: string
  url?: string
  method?: string
  stack?: string
}

/**
 * 自定义HTTP错误类
 */
export class HttpError extends Error {
  readonly code: number
  readonly meta: Record<string, any>
  readonly timestamp: number

  constructor(message: string, code: number, meta: Record<string, any> = {}) {
    super(message)
    this.name = 'HttpError'
    this.code = code
    this.meta = meta
    this.timestamp = Date.now()
  }

  public toLogData(): ErrorLogData {
    return {
      code: this.code,
      message: this.message,
      data: this.meta.data,
      timestamp: new Date(this.timestamp).toISOString(),
      url: this.meta.url,
      method: this.meta.method,
      stack: this.stack
    }
  }
}

/**
 * 显示错误提示
 * @param error 错误对象
 * @param showErrorMessage 是否显示错误提示
 */
export function showError(error: HttpError, showErrorMessage = true): void {
  if (!showErrorMessage) return

  // 特殊错误类型处理
  if (error.code === ApiStatus.networkError) {
    ElMessage({
      message: error.message || $t('httpMsg.networkError'),
      type: 'error',
      duration: 3000
    })
    return
  }

  // 请求频率限制错误
  if (error.code === ApiStatus.tooManyRequests) {
    // 如果是节流错误，不显示弹窗，因为throttle模块已经显示了
    return
  }

  ElMessage({
    message: error.message || $t('httpMsg.requestFailed'),
    type: 'error',
    duration: 3000
  })
}

/**
 * 获取HTTP状态码对应的错误信息
 * @param status HTTP状态码
 * @returns 错误信息
 */
export function getErrorMessage(status: number): string {
  switch (status) {
    case ApiStatus.error:
      return $t('httpMsg.requestError')
    case ApiStatus.unauthorized:
      return $t('httpMsg.unauthorized')
    case ApiStatus.forbidden:
      return $t('httpMsg.forbidden')
    case ApiStatus.notFound:
      return $t('httpMsg.notFound')
    case ApiStatus.timeout:
      return $t('httpMsg.requestTimeout')
    case ApiStatus.tooManyRequests:
      return $t('httpMsg.tooManyRequests')
    case ApiStatus.internalServerError:
      return $t('httpMsg.serverError')
    case ApiStatus.serviceUnavailable:
      return $t('httpMsg.serviceUnavailable')
    case ApiStatus.networkError:
      return $t('httpMsg.networkOffline')
    default:
      return $t('httpMsg.requestFailed')
  }
}

/**
 * 处理错误
 * @param error 错误对象
 * @returns 错误对象
 */
export function handleError(error: AxiosError<ErrorResponse>): never {
  // 处理取消的请求
  if (error.code === 'ERR_CANCELED') {
    // Request cancelled
    throw new HttpError($t('httpMsg.requestCancelled'), ApiStatus.error)
  }

  // 处理网络错误
  if (!navigator.onLine || error.message === 'Network Error') {
    throw new HttpError($t('httpMsg.networkOffline'), ApiStatus.networkError)
  }

  // 处理请求超时
  if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
    throw new HttpError($t('httpMsg.requestTimeout'), ApiStatus.timeout)
  }

  const statusCode = error.response?.status
  const errorData = error.response?.data
  const requestConfig = error.config

  // 处理网络错误
  if (!error.response) {
    throw new HttpError($t('httpMsg.networkError'), ApiStatus.error, {
      url: requestConfig?.url,
      method: requestConfig?.method?.toUpperCase()
    })
  }

  // 优先使用服务器返回的错误消息
  if (errorData) {
    // 先尝试使用message字段，然后是msg字段
    const errorMessage = errorData.message || errorData.msg || null
    if (errorMessage) {
      throw new HttpError(errorMessage, statusCode || ApiStatus.error, {
        data: errorData,
        url: requestConfig?.url,
        method: requestConfig?.method?.toUpperCase()
      })
    }
  }

  // 如果没有服务器错误消息，使用HTTP状态码对应的消息
  const message = statusCode
    ? getErrorMessage(statusCode)
    : error.message || $t('httpMsg.requestFailed')
  throw new HttpError(message, statusCode || ApiStatus.error, {
    data: error.response.data,
    url: requestConfig?.url,
    method: requestConfig?.method?.toUpperCase()
  })
}

/**
 * 判断是否为 HttpError 类型
 * @param error 错误对象
 * @returns 是否为 HttpError 类型
 */
export const isHttpError = (error: unknown): error is HttpError => {
  return error instanceof HttpError
}
