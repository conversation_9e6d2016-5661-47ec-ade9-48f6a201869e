var e=Object.defineProperty,t=Object.defineProperties,l=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,s=(t,l,a)=>l in t?e(t,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[l]=a;import{A as r}from"./index-BOCMSBcY.js";/* empty css                  *//* empty css                   */import{k as c,r as m,A as u,O as i,C as d,S as p,R as v,Q as h,X as y,F as f,Z as b,B as g,a2 as w,bk as F,a1 as _,aa as k,x as D,a6 as x,u as O,D as j,i as C,a7 as S}from"./vendor-9ydHGNSq.js";import{_ as I}from"./_plugin-vue_export-helper-BCo6x5W8.js";const R={class:"comment-item"},E={class:"comment-main"},V={class:"comment-header"},A={class:"name dark-text"},M={class:"content"},q={class:"comment-info"},P={class:"date"},U={key:0,class:"comment-replies"},N=I(c({__name:"CommentItem",props:{comment:{},showReplyForm:{}},emits:["toggle-reply","add-reply"],setup(e,{emit:t}){const l=e,a=t,o=m(""),n=m(""),s=e=>{a("toggle-reply",e)},c=(e,t,l)=>{a("add-reply",e,t,l),o.value="",n.value=""},_=()=>{a("add-reply",l.comment.id,o.value,n.value)};let k=null;const D=()=>{let e;do{const t=Math.floor(Math.random()*r.systemMainColor.length);e=r.systemMainColor[t]}while(e===k);return k=e,e};return(e,t)=>{const l=u("CommentItem",!0);return d(),i("li",R,[p("div",E,[p("div",V,[p("div",{class:"avatar",style:h({background:D()})},y(e.comment.author.substring(0,1)),5),p("strong",A,y(e.comment.author),1)]),p("span",M,y(e.comment.content),1),p("div",q,[p("span",P,y((a=e.comment.timestamp,new Date(a).toLocaleString())),1),p("div",{class:"btn-text",onClick:t[0]||(t[0]=t=>s(e.comment.id))},"回复")])]),e.comment.replies.length>0?(d(),i("ul",U,[(d(!0),i(f,null,b(e.comment.replies,(t=>(d(),g(l,{key:t.id,comment:t,"show-reply-form":e.showReplyForm,onToggleReply:s,onAddReply:c},null,8,["comment","show-reply-form"])))),128))])):v("",!0),e.showReplyForm===e.comment.id?(d(),i("form",{key:1,onSubmit:_},[p("div",null,[w(p("input",{"onUpdate:modelValue":t[1]||(t[1]=e=>o.value=e),placeholder:"你的名称",required:""},null,512),[[F,o.value]]),w(p("textarea",{"onUpdate:modelValue":t[2]||(t[2]=e=>n.value=e),placeholder:"你的回复",required:""},null,512),[[F,n.value]]),t[3]||(t[3]=p("button",{class:"btn",type:"submit"},"发布",-1))])],32)):v("",!0)]);var a}}}),[["__scopeId","data-v-1b3dc437"]]),B={class:"comment-module"},T={class:"comment-header"},z=I(c({__name:"index",setup(e){const t=m([{id:1,author:"用户1",content:"这是一条示例评论",time:"2024-01-01 12:00:00"}]),l=m({author:"",content:""}),a=m(null),o=()=>{l.value.author&&l.value.content?(t.value.push({id:Date.now(),author:l.value.author,content:l.value.content,timestamp:(new Date).toISOString(),replies:[]}),l.value.author="",l.value.content=""):alert("请填写完整的评论信息")},n=(e,l,o)=>{const n=r(t.value,e);n&&l&&o?(n.replies.push({id:Date.now(),author:l,content:o,timestamp:(new Date).toISOString(),replies:[]}),a.value=null):alert("请填写完整的回复信息")},s=e=>{a.value=a.value===e?null:e},r=(e,t)=>{for(const l of e){if(l.id===t)return l;const e=r(l.replies,t);if(e)return e}};return(e,r)=>(d(),i("div",B,[p("form",{onSubmit:_(o,["prevent"])},[p("div",null,[w(p("input",{"onUpdate:modelValue":r[0]||(r[0]=e=>l.value.author=e),placeholder:"你的名称",required:""},null,512),[[F,l.value.author]]),w(p("textarea",{"onUpdate:modelValue":r[1]||(r[1]=e=>l.value.content=e),placeholder:"简单说两句...",required:""},null,512),[[F,l.value.content]]),r[2]||(r[2]=p("button",{class:"btn",type:"submit"},"发布",-1))])],32),p("ul",null,[p("div",T,"评论 "+y(t.value.length),1),(d(!0),i(f,null,b(t.value.slice().reverse(),(e=>(d(),g(N,{class:"comment-item",key:e.id,comment:e,"show-reply-form":a.value,onToggleReply:s,onAddReply:n},null,8,["comment","show-reply-form"])))),128))])]))}}),[["__scopeId","data-v-9f8896bc"]]),L={class:"page-content"},Q={class:"list"},X={class:"offset"},Z=["onClick"],G={class:"date"},H={class:"content"},J={class:"bottom"},K={class:"left"},W={class:"right"},Y={class:"drawer-default"},$={class:"date"},ee={class:"content"},te={class:"bottom"},le={class:"left"},ae={class:"right"},oe=c((ne=((e,t)=>{for(var l in t||(t={}))o.call(t,l)&&s(e,l,t[l]);if(a)for(var l of a(t))n.call(t,l)&&s(e,l,t[l]);return e})({},{name:"ArticleComment"}),t(ne,l({__name:"index",setup(e){const t=m([]),l=m(!1),a=k(["#D8F8FF","#FDDFD9","#FCE6F0","#D3F8F0","#FFEABC","#F5E1FF","#E1E6FE"]);let o=null;const n=()=>{let e;do{const t=Math.floor(Math.random()*a.length);e=a[t]}while(e===o);return o=e,e},s=m({id:1,date:"2024-9-3",content:"加油！学好Node 自己写个小Demo",collection:5,comment:8,userName:"匿名"});return(e,a)=>{const o=z,r=S;return d(),i("div",L,[a[7]||(a[7]=p("h1",{class:"title"},"留言墙",-1)),a[8]||(a[8]=p("p",{class:"desc"},"每一份留言都记录了您的想法，也为我们提供了珍贵的回忆",-1)),p("div",Q,[p("ul",X,[(d(!0),i(f,null,b(O(t),(e=>(d(),i("li",{class:"comment-box",key:e.id,style:h({background:n()}),onClick:t=>(e=>{l.value=!0,s.value=e})(e)},[p("p",G,y(e.date),1),p("p",H,y(e.content),1),p("div",J,[p("div",K,[p("span",null,[a[1]||(a[1]=p("i",{class:"iconfont-sys"},"",-1)),x(y(e.collection),1)]),p("span",null,[a[2]||(a[2]=p("i",{class:"iconfont-sys"},"",-1)),x(y(e.comment),1)])]),p("div",W,[p("span",null,y(e.userName),1)])])],12,Z)))),128))])]),D(r,{lDrawer:"",modelValue:O(l),"onUpdate:modelValue":a[0]||(a[0]=e=>C(l)?l.value=e:null),"lock-scroll":!1,size:360,"modal-class":"comment-modal"},{header:j((()=>a[3]||(a[3]=[p("h4",null,"详情",-1)]))),default:j((()=>[p("div",Y,[p("div",{class:"comment-box",style:h({background:n()})},[p("p",$,y(O(s).date),1),p("p",ee,y(O(s).content),1),p("div",te,[p("div",le,[p("span",null,[a[4]||(a[4]=p("i",{class:"iconfont-sys"},"",-1)),x(y(O(s).collection),1)]),p("span",null,[a[5]||(a[5]=p("i",{class:"iconfont-sys"},"",-1)),x(y(O(s).comment),1)])]),p("div",ae,[p("span",null,y(O(s).userName),1)])])],4),D(o)])])),footer:j((()=>a[6]||(a[6]=[p("div",null,null,-1)]))),_:1},8,["modelValue"])])}}}))));var ne;const se=I(oe,[["__scopeId","data-v-15b482f8"]]);export{se as default};
