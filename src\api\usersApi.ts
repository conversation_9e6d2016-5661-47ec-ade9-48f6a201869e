import request from '@/utils/http'

export class UserService {
  // 获取验证码
  static getCaptcha() {
    return request.get<Api.Http.BaseResponse<Api.Auth.CaptchaResponse>>({
      url: '/auth/captcha'
    })
  }

  // 登录
  static login(params: Api.Auth.LoginParams) {
    return request.post<Api.Http.BaseResponse<Api.Auth.LoginResponse>>({
      url: '/auth/login',
      data: params,
      showErrorMessage: true
    })
  }

  // 获取用户信息
  static getUserInfo() {
    return request.get<Api.Http.BaseResponse<Api.User.UserInfo>>({
      url: '/users/me'
    })
  }

  // 刷新令牌
  static refreshToken(refreshToken: string) {
    return request.post<
      Api.Http.BaseResponse<{ accessToken: string; tokenType: string; expires: number }>
    >({
      url: '/auth/refresh-token',
      params: { refreshToken }
    })
  }

  // 注销
  static logout() {
    return request.post<Api.Http.BaseResponse<void>>({
      url: '/auth/logout'
    })
  }

  // 获取用户列表
  static getUserList(params: Api.Common.PaginatingParams) {
    return request.get<Api.Http.BaseResponse<Api.User.UserListData>>({
      url: '/api/user/list',
      params
    })
  }

  // 修改密码
  static updatePassword(data: { oldPassword: string; newPassword: string }) {
    return request.put<Api.Http.BaseResponse<void>>({
      url: '/users/password',
      data,
      showErrorMessage: false
    })
  }
}
