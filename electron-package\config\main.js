const { app, BrowserWindow, <PERSON><PERSON>, ipc<PERSON>ain } = require('electron')
const path = require('path')

// 创建菜单
function createMenu(mainWindow) {
  const template = [
    {
      label: '应用',
      submenu: [
        {
          label: '强制刷新',
          accelerator: 'Ctrl+Shift+R',
          click: () => {
            mainWindow.webContents.reloadIgnoringCache()
          }
        },
        {
          label: '普通刷新',
          accelerator: 'Ctrl+R',
          click: () => {
            mainWindow.webContents.reload()
          }
        },
        { type: 'separator' },
        {
          label: '开发者工具',
          accelerator: 'F12',
          click: () => {
            mainWindow.webContents.toggleDevTools()
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: 'Alt+F4',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: '视图',
      submenu: [
        {
          label: '实际大小',
          accelerator: 'Ctrl+0',
          click: () => {
            mainWindow.webContents.setZoomLevel(0)
          }
        },
        {
          label: '放大',
          accelerator: 'Ctrl+Plus',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel()
            mainWindow.webContents.setZoomLevel(currentZoom + 1)
          }
        },
        {
          label: '缩小',
          accelerator: 'Ctrl+-',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel()
            mainWindow.webContents.setZoomLevel(currentZoom - 1)
          }
        },
        { type: 'separator' },
        {
          label: '全屏',
          accelerator: 'F11',
          click: () => {
            const isFullScreen = mainWindow.isFullScreen()
            mainWindow.setFullScreen(!isFullScreen)
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// 创建一个浏览器窗口
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true, // 允许在渲染进程中使用node api
      contextIsolation: false,  // 禁用上下文隔离，使得渲染进程和预加载脚本运行在同一个上下文中
      enableRemoteModule: true, // 启用了 remote 模块，使得渲染进程可以访问主进程的功能
      preload: path.join(__dirname, 'preload.js'), // 加载预加载脚本
    },
  })

  let isDebug = false;
  // 配置热更新
  if (isDebug) {
    const elePath = path.join(__dirname, './node_modules/electron')
    require('electron-reload')('./', {
      electron: require(elePath),
    })
    // 热更新监听窗口
    mainWindow.loadURL('http://localhost:3000')
    // 打开开发工具
    mainWindow.webContents.openDevTools()
  } else {
    // 生产环境中要加载文件，打包的版本
    // 加载 index.html
    mainWindow.loadFile(path.resolve(__dirname, './web/index.html'))
  }

  // 创建自定义菜单（包含刷新按钮）
  createMenu(mainWindow)

  // 设置IPC监听器
  ipcMain.on('toggle-devtools', () => {
    mainWindow.webContents.toggleDevTools()
  })
}

app.whenReady().then(() => {
  createWindow() // 当 Electron 初始化完成并且准备好创建浏览器窗口时，调用 createWindow 函数

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit()
})
