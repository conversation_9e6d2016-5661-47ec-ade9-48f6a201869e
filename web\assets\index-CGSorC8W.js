import{_ as e}from"./ArtTable-ClHSxhXb.js";import"./index-BOCMSBcY.js";/* empty css                        *//* empty css                    *//* empty css                  *//* empty css                  */import{_ as a,a as t}from"./ArtExcelImport-C9pARw1X.js";/* empty css                   */import{k as s,r,V as o,O as p,C as m,x as i,a2 as l,D as n,a6 as c,u,B as g,F as j,Z as y,aJ as d,a3 as x,E as _}from"./vendor-9ydHGNSq.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                  *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-CT2bh8-V.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                    */const f={class:"page-content"},E=s({__name:"index",setup(s){const E=e=>{const a=e.map((e=>({name:e["姓名"],age:Number(e["年龄"]),city:e["城市"]})));v.value=a},b=e=>{},v=r([{name:"李四",age:20,city:"上海"},{name:"张三",age:25,city:"北京"},{name:"王五",age:30,city:"广州"},{name:"赵六",age:35,city:"深圳"},{name:"孙七",age:28,city:"杭州"},{name:"周八",age:32,city:"成都"},{name:"吴九",age:27,city:"武汉"},{name:"郑十",age:40,city:"南京"},{name:"刘一",age:22,city:"重庆"},{name:"陈二",age:33,city:"西安"}]),k={name:"姓名",age:"年龄",city:"城市"},h=()=>{_.success("导出成功")},I=e=>{_.error(`导出失败: ${e.message}`)},C=()=>{v.value=[]};return(s,r)=>{const _=a,A=t,D=x,N=d,O=e,S=o("ripple");return m(),p("div",f,[i(_,{onImportSuccess:E,onImportError:b},{"import-text":n((()=>r[0]||(r[0]=[c(" 上传 Excel ")]))),_:1}),i(A,{style:{"margin-left":"10px"},data:u(v),filename:"用户数据",sheetName:"用户列表",type:"success",headers:k,onExportSuccess:h,onExportError:I},{default:n((()=>r[1]||(r[1]=[c(" 导出 Excel ")]))),_:1,__:[1]},8,["data"]),l((m(),g(D,{type:"danger",onClick:C},{default:n((()=>r[2]||(r[2]=[c("清除数据")]))),_:1,__:[2]})),[[S]]),i(O,{data:u(v),style:{"margin-top":"10px"}},{default:n((()=>[(m(!0),p(j,null,y(Object.keys(k),(e=>(m(),g(N,{key:e,prop:e,label:k[e]},null,8,["prop","label"])))),128))])),_:1},8,["data"])])}}});export{E as default};
