var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,n=(t,s,r)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[s]=r;import{k as l,O as c,C as i,S as p,X as u,P as d,W as f}from"./vendor-9ydHGNSq.js";import{_ as b}from"./_plugin-vue_export-helper-BCo6x5W8.js";const v=["innerHTML"],m={class:"title"},g={class:"msg"},y={class:"res"},O={class:"btn-group"},j=l((P=((e,t)=>{for(var s in t||(t={}))o.call(t,s)&&n(e,s,t[s]);if(r)for(var s of r(t))a.call(t,s)&&n(e,s,t[s]);return e})({},{name:"ArtResultPage"}),t(P,s({__name:"ArtResultPage",props:{type:{default:"success"},title:{default:""},message:{default:""},iconCode:{default:""}},setup:e=>(e,t)=>(i(),c("div",{class:f(["page-content",e.type])},[p("i",{class:"iconfont-sys icon",innerHTML:e.iconCode},null,8,v),p("h1",m,u(e.title),1),p("p",g,u(e.message),1),p("div",y,[d(e.$slots,"content",{},void 0,!0)]),p("div",O,[d(e.$slots,"buttons",{},void 0,!0)])],2))}))));var P;const _=b(j,[["__scopeId","data-v-0c46648a"]]);export{_};
