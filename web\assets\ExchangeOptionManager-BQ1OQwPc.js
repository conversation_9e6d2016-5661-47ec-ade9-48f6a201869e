var e=(e,a,t)=>new Promise(((l,i)=>{var s=e=>{try{o(t.next(e))}catch(a){i(a)}},n=e=>{try{o(t.throw(e))}catch(a){i(a)}},o=e=>e.done?l(e.value):Promise.resolve(e.value).then(s,n);o((t=t.apply(e,a)).next())}));import{q as a}from"./index-BOCMSBcY.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css                         */import{_ as t}from"./ArtTable-ClHSxhXb.js";/* empty css                  *//* empty css                        *//* empty css                    *//* empty css                  */import{k as l,r as i,c as s,d as n,n as o,p as r,V as c,O as d,C as u,R as p,a2 as m,x as h,B as f,a3 as v,D as g,S as y,Y as _,u as b,aH as x,W as w,aI as k,X as C,aG as j,aJ as O,aK as A,a6 as V,F as z,Z as E,aL as M,aM as T,aN as D,aO as U,aP as q,ab as B,ac as P,$ as S,ak as R,aQ as $,aR as F,ae as I,E as L,aD as W}from"./vendor-9ydHGNSq.js";import{u as X,a as Z}from"./useForm-DsFKgpsv.js";import{u as G}from"./useDebounce-D5CjXTjR.js";import"./index-DEP0vMzR.js";import{_ as H}from"./_plugin-vue_export-helper-BCo6x5W8.js";class J{static getChangeOptions(t){return e(this,null,(function*(){try{const e=yield a.get({url:"/change-options",params:t});return Array.isArray(e)?e:e&&"object"==typeof e&&"data"in e&&e.data||[]}catch(e){throw e}}))}static getActiveChangeOptions(){return e(this,null,(function*(){return this.getChangeOptions({isActive:!0})}))}static getChangeOptionById(a){return e(this,null,(function*(){try{return(yield this.getChangeOptions()).find((e=>e._id===a))||null}catch(e){throw e}}))}static createChangeOption(t){return e(this,null,(function*(){try{return yield a.post({url:"/change-options",data:t,showErrorMessage:!0})}catch(e){throw e}}))}static updateChangeOption(t,l){return e(this,null,(function*(){try{return yield a.put({url:`/change-options/${t}`,data:l,showErrorMessage:!0})}catch(e){throw e}}))}static deleteChangeOption(t){return e(this,null,(function*(){try{yield a.del({url:`/change-options/${t}`,showErrorMessage:!0})}catch(e){throw e}}))}}const K={class:"exchange-option-manager"},N={key:0,class:"action-bar"},Q={key:1,class:"mobile-header"},Y={class:"mobile-stats-card"},ee={class:"stats-icon"},ae={class:"stats-content"},te={class:"stats-number"},le={class:"price-text"},ie={key:3,class:"mobile-card-list"},se={key:0,class:"mobile-skeleton"},ne={class:"skeleton-header"},oe={class:"skeleton-content"},re={class:"card-header-mobile"},ce={class:"exchange-info"},de={class:"exchange-icon"},ue={class:"exchange-details"},pe={class:"exchange-name"},me={class:"exchange-index"},he={class:"exchange-status"},fe={class:"exchange-content"},ve={class:"info-section"},ge={class:"info-item"},ye={class:"info-value price-value"},_e={key:0,class:"info-item description-item"},be={class:"info-value description-text"},xe={class:"card-footer"},we={class:"action-buttons-mobile"},ke={key:2,class:"empty-state"},Ce={key:4,class:"mobile-action-bar"},je={class:"action-bar-container"},Oe={class:"action-icon add-icon"},Ae={class:"action-icon refresh-icon"},Ve={class:"dialog-footer"},ze=H(l({__name:"ExchangeOptionManager",emits:["data-changed"],setup(a,{emit:l}){const H=l,ze=i(!1),Ee=()=>{ze.value=window.innerWidth<=768},Me=i(!1),Te=()=>e(this,null,(function*(){if(!Me.value){Me.value=!0;try{yield Be(),H("data-changed")}catch(e){}finally{setTimeout((()=>{Me.value=!1}),1e3)}}})),De=G((()=>e(this,null,(function*(){try{yield Be(),H("data-changed")}catch(e){}}))),{delay:1500}),{tableData:Ue,loading:qe,refresh:Be}=X({api:()=>e(this,null,(function*(){const e=yield J.getChangeOptions();return{data:e,total:e.length}})),deleteApi:e=>J.deleteChangeOption(e),immediate:!0,apiOptions:{noThrottle:!0}}),{formRef:Pe,formData:Se,rules:Re,submitting:$e,submit:Fe,setCreateMode:Ie}=Z({initialData:{name:"",description:"",price:0,isActive:!0},rules:{name:[{required:!0,message:"请输入选项名称",trigger:"blur"}],description:[{required:!0,message:"请输入描述",trigger:"blur"}],price:[{required:!0,message:"请输入价格",trigger:"blur"}]},submitApi:e=>Se._id?J.updateChangeOption(Se._id,e):J.createChangeOption(e),onSuccess:()=>{L.success(We.value?"更新成功":"创建成功"),Le.value=!1,De()},showSuccessMessage:!1}),Le=i(!1),We=s((()=>!!Se._id)),Xe=()=>{Ie(),Le.value=!0},Ze=e=>{Object.assign(Se,e),Le.value=!0},Ge=a=>e(this,null,(function*(){try{yield W.confirm(`确定要删除随心换选项"${a.name}"吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield J.deleteChangeOption(a._id),L.success("删除成功"),De()}catch(e){"cancel"!==e&&L.error("删除失败")}})),He=()=>e(this,null,(function*(){yield Fe()}));return n((()=>{Ee(),o((()=>{setTimeout((()=>{Ee()}),100)})),window.addEventListener("resize",Ee)})),r((()=>{window.removeEventListener("resize",Ee)})),(e,a)=>{const l=_,i=v,s=O,n=A,o=t,r=M,L=T,W=S,X=P,Z=R,G=F,H=$,J=B,Ee=I,De=c("ripple"),Be=j;return u(),d("div",K,[ze.value?p("",!0):(u(),d("div",N,[m((u(),f(i,{type:"primary",size:"default",onClick:Xe,class:"add-button"},{default:g((()=>[h(l,{class:"el-icon--left"},{default:g((()=>[h(b(x))])),_:1}),a[6]||(a[6]=y("span",{class:"btn-text"},"新增选项",-1))])),_:1,__:[6]})),[[De]]),m((u(),f(i,{class:w(["refresh-button",{refreshing:Me.value}]),onClick:Te,disabled:Me.value,circle:""},{default:g((()=>[h(l,null,{default:g((()=>[h(b(k))])),_:1})])),_:1},8,["class","disabled"])),[[De]])])),ze.value?(u(),d("div",Q,[y("div",Y,[y("div",ee,[h(l,null,{default:g((()=>[h(b(k))])),_:1})]),y("div",ae,[y("div",te,C(b(Ue).length),1),a[7]||(a[7]=y("div",{class:"stats-label"},"随心换选项",-1))])])])):p("",!0),ze.value?p("",!0):m((u(),f(o,{key:2,data:b(Ue),style:{"margin-top":"10px"}},{default:g((()=>[h(s,{type:"index",label:"序号",width:"60",align:"center"}),h(s,{prop:"name",label:"选项名称","min-width":"120"}),h(s,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),h(s,{prop:"price",label:"价格",width:"100",align:"center"},{default:g((({row:e})=>[y("span",le,"¥"+C(e.price),1)])),_:1}),h(s,{prop:"isActive",label:"状态",width:"80",align:"center"},{default:g((({row:e})=>[h(n,{type:e.isActive?"success":"danger"},{default:g((()=>[V(C(e.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),h(s,{label:"操作",width:"150",align:"center",fixed:"right"},{default:g((({row:e})=>[m((u(),f(i,{type:"primary",size:"small",onClick:a=>Ze(e)},{default:g((()=>a[8]||(a[8]=[V(" 编辑 ")]))),_:2,__:[8]},1032,["onClick"])),[[De]]),m((u(),f(i,{type:"danger",size:"small",onClick:a=>Ge(e)},{default:g((()=>a[9]||(a[9]=[V(" 删除 ")]))),_:2,__:[9]},1032,["onClick"])),[[De]])])),_:1})])),_:1},8,["data"])),[[Be,b(qe)]]),ze.value?(u(),d("div",ie,[b(qe)?(u(),d("div",se,[(u(),d(z,null,E(3,(e=>y("div",{key:e,class:"mobile-skeleton-card"},[h(L,{animated:""},{template:g((()=>[y("div",ne,[h(r,{variant:"circle",style:{width:"40px",height:"40px"}}),y("div",oe,[h(r,{variant:"text",style:{width:"60%",height:"16px"}}),h(r,{variant:"text",style:{width:"40%",height:"14px"}})])]),h(r,{variant:"rect",style:{width:"100%",height:"60px","margin-top":"12px"}})])),_:1})]))),64))])):p("",!0),b(qe)?p("",!0):(u(),f(D,{key:1,name:"card-list",tag:"div",class:"card-container"},{default:g((()=>[(u(!0),d(z,null,E(b(Ue),((e,t)=>(u(),d("div",{key:e.id,class:"exchange-card"},[y("div",re,[y("div",ce,[y("div",de,[h(l,null,{default:g((()=>[h(b(k))])),_:1})]),y("div",ue,[y("div",pe,C(e.name),1),y("div",me,"序号: "+C(t+1),1)])]),y("div",he,[h(n,{type:e.isActive?"success":"danger",size:"small"},{default:g((()=>[V(C(e.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])]),y("div",fe,[y("div",ve,[y("div",ge,[a[10]||(a[10]=y("span",{class:"info-label"},"价格",-1)),y("span",ye,"¥"+C(e.price),1)]),e.description?(u(),d("div",_e,[a[11]||(a[11]=y("span",{class:"info-label"},"描述",-1)),y("span",be,C(e.description),1)])):p("",!0)])]),y("div",xe,[y("div",we,[h(i,{size:"small",type:"primary",plain:"",onClick:a=>Ze(e)},{default:g((()=>[h(l,null,{default:g((()=>[h(b(U))])),_:1}),a[12]||(a[12]=y("span",null,"编辑",-1))])),_:2,__:[12]},1032,["onClick"]),h(i,{size:"small",type:"danger",plain:"",onClick:a=>Ge(e)},{default:g((()=>[h(l,null,{default:g((()=>[h(b(q))])),_:1}),a[13]||(a[13]=y("span",null,"删除",-1))])),_:2,__:[13]},1032,["onClick"])])])])))),128))])),_:1})),b(qe)||0!==b(Ue).length?p("",!0):(u(),d("div",ke,[a[15]||(a[15]=y("div",{class:"empty-icon"},"🔄",-1)),a[16]||(a[16]=y("div",{class:"empty-text"},"暂无随心换选项",-1)),h(i,{type:"primary",onClick:Xe,class:"empty-action"},{default:g((()=>[h(l,null,{default:g((()=>[h(b(x))])),_:1}),a[14]||(a[14]=y("span",null,"新增选项",-1))])),_:1,__:[14]})]))])):p("",!0),ze.value?(u(),d("div",Ce,[y("div",je,[y("div",{class:"action-item primary-action",onClick:Xe},[y("div",Oe,[h(l,{size:24},{default:g((()=>[h(b(x))])),_:1})]),a[17]||(a[17]=y("span",{class:"action-label"},"新增",-1))]),y("div",{class:"action-item",onClick:Te},[y("div",Ae,[h(l,{size:20},{default:g((()=>[h(b(k))])),_:1})]),a[18]||(a[18]=y("span",{class:"action-label"},"刷新",-1))])])])):p("",!0),h(Ee,{modelValue:Le.value,"onUpdate:modelValue":a[5]||(a[5]=e=>Le.value=e),title:We.value?"编辑随心换选项":"新增随心换选项",width:"500px","close-on-click-modal":!1},{footer:g((()=>[y("div",Ve,[h(i,{onClick:a[4]||(a[4]=e=>Le.value=!1)},{default:g((()=>a[21]||(a[21]=[V("取消")]))),_:1,__:[21]}),m((u(),f(i,{type:"primary",onClick:He,loading:b($e)},{default:g((()=>a[22]||(a[22]=[V(" 确定 ")]))),_:1,__:[22]},8,["loading"])),[[De]])])])),default:g((()=>[h(J,{ref_key:"formRef",ref:Pe,model:b(Se),rules:b(Re),"label-width":"100px",class:"config-form"},{default:g((()=>[h(X,{label:"选项名称",prop:"name"},{default:g((()=>[h(W,{modelValue:b(Se).name,"onUpdate:modelValue":a[0]||(a[0]=e=>b(Se).name=e),placeholder:"请输入选项名称"},null,8,["modelValue"])])),_:1}),h(X,{label:"描述",prop:"description"},{default:g((()=>[h(W,{modelValue:b(Se).description,"onUpdate:modelValue":a[1]||(a[1]=e=>b(Se).description=e),type:"textarea",rows:3,placeholder:"请输入选项描述"},null,8,["modelValue"])])),_:1}),h(X,{label:"价格",prop:"price"},{default:g((()=>[h(Z,{modelValue:b(Se).price,"onUpdate:modelValue":a[2]||(a[2]=e=>b(Se).price=e),min:0,precision:2,placeholder:"请输入价格",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),h(X,{label:"状态",prop:"isActive"},{default:g((()=>[h(H,{modelValue:b(Se).isActive,"onUpdate:modelValue":a[3]||(a[3]=e=>b(Se).isActive=e)},{default:g((()=>[h(G,{label:!0},{default:g((()=>a[19]||(a[19]=[V("启用")]))),_:1,__:[19]}),h(G,{label:!1},{default:g((()=>a[20]||(a[20]=[V("禁用")]))),_:1,__:[20]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-207dfc08"]]),Ee=Object.freeze(Object.defineProperty({__proto__:null,default:ze},Symbol.toStringTag,{value:"Module"}));export{J as C,ze as E,Ee as a};
