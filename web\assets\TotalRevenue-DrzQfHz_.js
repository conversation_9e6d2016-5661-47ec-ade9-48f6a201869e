import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";import{k as s,r as t,O as e,C as r,S as o,x as d,u as i}from"./vendor-9ydHGNSq.js";import{_ as n}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";const c={class:"custom-card total-revenue-card art-custom-card"},m={class:"custom-card-body"},u=n(s({__name:"TotalRevenue",setup(s){const n=t(["周一","周二","周三","周四","周五","周六","周日"]),u=t([{name:"线上销售",data:[12,13,5,15,10,15,18]},{name:"线下销售",data:[10,11,20,5,11,13,10]}]);return(s,t)=>{const p=a;return r(),e("div",c,[t[0]||(t[0]=o("div",{class:"custom-card-header"},[o("span",{class:"title"},"总收入")],-1)),o("div",m,[d(p,{height:"100%",data:i(u),xAxisData:i(n),showLegend:!0,showAxisLine:!1,barWidth:"18%"},null,8,["data","xAxisData"])])])}}}),[["__scopeId","data-v-0a4f6f3c"]]);export{u as default};
