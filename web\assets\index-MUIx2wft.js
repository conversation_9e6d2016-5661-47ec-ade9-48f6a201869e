var t=Object.defineProperty,e=Object.defineProperties,o=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,n=(e,o,a)=>o in e?t(e,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[o]=a;import{k as i,s,c as u,r as c,d,O as b,C as f,Q as m,W as g,R as p,S as v,u as C,F as y,Z as h,P as _,X as k,a1 as x}from"./vendor-9ydHGNSq.js";import{u as w}from"./index-BOCMSBcY.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";const j={key:0,class:"basic-banner__meteors"},$={class:"basic-banner__content"},P=["src"],M=i((D=((t,e)=>{for(var o in e||(e={}))r.call(e,o)&&n(t,o,e[o]);if(a)for(var o of a(e))l.call(e,o)&&n(t,o,e[o]);return t})({},{name:"ArtBasicBanner"}),B={__name:"index",props:{height:{default:"11rem"},title:{},subtitle:{},backgroundColor:{default:"var(--el-color-primary-light-3)"},decoration:{type:Boolean,default:!0},buttonConfig:{default:()=>({show:!0,text:"查看",color:"#fff",textColor:"#333",radius:"6px"})},meteorConfig:{default:()=>({enabled:!1,count:10})},imageConfig:{default:()=>({src:"",width:"12rem",bottom:"-3rem",right:"0"})},titleColor:{default:"white"},subtitleColor:{default:"white"}},emits:["click","buttonClick"],setup(t,{emit:e}){const o=w(),{isDark:a}=s(o),r=t,l=e,n=u((()=>{var t,e;return null!=(e=null==(t=r.buttonConfig)?void 0:t.color)?e:"#fff"})),i=u((()=>{var t,e;return null!=(e=null==(t=r.buttonConfig)?void 0:t.textColor)?e:"#333"})),O=u((()=>{var t,e;return null!=(e=null==(t=r.buttonConfig)?void 0:t.radius)?e:"6px"})),M=c([]);return d((()=>{var t,e,o;(null==(t=r.meteorConfig)?void 0:t.enabled)&&(M.value=function(t){const e=100/t;return Array.from({length:t},((t,o)=>({x:o*e+Math.random()*e,speed:Math.random()>.5?5+3*Math.random():2+2*Math.random(),delay:5*Math.random()})))}(null!=(o=null==(e=r.meteorConfig)?void 0:e.count)?o:10))})),(t,e)=>{var o;return f(),b("div",{class:g(["basic-banner art-custom-card",{"has-decoration":t.decoration}]),style:m({backgroundColor:t.backgroundColor,height:t.height}),onClick:e[1]||(e[1]=t=>l("click"))},[(null==(o=t.meteorConfig)?void 0:o.enabled)&&C(a)?(f(),b("div",j,[(f(!0),b(y,null,h(M.value,((t,e)=>(f(),b("span",{key:e,class:"meteor",style:m({top:"-60px",left:`${t.x}%`,animationDuration:`${t.speed}s`,animationDelay:`${t.delay}s`})},null,4)))),128))])):p("",!0),v("div",$,[_(t.$slots,"title",{},(()=>[t.title?(f(),b("p",{key:0,class:"basic-banner__title",style:m({color:t.titleColor})},k(t.title),5)):p("",!0)]),!0),_(t.$slots,"subtitle",{},(()=>[t.subtitle?(f(),b("p",{key:0,class:"basic-banner__subtitle",style:m({color:t.subtitleColor})},k(t.subtitle),5)):p("",!0)]),!0),_(t.$slots,"button",{},(()=>{var o,a;return[(null==(o=t.buttonConfig)?void 0:o.show)?(f(),b("div",{key:0,class:"basic-banner__button",style:m({backgroundColor:n.value,color:i.value,borderRadius:O.value}),onClick:e[0]||(e[0]=x((t=>l("buttonClick")),["stop"]))},k(null==(a=t.buttonConfig)?void 0:a.text),5)):p("",!0)]}),!0),_(t.$slots,"default",{},void 0,!0),t.imageConfig.src?(f(),b("img",{key:0,class:"basic-banner__background-image",src:t.imageConfig.src,style:m({width:t.imageConfig.width,bottom:t.imageConfig.bottom,right:t.imageConfig.right}),loading:"lazy",alt:"背景图片"},null,12,P)):p("",!0)])],6)}}},e(D,o(B))));var D,B;const A=O(M,[["__scopeId","data-v-96c579b3"]]);export{A as _};
