import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useOnline, useNetwork, useIntervalFn } from '@vueuse/core'

export interface NetworkStatusOptions {
  autoReconnect?: boolean
  maxRetries?: number
  retryInterval?: number
  onReconnect?: () => void
  onOffline?: () => void
  checkUrl?: string
}

/**
 * 增强的网络状态监测钩子
 * 基于VueUse的useOnline和useNetwork，添加了重试机制和连接检测
 * @param options 配置选项
 * @returns 网络状态相关数据和方法
 */
export function useNetworkStatus(options: NetworkStatusOptions = {}) {
  const {
    autoReconnect = true,
    maxRetries = 3,
    retryInterval = 5000,
    onReconnect,
    onOffline,
    checkUrl = 'https://www.google.com/favicon.ico'
  } = options

  // 使用VueUse的网络状态检测
  const isOnline = useOnline()
  const network = useNetwork()

  const retryCount = ref(0)
  const isRetrying = ref(false)
  const lastCheckTime = ref<Date | null>(null)
  const retryTimerId = ref<number | null>(null)

  // 网络状态变化处理
  const handleOnline = () => {
    if (autoReconnect && typeof onReconnect === 'function') {
      onReconnect()
    }
    stopRetrying()
  }

  const handleOffline = () => {
    if (typeof onOffline === 'function') {
      onOffline()
    }
    if (autoReconnect) {
      startRetrying()
    }
  }

  // 开始重试
  const startRetrying = () => {
    if (retryTimerId.value !== null) return

    retryCount.value = 0
    isRetrying.value = true
    retryTimerId.value = window.setInterval(() => {
      if (isOnline.value) {
        stopRetrying()
        return
      }

      retryCount.value++

      // 检查是否有网络连接（发送一个小请求）
      checkConnection().then((online) => {
        if (online) {
          if (typeof onReconnect === 'function') {
            onReconnect()
          }
          stopRetrying()
        } else if (retryCount.value >= maxRetries) {
          stopRetrying()
        }
      }).catch(() => {
        // 检查连接失败，继续重试或停止
        if (retryCount.value >= maxRetries) {
          stopRetrying()
        }
      })
    }, retryInterval)
  }

  // 停止重试
  const stopRetrying = () => {
    if (retryTimerId.value !== null) {
      clearInterval(retryTimerId.value)
      retryTimerId.value = null
    }
    isRetrying.value = false
    retryCount.value = 0
  }

  // 检查网络连接
  const checkConnection = async (): Promise<boolean> => {
    try {
      // 使用fetch发送一个HEAD请求到某个可靠的URL（如Google的favicon）
      // 使用Date.now()防止缓存
      const response = await fetch('https://www.google.com/favicon.ico?' + Date.now(), {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-store'
      })
      return true
    } catch (error) {
      return false
    }
  }

  // 手动检查网络连接
  const checkNetworkConnection = async () => {
    const online = await checkConnection()
    lastCheckTime.value = new Date()

    if (online !== isOnline.value) {
      if (online && typeof onReconnect === 'function') {
        onReconnect()
      }
      if (!online && typeof onOffline === 'function') {
        onOffline()
      }
    }
    return online
  }

  // 手动触发重连
  const reconnect = () => {
    if (typeof onReconnect === 'function') {
      onReconnect()
    }
  }

  onMounted(() => {
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // 初始化时检查一次网络状态
    checkNetworkConnection()
  })

  onUnmounted(() => {
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
    stopRetrying()
  })

  return {
    isOnline,
    isRetrying,
    retryCount,
    maxRetries,
    lastCheckTime,
    network,
    checkNetworkConnection,
    reconnect,
    startRetrying,
    stopRetrying
  }
}
