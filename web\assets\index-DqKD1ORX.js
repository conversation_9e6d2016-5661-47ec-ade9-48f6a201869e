const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-BOCMSBcY.js","./vendor-9ydHGNSq.js","./index-C3d_MO4K.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,a=Object.defineProperties,s=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,r=(a,s,t)=>s in a?e(a,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[s]=t,n=(e,a)=>{for(var s in a||(a={}))l.call(a,s)&&r(e,s,a[s]);if(t)for(var s of t(a))o.call(a,s)&&r(e,s,a[s]);return e},i=(e,t)=>a(e,s(t)),c=(e,a,s)=>new Promise(((t,l)=>{var o=e=>{try{n(s.next(e))}catch(a){l(a)}},r=e=>{try{n(s.throw(e))}catch(a){l(a)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,r);n((s=s.apply(e,a)).next())}));import{c as d,u as p,d as u,A as m,n as h,U as f,_ as g,H as y,o as v,S as w,i as b}from"./index-BOCMSBcY.js";/* empty css                *//* empty css                  *//* empty css                    */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{_}from"./index-B5Hr4D84.js";/* empty css                        *//* empty css                  *//* empty css                     *//* empty css                         */import{_ as j}from"./LoginLeftView-Csr_wmh1.js";import{k,s as x,M as C,r as P,aa as T,c as V,d as E,V as D,O as I,C as O,x as S,S as R,X as $,u as A,D as L,az as M,F as U,Z as K,aA as q,W as B,R as F,aB as H,ac as z,$ as Y,Y as Z,aE as G,aF as J,a6 as N,a2 as Q,B as W,a3 as X,a0 as ee,ab as ae,E as se,n as te,f as le}from"./vendor-9ydHGNSq.js";import{a as oe}from"./index-CT2bh8-V.js";import{_ as re}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./lf_icon2-BooRsIsW.js";const ne={class:"login"},ie={class:"right-wrap"},ce={class:"top-right-wrap"},de={class:"iconfont-sys"},pe={class:"menu-txt"},ue={key:0,class:"iconfont-sys icon-check"},me={class:"header"},he={class:"login-wrap"},fe={class:"form"},ge={class:"title"},ye={class:"sub-title"},ve={class:"captcha-container"},we=["src"],be={key:1,class:"captcha-loading"},_e={class:"forget-password"},je={style:{"margin-top":"30px"}},ke=re(k(i(n({},{name:"Login"}),{__name:"index",setup(e){const{t:a}=d(),s=p(),{isDark:t,systemThemeType:l}=x(s),o=u(),r=C(),k=P(""),re=P(""),ke=m.systemInfo.name,xe=P(),Ce=T({username:"",password:"",captchaCode:"",rememberPassword:!1}),Pe=V((()=>({username:[{required:!0,message:a("login.placeholder[0]"),trigger:"blur"}],password:[{required:!0,message:a("login.placeholder[1]"),trigger:"blur"}],captchaCode:[{required:!0,message:a("login.captchaPlaceholder"),trigger:"blur"}]}))),Te=P(!1);oe();const Ve=()=>c(this,null,(function*(){try{const e=yield f.getCaptcha();e&&e.data?(k.value=e.data.captchaKey,re.value=e.data.captchaImage):e&&e.captchaKey?(k.value=e.captchaKey,re.value=e.captchaImage):se.error(a("login.captchaFailed"))}catch(e){se.error(a("login.captchaFailed"))}})),Ee=()=>{Ve()},De=e=>{const a=e.replace(/[^0-9]/g,"").slice(0,4);Ce.captchaCode=a},Ie=e=>{["Backspace","Delete","Tab","Enter","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End"].includes(e.key)||(/^[0-9]$/.test(e.key)||e.preventDefault(),Ce.captchaCode.length>=4&&e.preventDefault())};E((()=>{Ve()}));const Oe=()=>c(this,null,(function*(){if(xe.value)try{if(!(yield xe.value.validate()))return;Te.value=!0;const{username:a,password:s,captchaCode:t}=Ce,l={username:a,password:s,captchaKey:k.value,captchaCode:t},d=yield f.login(l);let p;if(d&&d.data)p=d.data;else{if(!d||!d.accessToken)throw new Error("登录失败 - 响应格式不正确");p=d}if(!p||!p.accessToken)throw new Error("登录失败 - 未收到有效的令牌");if(o.setToken(p.accessToken,p.refreshToken),p.user){const e=i(n({},p.user),{permissions:p.user.perms||[]});o.setUserInfo(e)}else o.setUserInfo({permissions:[],roles:[]});o.setLoginStatus(!0);const{resetRouterState:u}=yield g((()=>c(this,null,(function*(){const{resetRouterState:e}=yield import("./index-BOCMSBcY.js").then((e=>e.Z));return{resetRouterState:e}}))),__vite__mapDeps([0,1,2]),import.meta.url);u(r);const{useMenuStore:m}=yield g((()=>c(this,null,(function*(){const{useMenuStore:e}=yield import("./index-BOCMSBcY.js").then((e=>e.Y));return{useMenuStore:e}}))),__vite__mapDeps([0,1,2]),import.meta.url),h=m();try{yield h.forceRefreshMenu()}catch(e){}yield te(),Se(),setTimeout((()=>{r.push(y)}),150)}catch(e){e instanceof v||se.error(e instanceof Error?e.message:a("login.failed")),Ve()}finally{Te.value=!1}})),Se=()=>{setTimeout((()=>{const e=o.info.name||o.info.username||Ce.username;le({title:a("login.success.title"),type:"success",duration:2500,zIndex:1e4,message:`${a("login.success.message")}, ${e}!`})}),150)},{locale:Re}=d(),$e=e=>{Re.value!==e&&(Re.value=e,o.setLanguage(e))},Ae=()=>{let{LIGHT:e,DARK:a}=w;b().switchThemeStyles(l.value===e?a:e)};return(e,a)=>{const s=j,l=q,o=M,r=H,n=_,i=Y,c=z,d=Z,p=J,u=X,m=ae,f=D("ripple");return O(),I("div",ne,[S(s),R("div",ie,[R("div",ce,[R("div",{class:"btn theme-btn",onClick:Ae},[R("i",de,$(A(t)?"":""),1)]),S(r,{onCommand:$e,"popper-class":"langDropDownStyle"},{dropdown:L((()=>[S(o,null,{default:L((()=>[(O(!0),I(U,null,K(A(h),(e=>(O(),I("div",{key:e.value,class:"lang-btn-item"},[S(l,{command:e.value,class:B({"is-selected":A(Re)===e.value})},{default:L((()=>[R("span",pe,$(e.label),1),A(Re)===e.value?(O(),I("i",ue,"")):F("",!0)])),_:2},1032,["command","class"])])))),128))])),_:1})])),default:L((()=>[a[4]||(a[4]=R("div",{class:"btn language-btn"},[R("i",{class:"iconfont-sys icon-language"},"")],-1))])),_:1,__:[4]})]),R("div",me,[S(n,{class:"icon"}),R("h1",null,$(A(ke)),1)]),R("div",he,[R("div",fe,[R("h3",ge,$(e.$t("login.title")),1),R("p",ye,$(e.$t("login.subTitle")),1),S(m,{ref_key:"formRef",ref:xe,model:A(Ce),rules:A(Pe),onKeyup:ee(Oe,["enter"]),style:{"margin-top":"25px"}},{default:L((()=>[S(c,{prop:"username"},{default:L((()=>[S(i,{placeholder:e.$t("login.placeholder[0]"),modelValue:A(Ce).username,"onUpdate:modelValue":a[0]||(a[0]=e=>A(Ce).username=e),modelModifiers:{trim:!0}},null,8,["placeholder","modelValue"])])),_:1}),S(c,{prop:"password"},{default:L((()=>[S(i,{placeholder:e.$t("login.placeholder[1]"),modelValue:A(Ce).password,"onUpdate:modelValue":a[1]||(a[1]=e=>A(Ce).password=e),modelModifiers:{trim:!0},type:"password",radius:"8px",autocomplete:"off","show-password":""},null,8,["placeholder","modelValue"])])),_:1}),S(c,{prop:"captchaCode"},{default:L((()=>[R("div",ve,[S(i,{placeholder:e.$t("login.captchaPlaceholder"),modelValue:A(Ce).captchaCode,"onUpdate:modelValue":a[2]||(a[2]=e=>A(Ce).captchaCode=e),modelModifiers:{trim:!0},class:"captcha-input",maxlength:"4",onInput:De,onKeypress:Ie},null,8,["placeholder","modelValue"]),R("div",{class:"captcha-image",onClick:Ee,title:"点击刷新验证码"},[A(re)?(O(),I("img",{key:0,src:A(re),alt:"验证码",draggable:"false"},null,8,we)):(O(),I("div",be,[S(d,null,{default:L((()=>[S(A(G))])),_:1})]))])])])),_:1}),R("div",_e,[S(p,{modelValue:A(Ce).rememberPassword,"onUpdate:modelValue":a[3]||(a[3]=e=>A(Ce).rememberPassword=e)},{default:L((()=>[N($(e.$t("login.rememberPwd")),1)])),_:1},8,["modelValue"])]),R("div",je,[Q((O(),W(u,{class:"login-btn",type:"primary",onClick:Oe,loading:A(Te)},{default:L((()=>[N($(e.$t("login.btnText")),1)])),_:1},8,["loading"])),[[f]])])])),_:1},8,["model","rules"])])])])])}}})),[["__scopeId","data-v-b5fa2758"]]);export{ke as default};
