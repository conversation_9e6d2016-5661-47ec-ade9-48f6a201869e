import { ref, computed, type Ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import request from '@/utils/http'

export interface UseApiOptions<T = any> {
  /** 是否立即执行 */
  immediate?: boolean
  /** 默认数据 */
  defaultData?: T
  /** 成功回调 */
  onSuccess?: (data: T, response: AxiosResponse<T>) => void
  /** 错误回调 */
  onError?: (error: any) => void
  /** 完成回调（无论成功失败） */
  onFinally?: () => void
  /** 是否显示错误消息 */
  showErrorMessage?: boolean
  /** 是否显示成功消息 */
  showSuccessMessage?: boolean | string
  /** 错误重试次数 */
  retryCount?: number
  /** 重试间隔（毫秒） */
  retryDelay?: number
}

export interface UseApiReturn<T> {
  /** 响应数据 */
  data: Ref<T | null>
  /** 加载状态 */
  loading: Ref<boolean>
  /** 错误信息 */
  error: Ref<any>
  /** 是否已完成（成功或失败） */
  finished: Ref<boolean>
  /** 执行请求 */
  execute: (config?: AxiosRequestConfig) => Promise<T>
  /** 刷新数据 */
  refresh: () => Promise<T>
  /** 重置状态 */
  reset: () => void
  /** 取消请求 */
  cancel: () => void
}

/**
 * 通用API请求Hook
 * 提供加载状态、错误处理、重试机制等功能
 */
export function useApi<T = any>(
  apiFunction: (config?: AxiosRequestConfig) => Promise<T>,
  options: UseApiOptions<T> = {}
): UseApiReturn<T> {
  const {
    immediate = false,
    defaultData = null,
    onSuccess,
    onError,
    onFinally,
    showErrorMessage = true,
    showSuccessMessage = false,
    retryCount = 0,
    retryDelay = 1000
  } = options

  // 状态管理
  const data = ref<T | null>(defaultData) as Ref<T | null>
  const loading = ref(false)
  const error = ref<any>(null)
  const finished = ref(false)

  // 取消控制器
  let abortController: AbortController | null = null
  let currentRetryCount = 0

  // 执行请求
  const execute = async (config?: AxiosRequestConfig): Promise<T> => {
    // 重置状态
    loading.value = true
    error.value = null
    finished.value = false

    // 创建取消控制器
    abortController = new AbortController()

    try {
      const response = await apiFunction({
        ...config,
        signal: abortController.signal
      })

      data.value = response
      finished.value = true

      // 成功回调
      if (onSuccess) {
        onSuccess(response, response as any)
      }

      // 显示成功消息
      if (showSuccessMessage) {
        const message = typeof showSuccessMessage === 'string' 
          ? showSuccessMessage 
          : '操作成功'
        ElMessage.success(message)
      }

      currentRetryCount = 0
      return response
    } catch (err: any) {
      // 如果是取消请求，不处理错误
      if (err.name === 'AbortError' || err.code === 'ERR_CANCELED') {
        return Promise.reject(err)
      }

      error.value = err
      finished.value = true

      // 重试机制
      if (currentRetryCount < retryCount) {
        currentRetryCount++
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        return execute(config)
      }

      // 错误回调
      if (onError) {
        onError(err)
      } else if (showErrorMessage) {
        const message = err.response?.data?.message || err.message || '请求失败'
        ElMessage.error(message)
      }

      throw err
    } finally {
      loading.value = false
      if (onFinally) {
        onFinally()
      }
    }
  }

  // 刷新数据
  const refresh = () => execute()

  // 重置状态
  const reset = () => {
    data.value = defaultData
    loading.value = false
    error.value = null
    finished.value = false
    currentRetryCount = 0
  }

  // 取消请求
  const cancel = () => {
    if (abortController) {
      abortController.abort()
      abortController = null
    }
    loading.value = false
  }

  // 立即执行
  if (immediate) {
    execute()
  }

  return {
    data,
    loading,
    error,
    finished,
    execute,
    refresh,
    reset,
    cancel
  }
}

/**
 * GET请求的便捷Hook
 */
export function useGet<T = any>(
  url: string,
  params?: any,
  options?: UseApiOptions<T>
) {
  return useApi<T>(
    (config) => request.get({ url, params, ...config }),
    options
  )
}

/**
 * POST请求的便捷Hook
 */
export function usePost<T = any>(
  url: string,
  data?: any,
  options?: UseApiOptions<T>
) {
  return useApi<T>(
    (config) => request.post({ url, data, ...config }),
    options
  )
}

/**
 * PUT请求的便捷Hook
 */
export function usePut<T = any>(
  url: string,
  data?: any,
  options?: UseApiOptions<T>
) {
  return useApi<T>(
    (config) => request.put({ url, data, ...config }),
    options
  )
}

/**
 * DELETE请求的便捷Hook
 */
export function useDelete<T = any>(
  url: string,
  options?: UseApiOptions<T>
) {
  return useApi<T>(
    (config) => request.del({ url, ...config }),
    options
  )
}
