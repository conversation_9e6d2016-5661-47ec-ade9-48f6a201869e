import{_ as s}from"./index-CAIJlOgO.js";import{k as a,c as t,O as i,C as e,S as o,x as r,u as d}from"./vendor-9ydHGNSq.js";import{_ as n}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./index-DEP0vMzR.js";import"./useChart-DM-2b2dH.js";const m={class:"custom-card art-custom-card visitor-insights"},c={class:"custom-card-body"},p=n(a({__name:"VisitorInsights",setup(a){const n=t((()=>[{name:"老客户",data:[280,350,300,250,230,210,240,280,320,350,300,200]},{name:"新客户",data:[260,200,150,130,180,270,340,380,300,220,170,130]}])),p=["1","2","3","4","5","6","7","8","9","10","11","12"];return(a,t)=>{const l=s;return e(),i("div",m,[t[0]||(t[0]=o("div",{class:"custom-card-header"},[o("span",{class:"title"},"访客洞察")],-1)),o("div",c,[r(l,{height:"15rem",data:d(n),xAxisData:p,showLegend:!0,showAxisLabel:!0,showAxisLine:!1,showSplitLine:!0},null,8,["data"])])])}}}),[["__scopeId","data-v-68525110"]]);export{p as default};
