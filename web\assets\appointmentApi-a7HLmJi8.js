import{q as t}from"./index-BOCMSBcY.js";class e{static getCustomerList(e){return t.get({url:"/customers",params:e})}static getAppointmentList(e){return t.get({url:"/appointments",params:e})}static getAppointmentDetail(e){return t.get({url:`/appointments/${e}`})}static createAppointment(e){return t.post({url:"/appointments",data:e,showErrorMessage:!0})}static updateAppointment(e,r){return t.put({url:`/appointments/${e}`,data:r,showErrorMessage:!0})}static deleteAppointment(e){return t.del({url:`/appointments/${e}`,showErrorMessage:!0})}static updateAppointmentRemark(e,r){return t.put({url:`/appointments/${e}/remark`,data:{remark:r},showErrorMessage:!0})}static updateAppointmentStatus(e,r){return t.put({url:`/appointments/${e}/status`,data:r,showErrorMessage:!0})}static getPackageTypes(){return t.get({url:"/package-types"})}static getChangeOptions(){return t.get({url:"/change-options"})}static getMemoryCards(){return t.get({url:"/memory-cards"})}static getAppointmentStats(){return t.get({url:"/appointments/stats"})}static getCustomerStats(){return t.get({url:"/customers/stats"})}static getTodayAppointments(){return t.get({url:"/appointments/today"})}static getAppointmentStatusStats(){return t.get({url:"/appointments/status-stats"})}}export{e as A};
