// 系统级别枚举

// 菜单类型
export enum MenuTypeEnum {
  LEFT = 'left',
  TOP = 'top',
  TOP_LEFT = 'top-left',
  DUAL_MENU = 'dual-menu'
}

// App theme enum
export enum SystemThemeEnum {
  DARK = 'dark',
  LIGHT = 'light',
  AUTO = 'auto'
}

// Menu theme enum
export enum MenuThemeEnum {
  DARK = 'dark',
  LIGHT = 'light',
  DESIGN = 'design'
}

// Menu close width
export enum MenuWidth {
  CLOSE = '70px'
}

// Language
export enum LanguageEnum {
  ZH = 'zh',
  EN = 'en'
}

// Icon type
export enum IconTypeEnum {
  CLASS_NAME = 'className',
  UNICODE = 'unicode'
}

// Container width
export enum ContainerWidthEnum {
  FULL = '100%',
  BOXED = '1200px'
}

// Background color enum
export enum BgColorEnum {
  PRIMARY = 'bg-primary',
  SECONDARY = 'bg-secondary',
  WARNING = 'bg-warning',
  ERROR = 'bg-error',
  SUCCESS = 'bg-success',
  DANGER = 'bg-danger'
}
