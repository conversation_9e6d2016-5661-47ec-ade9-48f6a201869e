var e=(e,a,t)=>new Promise(((l,s)=>{var i=e=>{try{o(t.next(e))}catch(a){s(a)}},r=e=>{try{o(t.throw(e))}catch(a){s(a)}},o=e=>e.done?l(e.value):Promise.resolve(e.value).then(i,r);o((t=t.apply(e,a)).next())}));import{q as a}from"./index-BOCMSBcY.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css                         */import{_ as t}from"./ArtTable-ClHSxhXb.js";/* empty css                  *//* empty css                        *//* empty css                    *//* empty css                  */import{k as l,r as s,c as i,d as r,n as o,p as n,V as d,O as c,C as u,R as m,a2 as p,x as y,B as v,a3 as f,D as _,S as h,Y as g,u as b,aH as k,W as w,aI as C,aS as x,X as j,aG as M,aJ as z,aK as V,a6 as A,F as T,Z as D,aL as B,aM as E,aN as O,aO as S,aP as U,ab as q,ac as P,$ as R,ak as $,aQ as F,aR as I,ae as L,E as W,aD as G}from"./vendor-9ydHGNSq.js";import{u as X,a as Z}from"./useForm-DsFKgpsv.js";import{u as H}from"./useDebounce-D5CjXTjR.js";import"./index-DEP0vMzR.js";import{_ as J}from"./_plugin-vue_export-helper-BCo6x5W8.js";class K{static getMemoryCards(t){return e(this,null,(function*(){try{return(yield a.get({url:"/memory-cards",params:t})).data||[]}catch(e){throw e}}))}static getActiveMemoryCards(){return e(this,null,(function*(){return this.getMemoryCards({isActive:!0})}))}static getMemoryCardById(a){return e(this,null,(function*(){try{return(yield this.getMemoryCards()).find((e=>e._id===a))||null}catch(e){throw e}}))}static createMemoryCard(t){return e(this,null,(function*(){return a.post({url:"/memory-cards",data:t,showErrorMessage:!0})}))}static updateMemoryCard(t,l){return e(this,null,(function*(){return a.put({url:`/memory-cards/${t}`,data:l,showErrorMessage:!0})}))}static deleteMemoryCard(t){return e(this,null,(function*(){return a.del({url:`/memory-cards/${t}`,showErrorMessage:!0})}))}}const N={class:"memory-card-manager"},Q={key:0,class:"action-bar"},Y={key:1,class:"mobile-header"},ee={class:"mobile-stats-card"},ae={class:"stats-icon"},te={class:"stats-content"},le={class:"stats-number"},se={class:"price-text"},ie={key:3,class:"mobile-card-list"},re={key:0,class:"mobile-skeleton"},oe={class:"skeleton-header"},ne={class:"skeleton-content"},de={class:"card-header-mobile"},ce={class:"memory-info"},ue={class:"memory-icon"},me={class:"memory-details"},pe={class:"memory-size"},ye={class:"memory-index"},ve={class:"memory-status"},fe={class:"memory-content"},_e={class:"info-section"},he={class:"info-item"},ge={class:"info-value price-value"},be={key:0,class:"info-item description-item"},ke={class:"info-value description-text"},we={class:"card-footer"},Ce={class:"action-buttons-mobile"},xe={key:2,class:"empty-state"},je={key:4,class:"mobile-action-bar"},Me={class:"action-bar-container"},ze={class:"action-icon add-icon"},Ve={class:"action-icon refresh-icon"},Ae={class:"dialog-footer"},Te=J(l({__name:"MemoryCardManager",emits:["data-changed"],setup(a,{emit:l}){const J=l,Te=s(!1),De=()=>{Te.value=window.innerWidth<=768},Be=s(!1),Ee=()=>e(this,null,(function*(){if(!Be.value){Be.value=!0;try{yield qe(),J("data-changed")}catch(e){}finally{setTimeout((()=>{Be.value=!1}),1e3)}}})),Oe=H((()=>e(this,null,(function*(){try{yield qe(),J("data-changed")}catch(e){}}))),{delay:1500}),{tableData:Se,loading:Ue,refresh:qe}=X({api:()=>e(this,null,(function*(){const e=yield K.getMemoryCards();return{data:e,total:e.length}})),deleteApi:e=>K.deleteMemoryCard(e),immediate:!0,apiOptions:{noThrottle:!0}}),{formRef:Pe,formData:Re,rules:$e,submitting:Fe,submit:Ie,setCreateMode:Le}=Z({initialData:{size:"",description:"",price:0,isActive:!0},rules:{size:[{required:!0,message:"请输入内存卡规格",trigger:"blur"}],description:[{required:!0,message:"请输入描述",trigger:"blur"}],price:[{required:!0,message:"请输入价格",trigger:"blur"}]},submitApi:e=>Re._id?K.updateMemoryCard(Re._id,e):K.createMemoryCard(e),onSuccess:()=>{W.success(Ge.value?"更新成功":"创建成功"),We.value=!1,Oe()},showSuccessMessage:!1}),We=s(!1),Ge=i((()=>!!Re._id)),Xe=()=>{Le(),We.value=!0},Ze=e=>{Object.assign(Re,e),We.value=!0},He=a=>e(this,null,(function*(){try{yield G.confirm(`确定要删除内存卡"${a.size}"吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield K.deleteMemoryCard(a._id),W.success("删除成功"),Oe()}catch(e){"cancel"!==e&&W.error("删除失败")}})),Je=()=>e(this,null,(function*(){yield Ie()}));return r((()=>{De(),o((()=>{setTimeout((()=>{De()}),100)})),window.addEventListener("resize",De)})),n((()=>{window.removeEventListener("resize",De)})),(e,a)=>{const l=g,s=f,i=z,r=V,o=t,n=B,W=E,G=R,X=P,Z=$,H=I,J=F,K=q,De=L,Oe=d("ripple"),qe=M;return u(),c("div",N,[Te.value?m("",!0):(u(),c("div",Q,[p((u(),v(s,{type:"primary",size:"default",onClick:Xe,class:"add-button"},{default:_((()=>[y(l,{class:"el-icon--left"},{default:_((()=>[y(b(k))])),_:1}),a[6]||(a[6]=h("span",{class:"btn-text"},"新增内存卡",-1))])),_:1,__:[6]})),[[Oe]]),p((u(),v(s,{class:w(["refresh-button",{refreshing:Be.value}]),onClick:Ee,disabled:Be.value,circle:""},{default:_((()=>[y(l,null,{default:_((()=>[y(b(C))])),_:1})])),_:1},8,["class","disabled"])),[[Oe]])])),Te.value?(u(),c("div",Y,[h("div",ee,[h("div",ae,[y(l,null,{default:_((()=>[y(b(x))])),_:1})]),h("div",te,[h("div",le,j(b(Se).length),1),a[7]||(a[7]=h("div",{class:"stats-label"},"内存卡配置",-1))])])])):m("",!0),Te.value?m("",!0):p((u(),v(o,{key:2,data:b(Se),style:{"margin-top":".625rem"}},{default:_((()=>[y(i,{type:"index",label:"序号",width:"60",align:"center"}),y(i,{prop:"size",label:"内存卡规格","min-width":"120"}),y(i,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),y(i,{prop:"price",label:"价格",width:"100",align:"center"},{default:_((({row:e})=>[h("span",se,"¥"+j(e.price),1)])),_:1}),y(i,{prop:"isActive",label:"状态",width:"80",align:"center"},{default:_((({row:e})=>[y(r,{type:e.isActive?"success":"danger"},{default:_((()=>[A(j(e.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),y(i,{label:"操作",width:"150",align:"center",fixed:"right"},{default:_((({row:e})=>[p((u(),v(s,{type:"primary",size:"small",onClick:a=>Ze(e)},{default:_((()=>a[8]||(a[8]=[A(" 编辑 ")]))),_:2,__:[8]},1032,["onClick"])),[[Oe]]),p((u(),v(s,{type:"danger",size:"small",onClick:a=>He(e)},{default:_((()=>a[9]||(a[9]=[A(" 删除 ")]))),_:2,__:[9]},1032,["onClick"])),[[Oe]])])),_:1})])),_:1},8,["data"])),[[qe,b(Ue)]]),Te.value?(u(),c("div",ie,[b(Ue)?(u(),c("div",re,[(u(),c(T,null,D(3,(e=>h("div",{key:e,class:"mobile-skeleton-card"},[y(W,{animated:""},{template:_((()=>[h("div",oe,[y(n,{variant:"circle",style:{width:"40px",height:"40px"}}),h("div",ne,[y(n,{variant:"text",style:{width:"60%",height:"16px"}}),y(n,{variant:"text",style:{width:"40%",height:"14px"}})])]),y(n,{variant:"rect",style:{width:"100%",height:"60px","margin-top":"12px"}})])),_:1})]))),64))])):m("",!0),b(Ue)?m("",!0):(u(),v(O,{key:1,name:"card-list",tag:"div",class:"card-container"},{default:_((()=>[(u(!0),c(T,null,D(b(Se),((e,t)=>(u(),c("div",{key:e.id,class:"memory-card"},[h("div",de,[h("div",ce,[h("div",ue,[y(l,null,{default:_((()=>[y(b(x))])),_:1})]),h("div",me,[h("div",pe,j(e.size),1),h("div",ye,"序号: "+j(t+1),1)])]),h("div",ve,[y(r,{type:e.isActive?"success":"danger",size:"small"},{default:_((()=>[A(j(e.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])]),h("div",fe,[h("div",_e,[h("div",he,[a[10]||(a[10]=h("span",{class:"info-label"},"价格",-1)),h("span",ge,"¥"+j(e.price),1)]),e.description?(u(),c("div",be,[a[11]||(a[11]=h("span",{class:"info-label"},"描述",-1)),h("span",ke,j(e.description),1)])):m("",!0)])]),h("div",we,[h("div",Ce,[y(s,{size:"small",type:"primary",plain:"",onClick:a=>Ze(e)},{default:_((()=>[y(l,null,{default:_((()=>[y(b(S))])),_:1}),a[12]||(a[12]=h("span",null,"编辑",-1))])),_:2,__:[12]},1032,["onClick"]),y(s,{size:"small",type:"danger",plain:"",onClick:a=>He(e)},{default:_((()=>[y(l,null,{default:_((()=>[y(b(U))])),_:1}),a[13]||(a[13]=h("span",null,"删除",-1))])),_:2,__:[13]},1032,["onClick"])])])])))),128))])),_:1})),b(Ue)||0!==b(Se).length?m("",!0):(u(),c("div",xe,[a[15]||(a[15]=h("div",{class:"empty-icon"},"💾",-1)),a[16]||(a[16]=h("div",{class:"empty-text"},"暂无内存卡配置",-1)),y(s,{type:"primary",onClick:Xe,class:"empty-action"},{default:_((()=>[y(l,null,{default:_((()=>[y(b(k))])),_:1}),a[14]||(a[14]=h("span",null,"新增内存卡",-1))])),_:1,__:[14]})]))])):m("",!0),Te.value?(u(),c("div",je,[h("div",Me,[h("div",{class:"action-item primary-action",onClick:Xe},[h("div",ze,[y(l,{size:24},{default:_((()=>[y(b(k))])),_:1})]),a[17]||(a[17]=h("span",{class:"action-label"},"新增",-1))]),h("div",{class:"action-item",onClick:Ee},[h("div",Ve,[y(l,{size:20},{default:_((()=>[y(b(C))])),_:1})]),a[18]||(a[18]=h("span",{class:"action-label"},"刷新",-1))])])])):m("",!0),y(De,{modelValue:We.value,"onUpdate:modelValue":a[5]||(a[5]=e=>We.value=e),title:Ge.value?"编辑内存卡":"新增内存卡",width:"31.25rem","close-on-click-modal":!1},{footer:_((()=>[h("div",Ae,[y(s,{onClick:a[4]||(a[4]=e=>We.value=!1)},{default:_((()=>a[21]||(a[21]=[A("取消")]))),_:1,__:[21]}),p((u(),v(s,{type:"primary",onClick:Je,loading:b(Fe)},{default:_((()=>a[22]||(a[22]=[A(" 确定 ")]))),_:1,__:[22]},8,["loading"])),[[Oe]])])])),default:_((()=>[y(K,{ref_key:"formRef",ref:Pe,model:b(Re),rules:b($e),"label-width":"6.25rem",class:"config-form"},{default:_((()=>[y(X,{label:"内存卡规格",prop:"size"},{default:_((()=>[y(G,{modelValue:b(Re).size,"onUpdate:modelValue":a[0]||(a[0]=e=>b(Re).size=e),placeholder:"请输入内存卡规格，如：64GB"},null,8,["modelValue"])])),_:1}),y(X,{label:"描述",prop:"description"},{default:_((()=>[y(G,{modelValue:b(Re).description,"onUpdate:modelValue":a[1]||(a[1]=e=>b(Re).description=e),type:"textarea",rows:3,placeholder:"请输入内存卡描述"},null,8,["modelValue"])])),_:1}),y(X,{label:"价格",prop:"price"},{default:_((()=>[y(Z,{modelValue:b(Re).price,"onUpdate:modelValue":a[2]||(a[2]=e=>b(Re).price=e),min:0,precision:2,placeholder:"请输入价格",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),y(X,{label:"状态",prop:"isActive"},{default:_((()=>[y(J,{modelValue:b(Re).isActive,"onUpdate:modelValue":a[3]||(a[3]=e=>b(Re).isActive=e)},{default:_((()=>[y(H,{label:!0},{default:_((()=>a[19]||(a[19]=[A("启用")]))),_:1,__:[19]}),y(H,{label:!1},{default:_((()=>a[20]||(a[20]=[A("禁用")]))),_:1,__:[20]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-74a41560"]]),De=Object.freeze(Object.defineProperty({__proto__:null,default:Te},Symbol.toStringTag,{value:"Module"}));export{Te as M,K as a,De as b};
