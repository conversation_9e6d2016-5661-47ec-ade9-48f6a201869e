import { defineStore } from 'pinia'
import { ref } from 'vue'
import { AppRouteRecord } from '@/types/router'
import { menuService, MenuCreateData, MenuUpdateData } from '@/api/menuApi'
import { ElMessage } from 'element-plus'

// 菜单
export const useMenuStore = defineStore('menuStore', () => {
  const menuList = ref<AppRouteRecord[]>([])
  const menuWidth = ref('')

  const setMenuList = (list: AppRouteRecord[]) => (menuList.value = list)

  const setMenuWidth = (width: string) => (menuWidth.value = width)

  // 刷新菜单列表
  const refreshMenuList = async () => {
    try {
      const { menuList: newMenuList } = await menuService.getMenuList()
      setMenuList(newMenuList)
      return newMenuList
    } catch (error) {
      ElMessage.error('刷新菜单失败')
      throw error
    }
  }

  // 强制刷新菜单（用于用户切换时）
  const forceRefreshMenu = async () => {
    try {
      // 先清空菜单
      setMenuList([])

      // 等待一个tick
      await new Promise(resolve => setTimeout(resolve, 50))

      // 重新获取菜单
      const { menuList: newMenuList } = await menuService.getMenuList(0) // 不延迟
      setMenuList(newMenuList)

      return newMenuList
    } catch (error) {
      console.error('强制刷新菜单失败:', error)
      throw error
    }
  }

  // 添加菜单
  const addMenu = async (data: MenuCreateData) => {
    try {
      const newMenu = await menuService.createMenu(data)
      // 刷新菜单列表
      await refreshMenuList()
      return newMenu
    } catch (error) {
      throw error
    }
  }

  // 更新菜单
  const updateMenu = async (data: MenuUpdateData) => {
    try {
      const updatedMenu = await menuService.updateMenu(data)
      // 刷新菜单列表
      await refreshMenuList()
      return updatedMenu
    } catch (error) {
      throw error
    }
  }

  // 删除菜单
  const deleteMenu = async (id: string) => {
    try {
      await menuService.deleteMenu(id)
      // 刷新菜单列表
      await refreshMenuList()
    } catch (error) {
      throw error
    }
  }

  return {
    menuList,
    menuWidth,
    setMenuList,
    setMenuWidth,
    refreshMenuList,
    forceRefreshMenu,
    addMenu,
    updateMenu,
    deleteMenu
  }
})
