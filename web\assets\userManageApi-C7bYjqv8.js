import{q as s}from"./index-BOCMSBcY.js";class t{static getUserList(t={page:1,limit:10}){return s.get({url:"/users",params:t})}static getUserDetail(t){return s.get({url:`/users/${t}`})}static createUser(t){return s.post({url:"/users",data:t})}static updateUser(t){return s.put({url:`/users/${t.userId}`,data:t})}static deleteUser(t){return s.del({url:`/users/${t}`})}static updateUserStatus(t){return s.put({url:`/users/${t.userId}/status`,data:{status:t.status}})}static generateResetSecurityCode(t,e){return s.post({url:`/users/${t}/reset-security-code`,data:e,showErrorMessage:!1})}static resetUserPassword(t,e){return s.post({url:`/users/${t}/reset-password`,data:e,showErrorMessage:!1})}}export{t as U};
