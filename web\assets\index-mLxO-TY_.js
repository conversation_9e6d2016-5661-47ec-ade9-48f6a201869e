var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,n=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,s=(e,l)=>{for(var a in l||(l={}))o.call(l,a)&&n(e,a,l[a]);if(t)for(var a of t(l))u.call(l,a)&&n(e,a,l[a]);return e},r=(e,l,a)=>new Promise(((t,o)=>{var u=e=>{try{s(a.next(e))}catch(l){o(l)}},n=e=>{try{s(a.throw(e))}catch(l){o(l)}},s=e=>e.done?t(e.value):Promise.resolve(e.value).then(u,n);s((a=a.apply(e,l)).next())}));import{k as i,c as d,X as m,a as p,V as c,f}from"./index-BOCMSBcY.js";import{k as v,O as b,C as h,P as _,Q as g,u as y,bK as k,bv as V,s as j,r as w,d as U,p as x,S as C,x as A,D as E,az as L,F as I,Z as B,B as O,aA as H,W as T,a6 as M,X as S,aB as z,aF as N,aC as P,R as q,i as D,aa as $,c as F,V as R,b7 as Z,a2 as X,a3 as K,aJ as G,br as Q,ae as W,ab as Y,ac as J,aQ as ee,bx as le,aX as ae,aY as te,$ as oe,ak as ue,aj as ne,E as se,n as re,l as ie,aK as de,aD as me}from"./vendor-9ydHGNSq.js";import{_ as pe}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                        */import{_ as ce}from"./index-Dg4yhe7D.js";/* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                        */import{u as fe,_ as ve}from"./ArtTable-ClHSxhXb.js";/* empty css                        *//* empty css                    *//* empty css                  *//* empty css                  *//* empty css                        *//* empty css                  *//* empty css                     *//* empty css                         */import{T as be}from"./formEnum-BLgiZVxV.js";import{l as he}from"./vue-draggable-plus-KL8EvIWr.js";import{_ as _e}from"./index-Bys663yA.js";import{u as ge,A as ye}from"./ArtButtonTable-BDkAL_5A.js";import"./iconfont-DPUoc2h2.js";/* empty css                   *//* empty css                      */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-CT2bh8-V.js";/* empty css                 *//* empty css                       */const ke=pe(v({__name:"ArtTableFullScreen",setup(e){const{containerMinHeight:l}=i();return(e,a)=>(h(),b("div",{class:"art-table-full-screen",style:g({height:y(l)})},[_(e.$slots,"default",{},void 0,!0)],4))}}),[["__scopeId","data-v-f94d78d0"]]),Ve={class:"table-header"},je={class:"left"},we={class:"right"},Ue={class:"iconfont-sys"},xe=pe(v({__name:"ArtTableHeader",props:k({showZebra:{type:Boolean,default:!0},showBorder:{type:Boolean,default:!0},showHeaderBackground:{type:Boolean,default:!0}},{columns:{required:!0},columnsModifiers:{}}),emits:k(["refresh"],["update:columns"]),setup(e,{emit:l}){const{t:a}=d(),t=V(e,"columns"),o=l,u=[{value:be.SMALL,label:a("table.sizeOptions.small")},{value:be.DEFAULT,label:a("table.sizeOptions.default")},{value:be.LARGE,label:a("table.sizeOptions.large")}],n=fe(),{tableSize:s,isZebra:r,isBorder:i,isHeaderBackground:m}=j(n),p=()=>{o("refresh")},c=e=>{fe().setTableSize(e)},f=w(!1),v=()=>{const e=document.querySelector("#table-full-screen");e&&(f.value=!f.value,e.classList.toggle("el-full-screen"))};return U((()=>{document.addEventListener("keydown",(e=>{"Escape"===e.key&&f.value&&v()}))})),x((()=>{document.removeEventListener("keydown",(e=>{"Escape"===e.key&&f.value&&v()}))})),(l,o)=>{const n=H,d=L,g=z;return h(),b("div",Ve,[C("div",je,[_(l.$slots,"left",{},void 0,!0)]),C("div",we,[C("div",{class:"btn",onClick:p},o[4]||(o[4]=[C("i",{class:"iconfont-sys"},"",-1)])),A(g,{onCommand:c},{dropdown:E((()=>[A(d,null,{default:E((()=>[(h(),b(I,null,B(u,(e=>C("div",{key:e.value,class:"table-size-btn-item"},[(h(),O(n,{key:e.value,command:e.value,class:T({"is-selected":y(s)===e.value})},{default:E((()=>[M(S(e.label),1)])),_:2},1032,["command","class"]))]))),64))])),_:1})])),default:E((()=>[o[5]||(o[5]=C("div",{class:"btn"},[C("i",{class:"iconfont-sys"},"")],-1))])),_:1,__:[5]}),C("div",{class:"btn",onClick:v},[C("i",Ue,S(y(f)?"":""),1)]),A(y(P),{placement:"bottom",trigger:"click"},{reference:E((()=>o[6]||(o[6]=[C("div",{class:"btn"},[C("i",{class:"iconfont-sys",style:{"font-size":"17px"}},"")],-1)]))),default:E((()=>[C("div",null,[A(y(he),{modelValue:t.value,"onUpdate:modelValue":o[0]||(o[0]=e=>t.value=e)},{default:E((()=>[(h(!0),b(I,null,B(t.value,(e=>(h(),b("div",{key:e.prop||e.type,class:"column-option"},[o[7]||(o[7]=C("div",{class:"drag-icon"},[C("i",{class:"iconfont-sys"},"")],-1)),A(y(N),{modelValue:e.checked,"onUpdate:modelValue":l=>e.checked=l,disabled:e.disabled},{default:E((()=>[M(S(e.label||("selection"===e.type?y(a)("table.selection"):"")),1)])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])))),128))])),_:1},8,["modelValue"])])])),_:1}),A(y(P),{placement:"bottom",trigger:"click"},{reference:E((()=>o[8]||(o[8]=[C("div",{class:"btn"},[C("i",{class:"iconfont-sys",style:{"font-size":"17px"}},"")],-1)]))),default:E((()=>[C("div",null,[e.showZebra?(h(),O(y(N),{key:0,modelValue:y(r),"onUpdate:modelValue":o[1]||(o[1]=e=>D(r)?r.value=e:null),value:!0},{default:E((()=>[M(S(y(a)("table.zebra")),1)])),_:1},8,["modelValue"])):q("",!0),e.showBorder?(h(),O(y(N),{key:1,modelValue:y(i),"onUpdate:modelValue":o[2]||(o[2]=e=>D(i)?i.value=e:null),value:!0},{default:E((()=>[M(S(y(a)("table.border")),1)])),_:1},8,["modelValue"])):q("",!0),e.showHeaderBackground?(h(),O(y(N),{key:2,modelValue:y(m),"onUpdate:modelValue":o[3]||(o[3]=e=>D(m)?m.value=e:null),value:!0},{default:E((()=>[M(S(y(a)("table.headerBackground")),1)])),_:1},8,["modelValue"])):q("",!0)])])),_:1}),_(l.$slots,"right",{},void 0,!0)])])}}}),[["__scopeId","data-v-aa34ef3c"]]),Ce={class:"menu-page",id:"table-full-screen"},Ae={class:"dialog-footer"};var Ee;const Le=pe(v((Ee=s({},{name:"Menus"}),l(Ee,a({__name:"index",setup(e){const{hasAuth:l}=m(),{menuList:a}=j(p()),t=w(!1),o={name:"",route:""},u=$(s({},o)),n=$(s({},o)),i=()=>{Object.assign(u,s({},o)),Object.assign(n,s({},o)),pe()},d=()=>{Object.assign(n,s({},u)),pe()},v=[{label:"菜单名称",prop:"name",type:"input",config:{clearable:!0}},{label:"路由地址",prop:"route",type:"input",config:{clearable:!0}}],_=e=>{var l,a,t;return e.children&&e.children.length>0?"info":(null==(l=e.meta)?void 0:l.link)&&(null==(a=e.meta)?void 0:a.isIframe)?"success":e.path?"primary":(null==(t=e.meta)?void 0:t.link)?"warning":void 0},{columnChecks:g,columns:k}=ge((()=>[{prop:"meta.title",label:"菜单名称",minWidth:120,formatter:e=>{var l;return f(null==(l=e.meta)?void 0:l.title)}},{prop:"type",label:"菜单类型",formatter:e=>ie(de,{type:_(e)},(()=>(e=>{var l,a,t;return e.children&&e.children.length>0?"目录":(null==(l=e.meta)?void 0:l.link)&&(null==(a=e.meta)?void 0:a.isIframe)?"内嵌":e.path?"菜单":(null==(t=e.meta)?void 0:t.link)?"外链":void 0})(e)))},{prop:"path",label:"路由",formatter:e=>{var l;return(null==(l=e.meta)?void 0:l.link)||e.path||""}},{prop:"meta.authList",label:"可操作权限",formatter:e=>{var l;return ie("div",{},null==(l=e.meta.authList)?void 0:l.map(((e,l)=>ie(P,{placement:"top-start",title:"操作",width:200,trigger:"click",key:l},{default:()=>ie("div",{style:"margin: 0; text-align: right"},[ie(K,{size:"small",type:"primary",onClick:()=>Le("button",e)},{default:()=>"编辑"}),ie(K,{size:"small",type:"danger",onClick:()=>Oe()},{default:()=>"删除"})]),reference:()=>ie(K,{class:"small-btn"},{default:()=>e.title})}))))}},{prop:"date",label:"编辑时间",formatter:()=>"2022-3-12 12:00:00"},{prop:"status",label:"隐藏菜单",formatter:e=>ie(de,{type:e.meta.isHide?"danger":"info"},(()=>e.meta.isHide?"是":"否"))},{prop:"operation",label:"操作",width:180,formatter:e=>ie("div",[l("add")&&ie(ye,{type:"add",onClick:()=>Le("menu")}),l("edit")&&ie(ye,{type:"edit",onClick:()=>we("edit",e)}),l("delete")&&ie(ye,{type:"delete",onClick:()=>Be(e)})])}])),V=()=>{pe()},x=w(!1),L=$({name:"",path:"",label:"",icon:"",isEnable:!0,sort:1,isMenu:!0,keepAlive:!0,isHidden:!0,link:"",isIframe:!1,authName:"",authLabel:"",authIcon:"",authSort:1}),H=w(c.UNICODE),T=w("menu"),z=$({name:[{required:!0,message:"请输入菜单名称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],path:[{required:!0,message:"请输入路由地址",trigger:"blur"}],label:[{required:!0,message:"输入权限标识",trigger:"blur"}],authName:[{required:!0,message:"请输入权限名称",trigger:"blur"}],authLabel:[{required:!0,message:"请输入权限权限标识",trigger:"blur"}]}),N=w([]);U((()=>{pe()}));const pe=()=>{t.value=!0,setTimeout((()=>{N.value=a.value,t.value=!1}),500)},fe=F((()=>{const e=l=>l.filter((l=>{var a,t,o;const u=(null==(a=n.name)?void 0:a.toLowerCase().trim())||"",s=(null==(t=n.route)?void 0:t.toLowerCase().trim())||"",r=f((null==(o=l.meta)?void 0:o.title)||"").toLowerCase(),i=(l.path||"").toLowerCase(),d=!u||r.includes(u),m=!s||i.includes(s);if(l.children&&l.children.length>0){const a=e(l.children);if(a.length>0)return l.children=a,!0}return d&&m}));return e(N.value)})),be=w(!1),he=w(""),Ve=w(),je=F((()=>{const e="menu"===T.value?"菜单":"权限";return be.value?`编辑${e}`:`新建${e}`})),we=(e,l)=>{Le("menu",l,!0)},Ue=()=>{},Ee=()=>r(this,null,(function*(){Ve.value&&(yield Ve.value.validate((e=>r(this,null,(function*(){if(e)try{const e=p();if("menu"!==T.value)return void se.info("权限管理功能暂未实现");{const l={title:L.name,path:L.path,name:L.label,icon:L.icon,sort:L.sort,isEnable:L.isEnable,isMenu:L.isMenu,keepAlive:L.keepAlive,isHidden:L.isHidden,link:L.link,isIframe:L.isIframe};be.value?yield e.updateMenu(s({id:he.value},l)):yield e.addMenu(l)}se.success((be.value?"编辑":"新增")+"成功"),x.value=!1,yield pe()}catch(l){se.error((be.value?"编辑":"新增")+"失败")}})))))})),Le=(e,l,a=!1)=>{x.value=!0,T.value=e,be.value=!1,he.value="",Te.value=a,Ie(),l&&(be.value=!0,he.value=l.id||l.name||"",re((()=>{"menu"===e?(L.name=f(l.meta.title),L.path=l.path,L.label=l.name,L.icon=l.meta.icon,L.sort=l.meta.sort||1,L.isMenu=l.meta.isMenu,L.keepAlive=l.meta.keepAlive,L.isHidden=l.meta.isHidden||!0,L.isEnable=l.meta.isEnable||!0,L.link=l.meta.link,L.isIframe=l.meta.isIframe||!1):(L.authName=l.title,L.authLabel=l.auth_mark,L.authIcon=l.icon||"",L.authSort=l.sort||1)})))},Ie=()=>{var e;null==(e=Ve.value)||e.resetFields(),Object.assign(L,{name:"",path:"",label:"",icon:"",sort:1,isMenu:!0,keepAlive:!0,isHidden:!0,link:"",isIframe:!1,authName:"",authLabel:"",authIcon:"",authSort:1})},Be=e=>r(this,null,(function*(){try{if(yield me.confirm("确定要删除该菜单吗？删除后无法恢复","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e){const l=p(),a=e.id||e.name||"";yield l.deleteMenu(a),se.success("删除成功"),yield pe()}else se.error("无法获取菜单信息")}catch(l){"cancel"!==l&&se.error("删除失败")}})),Oe=()=>r(this,null,(function*(){try{yield me.confirm("确定要删除该权限吗？删除后无法恢复","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),se.success("删除成功")}catch(e){"cancel"!==e&&se.error("删除失败")}})),He=F((()=>!(!be.value||"button"!==T.value)||!(!be.value||"menu"!==T.value)||!(be.value||"menu"!==T.value||!Te.value))),Te=w(!1),Me=w(!1),Se=w(),ze=()=>{Me.value=!Me.value,re((()=>{Se.value&&Se.value[Me.value?"expandAll":"collapseAll"]()}))};return(e,l)=>{const a=_e,o=xe,n=G,s=ve,r=le,m=ee,p=J,c=oe,f=te,_=ae,j=ce,w=ue,U=ne,N=Y,P=W,$=Z,F=ke,se=R("auth"),re=R("ripple");return h(),O(F,null,{default:E((()=>[C("div",Ce,[A(a,{filter:u,"onUpdate:filter":l[0]||(l[0]=e=>u=e),items:v,showExpand:!1,onReset:i,onSearch:d},null,8,["filter"]),A($,{shadow:"never",class:"art-table-card"},{default:E((()=>[A(o,{showZebra:!1,columns:y(g),"onUpdate:columns":l[2]||(l[2]=e=>D(g)?g.value=e:null),onRefresh:V},{left:E((()=>[X((h(),O(y(K),{onClick:l[1]||(l[1]=e=>Le("menu",null,!0))},{default:E((()=>l[20]||(l[20]=[M(" 添加菜单 ")]))),_:1,__:[20]})),[[se,"add"],[re]]),X((h(),O(y(K),{onClick:ze},{default:E((()=>[M(S(Me.value?"收起":"展开"),1)])),_:1})),[[re]])])),_:1},8,["columns"]),A(s,{rowKey:"path",ref_key:"tableRef",ref:Se,loading:t.value,data:fe.value,marginTop:10,stripe:!1},{default:E((()=>[(h(!0),b(I,null,B(y(k),(e=>(h(),O(n,Q({key:e.prop||e.type},{ref_for:!0},e),null,16)))),128))])),_:1},8,["loading","data"]),A(P,{title:je.value,modelValue:x.value,"onUpdate:modelValue":l[19]||(l[19]=e=>x.value=e),width:"700px","align-center":""},{footer:E((()=>[C("span",Ae,[A(y(K),{onClick:l[17]||(l[17]=e=>x.value=!1)},{default:E((()=>l[23]||(l[23]=[M("取 消")]))),_:1,__:[23]}),A(y(K),{type:"primary",onClick:l[18]||(l[18]=e=>Ee())},{default:E((()=>l[24]||(l[24]=[M("确 定")]))),_:1,__:[24]})])])),default:E((()=>[A(N,{ref_key:"formRef",ref:Ve,model:L,rules:z,"label-width":"85px"},{default:E((()=>[A(p,{label:"菜单类型"},{default:E((()=>[A(m,{modelValue:T.value,"onUpdate:modelValue":l[3]||(l[3]=e=>T.value=e),disabled:He.value},{default:E((()=>[A(r,{value:"menu",label:"menu"},{default:E((()=>l[21]||(l[21]=[M("菜单")]))),_:1,__:[21]}),A(r,{value:"button",label:"button"},{default:E((()=>l[22]||(l[22]=[M("权限")]))),_:1,__:[22]})])),_:1},8,["modelValue","disabled"])])),_:1}),"menu"===T.value?(h(),b(I,{key:0},[A(_,{gutter:20},{default:E((()=>[A(f,{span:12},{default:E((()=>[A(p,{label:"菜单名称",prop:"name"},{default:E((()=>[A(c,{modelValue:L.name,"onUpdate:modelValue":l[4]||(l[4]=e=>L.name=e),placeholder:"菜单名称"},null,8,["modelValue"])])),_:1})])),_:1}),A(f,{span:12},{default:E((()=>[A(p,{label:"路由地址",prop:"path"},{default:E((()=>[A(c,{modelValue:L.path,"onUpdate:modelValue":l[5]||(l[5]=e=>L.path=e),placeholder:"路由地址"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),A(_,{gutter:20},{default:E((()=>[A(f,{span:12},{default:E((()=>[A(p,{label:"权限标识",prop:"label"},{default:E((()=>[A(c,{modelValue:L.label,"onUpdate:modelValue":l[6]||(l[6]=e=>L.label=e),placeholder:"权限标识"},null,8,["modelValue"])])),_:1})])),_:1}),A(f,{span:12},{default:E((()=>[A(p,{label:"图标",prop:"icon"},{default:E((()=>[A(j,{modelValue:L.icon,"onUpdate:modelValue":l[7]||(l[7]=e=>L.icon=e),iconType:H.value,width:"100%"},null,8,["modelValue","iconType"])])),_:1})])),_:1})])),_:1}),A(_,{gutter:20},{default:E((()=>[A(f,{span:12},{default:E((()=>[A(p,{label:"菜单排序",prop:"sort",style:{width:"100%"}},{default:E((()=>[A(w,{modelValue:L.sort,"onUpdate:modelValue":l[8]||(l[8]=e=>L.sort=e),style:{width:"100%"},onChange:Ue,min:1,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1}),A(f,{span:12},{default:E((()=>[A(p,{label:"外部链接",prop:"link"},{default:E((()=>[A(c,{modelValue:L.link,"onUpdate:modelValue":l[9]||(l[9]=e=>L.link=e),placeholder:"外部链接/内嵌地址(https://www.baidu.com)"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),A(_,{gutter:20},{default:E((()=>[A(f,{span:5},{default:E((()=>[A(p,{label:"是否启用",prop:"isEnable"},{default:E((()=>[A(U,{modelValue:L.isEnable,"onUpdate:modelValue":l[10]||(l[10]=e=>L.isEnable=e)},null,8,["modelValue"])])),_:1})])),_:1}),A(f,{span:5},{default:E((()=>[A(p,{label:"页面缓存",prop:"keepAlive"},{default:E((()=>[A(U,{modelValue:L.keepAlive,"onUpdate:modelValue":l[11]||(l[11]=e=>L.keepAlive=e)},null,8,["modelValue"])])),_:1})])),_:1}),A(f,{span:5},{default:E((()=>[A(p,{label:"是否显示",prop:"isHidden"},{default:E((()=>[A(U,{modelValue:L.isHidden,"onUpdate:modelValue":l[12]||(l[12]=e=>L.isHidden=e)},null,8,["modelValue"])])),_:1})])),_:1}),A(f,{span:5},{default:E((()=>[A(p,{label:"是否内嵌",prop:"isMenu"},{default:E((()=>[A(U,{modelValue:L.isIframe,"onUpdate:modelValue":l[13]||(l[13]=e=>L.isIframe=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})],64)):q("",!0),"button"===T.value?(h(),b(I,{key:1},[A(_,{gutter:20},{default:E((()=>[A(f,{span:12},{default:E((()=>[A(p,{label:"权限名称",prop:"authName"},{default:E((()=>[A(c,{modelValue:L.authName,"onUpdate:modelValue":l[14]||(l[14]=e=>L.authName=e),placeholder:"权限名称"},null,8,["modelValue"])])),_:1})])),_:1}),A(f,{span:12},{default:E((()=>[A(p,{label:"权限标识",prop:"authLabel"},{default:E((()=>[A(c,{modelValue:L.authLabel,"onUpdate:modelValue":l[15]||(l[15]=e=>L.authLabel=e),placeholder:"权限标识"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),A(_,{gutter:20},{default:E((()=>[A(f,{span:12},{default:E((()=>[A(p,{label:"权限排序",prop:"authSort",style:{width:"100%"}},{default:E((()=>[A(w,{modelValue:L.authSort,"onUpdate:modelValue":l[16]||(l[16]=e=>L.authSort=e),style:{width:"100%"},onChange:Ue,min:1,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})],64)):q("",!0)])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])])),_:1})])])),_:1})}}})))),[["__scopeId","data-v-47fc14aa"]]);export{Le as default};
