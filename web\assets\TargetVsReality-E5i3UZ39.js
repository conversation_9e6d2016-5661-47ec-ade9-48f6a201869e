import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";import{k as s,r as t,O as d,C as i,S as c,bp as e,x as v,u as l}from"./vendor-9ydHGNSq.js";import{_ as r}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";const o={class:"custom-card art-custom-card target-vs-reality"},n={class:"custom-card-body"},p=r(s({__name:"TargetVsReality",setup(s){const r=t(["周一","周二","周三","周四","周五","周六","周日"]),p=t([{name:"线上销售",data:[12,13,5,15,10,15,18]}]);return(s,t)=>{const b=a;return i(),d("div",o,[t[0]||(t[0]=c("div",{class:"custom-card-header"},[c("span",{class:"title"},"目标与实际")],-1)),c("div",n,[v(b,{height:"10rem",data:l(p),xAxisData:l(r),showAxisLine:!1,barWidth:"28%"},null,8,["data","xAxisData"])]),t[1]||(t[1]=e('<div class="custom-card-footer" data-v-b9d54c22><div class="total-item" data-v-b9d54c22><div class="label" data-v-b9d54c22><i class="iconfont-sys" data-v-b9d54c22></i><div class="label-text" data-v-b9d54c22><span data-v-b9d54c22>实际销售额</span><span data-v-b9d54c22>全球</span></div></div><div class="value text-color-green" data-v-b9d54c22>8,823</div></div><div class="total-item" data-v-b9d54c22><div class="label" data-v-b9d54c22><i class="iconfont-sys" data-v-b9d54c22></i><div class="label-text" data-v-b9d54c22><span data-v-b9d54c22>目标销售额</span><span data-v-b9d54c22>商业</span></div></div><div class="value text-color-orange" data-v-b9d54c22>12,122</div></div></div>',1))])}}}),[["__scopeId","data-v-b9d54c22"]]);export{p as default};
