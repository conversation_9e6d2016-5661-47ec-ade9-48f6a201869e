var t=Object.defineProperty,e=Object.defineProperties,r=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable,i=(e,r,o)=>r in e?t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[r]=o;import"./index-BOCMSBcY.js";/* empty css               *//* empty css               */import l from"./Banner-CIdut27n.js";import p from"./TotalOrderVolume-FoJi6oIs.js";import a from"./TotalProducts-r_yDyoug.js";import n from"./SalesTrend-C2c1f59e.js";import u from"./SalesClassification-iGvDn-Bu.js";import{_ as d}from"./TransactionList.vue_vue_type_script_setup_true_lang-CXpW9MpB.js";import _ from"./HotCommodity-C1913Sp3.js";import{_ as j}from"./RecentTransaction.vue_vue_type_script_setup_true_lang-CeqbQL_E.js";import f from"./AnnualSales-In9zIpOM.js";import c from"./ProductSales-DdNssE2M.js";import g from"./SalesGrowth-Djqki-f_.js";import v from"./CartConversionRate-C8koUly5.js";import b from"./HotProductsList-DpxeJi_I.js";import{k as x,O as y,C as O,x as P,D as w,aY as T,aX as C}from"./vendor-9ydHGNSq.js";import{_ as S}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-MUIx2wft.js";import"./lf_icon2-BooRsIsW.js";import"./index-Ds1tx7vI.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";import"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";import"./index.vue_vue_type_script_setup_true_lang-BYz43fXJ.js";import"./index-DyQi0jHX.js";/* empty css                  *//* empty css                     */import"./index-CAIJlOgO.js";import"./index-DgSUn1PT.js";import"./index-BHI_fG6p.js";import"./ArtTable-ClHSxhXb.js";/* empty css                      *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-CT2bh8-V.js";/* empty css                    */import"./icon4-mCHIfvQe.js";const h={class:"ecommerce"},E=x((I=((t,e)=>{for(var r in e||(e={}))s.call(e,r)&&i(t,r,e[r]);if(o)for(var r of o(e))m.call(e,r)&&i(t,r,e[r]);return t})({},{name:"Ecommerce"}),e(I,r({__name:"index",setup:t=>(t,e)=>{const r=T,o=C;return O(),y("div",h,[P(o,{gutter:20},{default:w((()=>[P(r,{sm:24,md:24,lg:16},{default:w((()=>[P(l)])),_:1}),P(r,{sm:12,md:12,lg:4},{default:w((()=>[P(p)])),_:1}),P(r,{sm:12,md:12,lg:4},{default:w((()=>[P(a)])),_:1})])),_:1}),P(o,{gutter:20},{default:w((()=>[P(r,{sm:12,md:12,lg:8},{default:w((()=>[P(n)])),_:1}),P(r,{sm:12,md:12,lg:8},{default:w((()=>[P(u)])),_:1}),P(r,{sm:24,md:24,lg:8},{default:w((()=>[P(o,{gutter:20},{default:w((()=>[P(r,{sm:24,md:12,lg:12},{default:w((()=>[P(c)])),_:1}),P(r,{sm:24,md:12,lg:12},{default:w((()=>[P(g)])),_:1}),P(r,{span:24,class:"no-margin-bottom"},{default:w((()=>[P(v)])),_:1})])),_:1})])),_:1})])),_:1}),P(o,{gutter:20},{default:w((()=>[P(r,{sm:24,md:12,lg:8},{default:w((()=>[P(_)])),_:1}),P(r,{sm:24,md:12,lg:8},{default:w((()=>[P(f)])),_:1}),P(r,{sm:24,md:24,lg:8},{default:w((()=>[P(d)])),_:1})])),_:1}),P(o,{gutter:20},{default:w((()=>[P(r,{md:24,lg:8},{default:w((()=>[P(j)])),_:1}),P(r,{md:24,lg:16,class:"no-margin-bottom"},{default:w((()=>[P(b)])),_:1})])),_:1})])}}))));var I;const R=S(E,[["__scopeId","data-v-9492236a"]]);export{R as default};
