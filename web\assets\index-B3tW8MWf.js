var e=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,s=(a,o,r)=>o in a?e(a,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[o]=r;import{i as n,r as i}from"./index-DEP0vMzR.js";import{u as d}from"./index-BOCMSBcY.js";import{k as p,r as c,q as u,s as m,c as v,d as f,p as h,w as b,O as y,C as g,u as w,x as C,bn as x,n as S}from"./vendor-9ydHGNSq.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";const k={class:"art-map-chart",style:{height:"calc(100vh - 120px)"}},j={key:0,class:"chart-empty-state"},z=p((D=((e,a)=>{for(var o in a||(a={}))t.call(a,o)&&s(e,o,a[o]);if(r)for(var o of r(a))l.call(a,o)&&s(e,o,a[o]);return e})({},{name:"ArtMapChart"}),B={__name:"index",props:{mapData:{default:()=>[]},selectedRegion:{default:""},showLabels:{type:Boolean,default:!0},showScatter:{type:Boolean,default:!0},height:{},loading:{type:Boolean},isEmpty:{type:Boolean,default:!1},colors:{}},emits:["renderComplete","regionClick"],setup(e,{emit:a}){const o={type:"FeatureCollection",features:[]},r=c(null),t=u(null),l=d(),{isDark:s}=m(l),p=e,O=a,z=v((()=>{var e;return p.isEmpty||!(null==(e=p.mapData)?void 0:e.length)&&!o})),D=e=>e.features.map((e=>({name:e.properties.name,value:Math.round(1e3*Math.random()),adcode:e.properties.adcode,level:e.properties.level,selected:!1}))),B=e=>{const a={borderColor:s.value?"rgba(255,255,255,0.6)":"rgba(147,235,248,1)",shadowColor:s.value?"rgba(0,0,0,0.8)":"rgba(128,217,248,1)",labelColor:s.value?"#fff":"#333",backgroundColor:s.value?"rgba(0,0,0,0.8)":"rgba(255,255,255,0.9)"};return{animation:!1,tooltip:{show:!0,backgroundColor:a.backgroundColor,borderColor:s.value?"#333":"#ddd",borderWidth:1,textStyle:{color:a.labelColor},formatter:({data:e})=>{const{name:a,adcode:o,level:r}=e||{};return`\n            <div style="padding: 8px;">\n              <div><strong>名称:</strong> ${a||"未知区域"}</div>\n              <div><strong>代码:</strong> ${o||"暂无"}</div>\n              <div><strong>级别:</strong> ${r||"暂无"}</div>\n            </div>\n          `}},geo:{map:"china",zoom:1,show:!0,roam:!0,scaleLimit:{min:.8,max:3},layoutSize:"100%",emphasis:{label:{show:p.showLabels},itemStyle:{areaColor:"rgba(82,180,255,0.9)",borderColor:"#fff",borderWidth:3}},itemStyle:{borderColor:a.borderColor,borderWidth:2,shadowColor:a.shadowColor,shadowOffsetX:2,shadowOffsetY:15,shadowBlur:15}},series:[{type:"map",map:"china",aspectScale:.75,zoom:1,label:{show:p.showLabels,color:"#fff",fontSize:10},itemStyle:{borderColor:"rgba(147,235,248,0.8)",borderWidth:2,areaColor:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(147,235,248,0.3)"},{offset:1,color:"rgba(32,120,207,0.9)"}]},shadowColor:"rgba(32,120,207,1)",shadowOffsetY:15,shadowBlur:20},emphasis:{label:{show:!0,color:"#fff",fontSize:12},itemStyle:{areaColor:"rgba(82,180,255,0.9)",borderColor:"#fff",borderWidth:3}},select:{label:{show:!0,color:"#fff",fontWeight:"bold"},itemStyle:{areaColor:"#4FAEFB",borderColor:"#fff",borderWidth:2}},data:e},...p.showScatter?[{name:"城市",type:"scatter",coordinateSystem:"geo",symbol:"pin",symbolSize:15,label:{show:!1},itemStyle:{color:"#F99020",shadowBlur:10,shadowColor:"#333"},data:[{name:"北京",value:[116.405285,39.904989,100]},{name:"上海",value:[121.472644,31.231706,100]},{name:"深圳",value:[114.085947,22.547,100]}]}]:[]]}},_=()=>{return e=this,a=null,l=function*(){if(!r.value)return;t.value=n(r.value),i("china",o);const e=p.mapData.length>0?p.mapData:D(o),a=B(e);t.value.setOption(a),t.value.on("click",P),O("renderComplete")},new Promise(((o,r)=>{var t=e=>{try{n(l.next(e))}catch(a){r(a)}},s=e=>{try{n(l.throw(e))}catch(a){r(a)}},n=e=>e.done?o(e.value):Promise.resolve(e.value).then(t,s);n((l=l.apply(e,a)).next())}));var e,a,l},P=e=>{var a,o,r;if("series"===e.componentType){const l={name:e.name,adcode:(null==(a=e.data)?void 0:a.adcode)||"",level:(null==(o=e.data)?void 0:o.level)||""};null==(r=t.value)||r.dispatchAction({type:"select",seriesIndex:0,dataIndex:e.dataIndex}),O("regionClick",l)}},W=()=>{var e;null==(e=t.value)||e.resize()},E=()=>{t.value&&(t.value.off("click",P),t.value.dispose(),t.value=null),window.removeEventListener("resize",W)};return f((()=>{z.value||_().then((()=>{setTimeout(W,100)})),window.addEventListener("resize",W)})),h(E),b(s,((e,a)=>{e!==a&&t.value&&(E(),S((()=>{z.value||_()})))})),b((()=>p.mapData),(()=>{if(t.value&&!z.value){const e=p.mapData.length>0?p.mapData:D(o),a=B(e);t.value.setOption(a)}}),{deep:!0}),(e,a)=>(g(),y("div",k,[w(z)?(g(),y("div",j,[C(w(x),{description:"暂无地图数据"})])):(g(),y("div",{key:1,id:"china-map",ref_key:"chinaMapRef",ref:r,class:"china-map"},null,512))]))}},a(D,o(B))));var D,B;const _=O(z,[["__scopeId","data-v-b167eb5c"]]);export{_ as default};
