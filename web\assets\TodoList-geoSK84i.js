import"./index-BOCMSBcY.js";/* empty css                    */import{k as a,aa as e,O as s,C as t,bp as d,S as l,F as o,Z as c,x as m,X as p,aF as r,u as n}from"./vendor-9ydHGNSq.js";import{_ as u}from"./_plugin-vue_export-helper-BCo6x5W8.js";const i={class:"card art-custom-card"},v={class:"list"},x={class:"title"},_={class:"date subtitle"},b=u(a({__name:"TodoList",setup(a){const u=e([{username:"查看今天工作内容",date:"上午 09:30",complate:!0},{username:"回复邮件",date:"上午 10:30",complate:!0},{username:"工作汇报整理",date:"上午 11:00",complate:!0},{username:"产品需求会议",date:"下午 02:00",complate:!1},{username:"整理会议内容",date:"下午 03:30",complate:!1},{username:"明天工作计划",date:"下午 06:30",complate:!1}]);return(a,e)=>{const b=r;return t(),s("div",i,[e[0]||(e[0]=d('<div class="card-header" data-v-d82490d0><div class="title" data-v-d82490d0><h4 class="box-title" data-v-d82490d0>代办事项</h4><p class="subtitle" data-v-d82490d0>待处理<span class="text-danger" data-v-d82490d0>3</span></p></div></div>',1)),l("div",v,[(t(!0),s(o,null,c(n(u),((a,e)=>(t(),s("div",{key:e},[l("p",x,p(a.username),1),l("p",_,p(a.date),1),m(b,{modelValue:a.complate,"onUpdate:modelValue":e=>a.complate=e},null,8,["modelValue","onUpdate:modelValue"])])))),128))])])}}}),[["__scopeId","data-v-d82490d0"]]);export{b as default};
