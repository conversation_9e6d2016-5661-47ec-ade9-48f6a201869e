import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";import{k as s,r as t,O as e,C as r,S as o,x as i,u as d}from"./vendor-9ydHGNSq.js";import{_ as c}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";const m={class:"custom-card art-custom-card volume-service-level"},l={class:"custom-card-body"},n=c(s({__name:"VolumeServiceLevel",setup(s){const c=t(["产品A","产品B","产品C","产品D","产品E"]),n=t([{name:"业务量",data:[20,25,30,35,40],stack:"total"},{name:"服务量",data:[30,35,40,45,50],stack:"total"}]);return(s,t)=>{const u=a;return r(),e("div",m,[t[0]||(t[0]=o("div",{class:"custom-card-header"},[o("span",{class:"title"},"业务量与服务水平")],-1)),o("div",l,[i(u,{height:"14.3rem",data:d(n),xAxisData:d(c),showLegend:!0,showAxisLine:!1,stack:!0,barWidth:"22%"},null,8,["data","xAxisData"])])])}}}),[["__scopeId","data-v-5792b052"]]);export{n as default};
