var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,t=(a,l,r)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,i=(e,a,l)=>new Promise(((r,o)=>{var s=e=>{try{i(l.next(e))}catch(a){o(a)}},t=e=>{try{i(l.throw(e))}catch(a){o(a)}},i=e=>e.done?r(e.value):Promise.resolve(e.value).then(s,t);i((l=l.apply(e,a)).next())}));import{Q as u}from"./index-BOCMSBcY.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";import{k as n,aa as d,r as p,c as m,V as c,O as g,C as f,S as _,a2 as v,x as b,D as w,ac as h,$ as y,a0 as V,al as j,F as k,Z as C,B as P,am as U,a3 as x,a6 as z,ab as A,R as S,u as O,aH as q,aG as D,aJ as R,aK as B,X as E,aW as L,aQ as F,aR as I,ae as M,aD as T,E as K,a_ as Q}from"./vendor-9ydHGNSq.js";import{U as Z}from"./userManageApi-C7bYjqv8.js";import{u as $,a as G}from"./useForm-DsFKgpsv.js";import"./index-DEP0vMzR.js";/* empty css                       *//* empty css                   */import{_ as H}from"./_plugin-vue_export-helper-BCo6x5W8.js";const J={class:"user-management"},N={class:"search-bar"},W={class:"action-bar"},X={class:"pagination-wrapper"},Y={class:"dialog-footer"},ee={class:"dialog-footer"},ae=n((le=((e,a)=>{for(var l in a||(a={}))o.call(a,l)&&t(e,l,a[l]);if(r)for(var l of r(a))s.call(a,l)&&t(e,l,a[l]);return e})({},{name:"UserManagement"}),a(le,l({__name:"index-optimized",setup(e){const a=d({username:"",role:"",status:""}),l=p([{label:"超级管理员",value:"superadmin"},{label:"管理员",value:"admin"},{label:"普通用户",value:"user"}]),{hasRole:r,isSuperAdmin:o}=u(),s=m((()=>r(["admin","superadmin"]))),t=m((()=>o.value)),n=m((()=>o.value?l.value.filter((e=>"superadmin"!==e.value)):r("admin")?l.value.filter((e=>"user"===e.value)):[])),{tableData:H,loading:ae,pagination:le,refresh:re,search:oe,handlePageChange:se,handleSizeChange:te,handleSelectionChange:ie}=$({api:e=>Z.getUserList(e),deleteApi:e=>Z.deleteUser(e),immediate:!0}),{formRef:ue,formData:ne,rules:de,submitting:pe,submit:me,setCreateMode:ce}=G({initialData:{username:"",nickname:"",email:"",password:"",roles:[],isActive:!0},rules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],nickname:[{required:!0,message:"请输入昵称",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],roles:[{required:!0,message:"请选择角色",trigger:"change"}]},submitApi:e=>ne._id?Z.updateUser(ne._id,e):Z.createUser(e),onSuccess:()=>{ge.value=!1,re()}}),ge=p(!1),fe=m((()=>!!ne._id)),_e=p(!1),ve=p(!1),be=p(null),we=p(),he=d({newPassword:"",confirmPassword:""}),ye={newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(e,a,l)=>{a!==he.newPassword?l(new Error("两次输入的密码不一致")):l()},trigger:"blur"}]},Ve=()=>{ce(),ge.value=!0},je=e=>i(this,null,(function*(){try{yield T.confirm(`确定要删除用户"${e.username}"吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield Z.deleteUser(e._id),K.success("删除成功"),re()}catch(a){"cancel"!==a&&K.error("删除失败")}})),ke=()=>i(this,null,(function*(){yield me()})),Ce=()=>i(this,null,(function*(){if(we.value)try{yield we.value.validate(),ve.value=!0,yield Z.resetPassword(be.value._id,{newPassword:he.newPassword}),K.success("密码重置成功"),_e.value=!1}catch(e){K.error("密码重置失败")}finally{ve.value=!1}})),Pe=()=>{oe(a)},Ue=()=>{Object.assign(a,{username:"",role:"",status:""}),oe({})},xe=e=>({superadmin:"danger",admin:"warning",user:"success"}[e]||"info"),ze=e=>({superadmin:"超级管理员",admin:"管理员",user:"普通用户"}[e]||e);return(e,r)=>{const o=y,i=h,u=U,d=j,p=x,m=A,T=R,K=B,Z=Q,$=L,G=I,oe=F,me=M,ce=c("ripple"),Ae=D;return f(),g("div",J,[_("div",N,[b(m,{model:a,inline:"",class:"search-form"},{default:w((()=>[b(i,{label:"用户名"},{default:w((()=>[b(o,{modelValue:a.username,"onUpdate:modelValue":r[0]||(r[0]=e=>a.username=e),placeholder:"请输入用户名",clearable:"",onKeyup:V(Pe,["enter"])},null,8,["modelValue"])])),_:1}),b(i,{label:"角色"},{default:w((()=>[b(d,{modelValue:a.role,"onUpdate:modelValue":r[1]||(r[1]=e=>a.role=e),placeholder:"请选择角色",clearable:""},{default:w((()=>[(f(!0),g(k,null,C(l.value,(e=>(f(),P(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),b(i,{label:"状态"},{default:w((()=>[b(d,{modelValue:a.status,"onUpdate:modelValue":r[2]||(r[2]=e=>a.status=e),placeholder:"请选择状态",clearable:""},{default:w((()=>[b(u,{label:"启用",value:!0}),b(u,{label:"禁用",value:!1})])),_:1},8,["modelValue"])])),_:1}),b(i,null,{default:w((()=>[v((f(),P(p,{type:"primary",onClick:Pe},{default:w((()=>r[17]||(r[17]=[z("搜索")]))),_:1,__:[17]})),[[ce]]),v((f(),P(p,{onClick:Ue},{default:w((()=>r[18]||(r[18]=[z("重置")]))),_:1,__:[18]})),[[ce]])])),_:1})])),_:1},8,["model"])]),_("div",W,[s.value?v((f(),P(p,{key:0,type:"primary",icon:O(q),onClick:Ve},{default:w((()=>r[19]||(r[19]=[z(" 新增用户 ")]))),_:1,__:[19]},8,["icon"])),[[ce]]):S("",!0),v((f(),P(p,{onClick:O(re),loading:O(ae)},{default:w((()=>r[20]||(r[20]=[z(" 刷新 ")]))),_:1,__:[20]},8,["onClick","loading"])),[[ce]])]),v((f(),P(Z,{data:O(H),border:"",stripe:"",onSelectionChange:O(ie)},{default:w((()=>[b(T,{type:"selection",width:"55"}),b(T,{type:"index",label:"序号",width:"60",align:"center"}),b(T,{prop:"username",label:"用户名","min-width":"120"}),b(T,{prop:"nickname",label:"昵称","min-width":"120"}),b(T,{prop:"email",label:"邮箱","min-width":"150"}),b(T,{prop:"roles",label:"角色",width:"120"},{default:w((({row:e})=>[(f(!0),g(k,null,C(e.roles,(e=>(f(),P(K,{key:e,type:xe(e),size:"small",class:"role-tag"},{default:w((()=>[z(E(ze(e)),1)])),_:2},1032,["type"])))),128))])),_:1}),b(T,{prop:"isActive",label:"状态",width:"80",align:"center"},{default:w((({row:e})=>[b(K,{type:e.isActive?"success":"danger"},{default:w((()=>[z(E(e.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),b(T,{prop:"lastLoginAt",label:"最后登录",width:"150",align:"center"},{default:w((({row:e})=>{return[z(E((a=e.lastLoginAt,a?new Date(a).toLocaleString("zh-CN"):"-")),1)];var a})),_:1}),b(T,{label:"操作",width:"200",align:"center",fixed:"right"},{default:w((({row:e})=>[v((f(),P(p,{type:"primary",size:"small",onClick:a=>(e=>{Object.assign(ne,e),ge.value=!0})(e)},{default:w((()=>r[21]||(r[21]=[z(" 编辑 ")]))),_:2,__:[21]},1032,["onClick"])),[[ce]]),t.value?v((f(),P(p,{key:0,type:"warning",size:"small",onClick:a=>(e=>{be.value=e,he.newPassword="",he.confirmPassword="",_e.value=!0})(e)},{default:w((()=>r[22]||(r[22]=[z(" 重置密码 ")]))),_:2,__:[22]},1032,["onClick"])),[[ce]]):S("",!0),v((f(),P(p,{type:"danger",size:"small",onClick:a=>je(e)},{default:w((()=>r[23]||(r[23]=[z(" 删除 ")]))),_:2,__:[23]},1032,["onClick"])),[[ce]])])),_:1})])),_:1},8,["data","onSelectionChange"])),[[Ae,O(ae)]]),_("div",X,[b($,{"current-page":O(le).page,"onUpdate:currentPage":r[3]||(r[3]=e=>O(le).page=e),"page-size":O(le).pageSize,"onUpdate:pageSize":r[4]||(r[4]=e=>O(le).pageSize=e),"page-sizes":[10,20,50,100],total:O(le).total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:O(te),onCurrentChange:O(se)},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),b(me,{modelValue:ge.value,"onUpdate:modelValue":r[12]||(r[12]=e=>ge.value=e),title:fe.value?"编辑用户":"新增用户",width:"500px","close-on-click-modal":!1},{footer:w((()=>[_("div",Y,[b(p,{onClick:r[11]||(r[11]=e=>ge.value=!1)},{default:w((()=>r[26]||(r[26]=[z("取消")]))),_:1,__:[26]}),v((f(),P(p,{type:"primary",onClick:ke,loading:O(pe)},{default:w((()=>r[27]||(r[27]=[z(" 确定 ")]))),_:1,__:[27]},8,["loading"])),[[ce]])])])),default:w((()=>[b(m,{ref_key:"formRef",ref:ue,model:O(ne),rules:O(de),"label-width":"100px"},{default:w((()=>[b(i,{label:"用户名",prop:"username"},{default:w((()=>[b(o,{modelValue:O(ne).username,"onUpdate:modelValue":r[5]||(r[5]=e=>O(ne).username=e),placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),b(i,{label:"昵称",prop:"nickname"},{default:w((()=>[b(o,{modelValue:O(ne).nickname,"onUpdate:modelValue":r[6]||(r[6]=e=>O(ne).nickname=e),placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),b(i,{label:"邮箱",prop:"email"},{default:w((()=>[b(o,{modelValue:O(ne).email,"onUpdate:modelValue":r[7]||(r[7]=e=>O(ne).email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1}),fe.value?S("",!0):(f(),P(i,{key:0,label:"密码",prop:"password"},{default:w((()=>[b(o,{modelValue:O(ne).password,"onUpdate:modelValue":r[8]||(r[8]=e=>O(ne).password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])])),_:1})),b(i,{label:"角色",prop:"roles"},{default:w((()=>[b(d,{modelValue:O(ne).roles,"onUpdate:modelValue":r[9]||(r[9]=e=>O(ne).roles=e),multiple:"",placeholder:"请选择角色"},{default:w((()=>[(f(!0),g(k,null,C(n.value,(e=>(f(),P(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),b(i,{label:"状态",prop:"isActive"},{default:w((()=>[b(oe,{modelValue:O(ne).isActive,"onUpdate:modelValue":r[10]||(r[10]=e=>O(ne).isActive=e)},{default:w((()=>[b(G,{label:!0},{default:w((()=>r[24]||(r[24]=[z("启用")]))),_:1,__:[24]}),b(G,{label:!1},{default:w((()=>r[25]||(r[25]=[z("禁用")]))),_:1,__:[25]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),b(me,{modelValue:_e.value,"onUpdate:modelValue":r[16]||(r[16]=e=>_e.value=e),title:"重置密码",width:"400px","close-on-click-modal":!1},{footer:w((()=>[_("div",ee,[b(p,{onClick:r[15]||(r[15]=e=>_e.value=!1)},{default:w((()=>r[28]||(r[28]=[z("取消")]))),_:1,__:[28]}),v((f(),P(p,{type:"primary",onClick:Ce,loading:ve.value},{default:w((()=>r[29]||(r[29]=[z(" 确定 ")]))),_:1,__:[29]},8,["loading"])),[[ce]])])])),default:w((()=>[b(m,{ref_key:"passwordFormRef",ref:we,model:he,rules:ye,"label-width":"100px"},{default:w((()=>[b(i,{label:"用户名"},{default:w((()=>{var e;return[b(o,{value:null==(e=be.value)?void 0:e.username,disabled:""},null,8,["value"])]})),_:1}),b(i,{label:"新密码",prop:"newPassword"},{default:w((()=>[b(o,{modelValue:he.newPassword,"onUpdate:modelValue":r[13]||(r[13]=e=>he.newPassword=e),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])])),_:1}),b(i,{label:"确认密码",prop:"confirmPassword"},{default:w((()=>[b(o,{modelValue:he.confirmPassword,"onUpdate:modelValue":r[14]||(r[14]=e=>he.confirmPassword=e),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}}))));var le;const re=H(ae,[["__scopeId","data-v-b6e275cc"]]);export{re as default};
