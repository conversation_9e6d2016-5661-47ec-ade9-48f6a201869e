/**
 * 地区数据验证工具
 * 检查省市区数据的完整性
 */

// 地区数据（简化版本，实际使用时请使用完整的地区数据）
const areaData = []

interface AreaValidationResult {
  /** 验证是否通过 */
  isValid: boolean
  /** 总省份数 */
  totalProvinces: number
  /** 有城市数据的省份数 */
  provincesWithCities: number
  /** 缺少城市数据的省份 */
  missingCityProvinces: Array<{
    code: string
    name: string
  }>
  /** 有区县数据的城市数 */
  citiesWithDistricts: number
  /** 总城市数 */
  totalCities: number
  /** 验证详情 */
  details: {
    provinces: Array<{
      code: string
      name: string
      hasCities: boolean
      cityCount: number
      districtCount: number
    }>
  }
}

class AreaValidator {
  /**
   * 验证地区数据完整性
   */
  validateAreaData(): AreaValidationResult {
    const { provinceData, cityData, districtData } = areaData
    
    const missingCityProvinces: Array<{ code: string; name: string }> = []
    const details: AreaValidationResult['details'] = { provinces: [] }
    
    let citiesWithDistricts = 0
    let totalCities = 0
    
    // 检查每个省份
    provinceData.forEach(province => {
      const provinceCode = province.value
      const provinceName = province.label
      const cities = cityData[provinceCode] || []
      const hasCities = cities.length > 0
      
      if (!hasCities) {
        missingCityProvinces.push({
          code: provinceCode,
          name: provinceName
        })
      }
      
      // 统计该省份的区县数据
      let districtCount = 0
      cities.forEach(city => {
        totalCities++
        const districts = districtData[city.value] || []
        if (districts.length > 0) {
          citiesWithDistricts++
        }
        districtCount += districts.length
      })
      
      details.provinces.push({
        code: provinceCode,
        name: provinceName,
        hasCities,
        cityCount: cities.length,
        districtCount
      })
    })
    
    return {
      isValid: missingCityProvinces.length === 0,
      totalProvinces: provinceData.length,
      provincesWithCities: provinceData.length - missingCityProvinces.length,
      missingCityProvinces,
      citiesWithDistricts,
      totalCities,
      details
    }
  }
  
  /**
   * 生成缺失数据的模板
   */
  generateMissingDataTemplate(): string {
    const validation = this.validateAreaData()
    
    if (validation.isValid) {
      return '// 所有省份都有城市数据，无需补充'
    }
    
    let template = '// 缺失的省份城市数据模板\n'
    template += '// 请将以下代码添加到 cityData 中\n\n'
    
    validation.missingCityProvinces.forEach(province => {
      template += `'${province.code}': [\n`
      template += `  // ${province.name} 的城市数据\n`
      template += `  { value: '${province.code.substring(0, 2)}0100', label: '${province.name.replace('省', '').replace('市', '').replace('自治区', '').replace('特别行政区', '')}市' },\n`
      template += `  // 请添加更多城市...\n`
      template += `],\n\n`
    })
    
    return template
  }
  
  /**
   * 打印验证报告
   */
  printValidationReport(): void {
    const validation = this.validateAreaData()

    // 验证报告已禁用输出
    // 数据验证结果可通过 validateAreaData() 方法获取
  }
  
  /**
   * 检查特定省份的数据
   */
  checkProvince(provinceCode: string): {
    exists: boolean
    name?: string
    cities: number
    districts: number
    cityList?: Array<{ code: string; name: string; districts: number }>
  } {
    const { provinceData, cityData, districtData } = areaData
    
    const province = provinceData.find(p => p.value === provinceCode)
    if (!province) {
      return { exists: false, cities: 0, districts: 0 }
    }
    
    const cities = cityData[provinceCode] || []
    let totalDistricts = 0
    const cityList = cities.map(city => {
      const districts = districtData[city.value] || []
      totalDistricts += districts.length
      return {
        code: city.value,
        name: city.label,
        districts: districts.length
      }
    })
    
    return {
      exists: true,
      name: province.label,
      cities: cities.length,
      districts: totalDistricts,
      cityList
    }
  }
  
  /**
   * 获取数据统计
   */
  getStatistics() {
    const validation = this.validateAreaData()
    
    return {
      provinces: validation.totalProvinces,
      cities: validation.totalCities,
      districts: validation.citiesWithDistricts,
      coverage: {
        city: (validation.provincesWithCities / validation.totalProvinces * 100).toFixed(1) + '%',
        district: validation.totalCities > 0 ? (validation.citiesWithDistricts / validation.totalCities * 100).toFixed(1) + '%' : '0%'
      },
      missing: validation.missingCityProvinces.length
    }
  }
}

// 创建全局实例
export const areaValidator = new AreaValidator()

// 便捷方法
export const validateAreaData = () => areaValidator.validateAreaData()
export const printAreaReport = () => areaValidator.printValidationReport()
export const checkProvince = (code: string) => areaValidator.checkProvince(code)
export const getAreaStatistics = () => areaValidator.getStatistics()

export default AreaValidator
