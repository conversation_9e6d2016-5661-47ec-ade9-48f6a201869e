<template>
  <div class="config-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">配置管理</h2>
      <p class="page-description">管理套餐类型、随心换选项、内存卡等配置信息</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="config-layout">
      <!-- 侧边导航 -->
      <div class="config-sidebar">
        <div class="sidebar-header">
          <h3>配置分类</h3>
        </div>
        <nav class="sidebar-nav">
          <div v-for="category in categories" :key="category.key" class="nav-item"
            :class="{ active: activeTab === category.key }" @click="activeTab = category.key">
            <div class="nav-icon" :style="{ backgroundColor: category.bgColor, color: category.color }">
              <span class="icon-emoji">{{ category.icon }}</span>
            </div>
            <div class="nav-content">
              <span class="nav-title">{{ category.title }}</span>
              <span class="nav-count">{{ category.count }}</span>
            </div>
            <div class="nav-indicator"></div>
          </div>
        </nav>
      </div>

      <!-- 内容区域 -->
      <div class="config-main">
        <!-- 移动端滑动标题区域 -->
        <div class="mobile-swipe-header" v-if="isMobile">
          <div class="swipe-container" ref="swipeContainer" @touchstart="handleTouchStart" @touchmove="handleTouchMove"
            @touchend="handleTouchEnd">
            <div class="swipe-wrapper" :style="{ transform: `translateX(${swipeOffset}px)` }">
              <div v-for="(category, index) in categories" :key="category.key" class="swipe-item"
                :class="{ active: activeTab === category.key }" @click="handleSwipeItemClick(category.key, index)">
                <div class="swipe-item-content">
                  <div class="swipe-icon" :style="{ backgroundColor: category.bgColor, color: category.color }">
                    <span class="icon-emoji">{{ category.icon }}</span>
                  </div>
                  <div class="swipe-content">
                    <h3 class="swipe-title">{{ category.title }}</h3>
                    <p class="swipe-description">{{ category.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="swipe-indicators">
            <span v-for="(category, index) in categories" :key="category.key" class="indicator"
              :class="{ active: activeTab === category.key }" @click="handleSwipeItemClick(category.key, index)"></span>
          </div>
        </div>

        <!-- PC端标题区域 -->
        <div class="content-header" v-if="!isMobile">
          <h2 class="content-title">{{ getCurrentCategory?.title }}</h2>
          <p class="content-description">{{ getCurrentCategory?.description }}</p>
        </div>

        <div class="content-body">
          <PackageTypeManager v-if="activeTab === 'package'" :key="`package-${isMobile}`"
            @data-changed="() => refreshCategoryCount('package')" />
          <ExchangeOptionManager v-if="activeTab === 'exchange'" :key="`exchange-${isMobile}`"
            @data-changed="() => refreshCategoryCount('exchange')" />
          <MemoryCardManager v-if="activeTab === 'memory'" :key="`memory-${isMobile}`"
            @data-changed="() => refreshCategoryCount('memory')" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import PackageTypeManager from './components/PackageTypeManager.vue'
import ExchangeOptionManager from './components/ExchangeOptionManager.vue'
import MemoryCardManager from './components/MemoryCardManager.vue'
import { PackageTypesService } from '@/api/packageTypesApi'
import { ChangeOptionsService } from '@/api/changeOptionsApi'
import { MemoryCardsService } from '@/api/memoryCardsApi'

defineOptions({ name: 'ConfigManagement' })

// 当前激活的选项卡
const activeTab = ref('package')

// 移动端检测
const isMobile = ref(false)

// 滑动相关
const swipeContainer = ref<HTMLElement>()
const swipeOffset = ref(0)
const currentIndex = ref(0)

// 触摸滑动相关
const touchStartX = ref(0)
const touchStartY = ref(0)
const isDragging = ref(false)

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 处理滑动项点击
const handleSwipeItemClick = (key: string, index: number) => {
  activeTab.value = key
  currentIndex.value = index
  updateSwipePosition()
}

// 监听activeTab变化，同步更新currentIndex
watch(activeTab, (newTab) => {
  if (isMobile.value) {
    const activeIndex = categories.value.findIndex(cat => cat.key === newTab)
    if (activeIndex !== -1 && activeIndex !== currentIndex.value) {
      currentIndex.value = activeIndex
      nextTick(() => {
        updateSwipePosition()
      })
    }
  }
})

// 更新滑动位置
const updateSwipePosition = () => {
  if (!swipeContainer.value) return

  // 使用视口宽度，与CSS中的100vw保持一致
  const viewportWidth = window.innerWidth

  // 计算偏移量：每次移动一个完整的视口宽度
  const offset = -currentIndex.value * viewportWidth

  // 计算边界限制
  const maxOffset = 0 // 第一个项目的位置
  const minOffset = -(categories.value.length - 1) * viewportWidth // 最后一个项目的位置

  // 应用边界限制，确保不会滑动过头
  swipeOffset.value = Math.max(Math.min(offset, maxOffset), minOffset)
}

// 触摸开始
const handleTouchStart = (e: TouchEvent) => {
  touchStartX.value = e.touches[0].clientX
  touchStartY.value = e.touches[0].clientY
  isDragging.value = false
}

// 触摸移动
const handleTouchMove = (e: TouchEvent) => {
  if (!isDragging.value) {
    const deltaX = Math.abs(e.touches[0].clientX - touchStartX.value)
    const deltaY = Math.abs(e.touches[0].clientY - touchStartY.value)

    // 如果水平滑动距离大于垂直滑动距离，则开始拖拽
    if (deltaX > deltaY && deltaX > 10) {
      isDragging.value = true
      e.preventDefault()
    }
  }
}

// 触摸结束
const handleTouchEnd = (e: TouchEvent) => {
  if (!isDragging.value) return

  const deltaX = e.changedTouches[0].clientX - touchStartX.value
  const threshold = 50 // 滑动阈值

  if (Math.abs(deltaX) > threshold) {
    if (deltaX > 0 && currentIndex.value > 0) {
      // 向右滑动，切换到上一个
      currentIndex.value--
      activeTab.value = categories.value[currentIndex.value].key
      updateSwipePosition()
    } else if (deltaX < 0 && currentIndex.value < categories.value.length - 1) {
      // 向左滑动，切换到下一个
      currentIndex.value++
      activeTab.value = categories.value[currentIndex.value].key
      updateSwipePosition()
    }
  }

  isDragging.value = false
}

// 分类配置
const categories = ref([
  {
    key: 'package',
    title: '套餐类型',
    description: '管理各种套餐类型的配置信息',
    icon: '📦',
    color: '#667eea',
    bgColor: '#eff6ff',
    count: 0
  },
  {
    key: 'exchange',
    title: '随心换选项',
    description: '管理随心换服务的配置选项',
    icon: '🔄',
    color: '#10b981',
    bgColor: '#ecfdf5',
    count: 0
  },
  {
    key: 'memory',
    title: '内存卡管理',
    description: '管理内存卡相关的配置信息',
    icon: '💾',
    color: '#f59e0b',
    bgColor: '#fffbeb',
    count: 0
  }
])

// 获取当前分类信息
const getCurrentCategory = computed(() => {
  return categories.value.find(cat => cat.key === activeTab.value)
})

// 加载数据数量
const loadDataCounts = async () => {
  try {
    // 并行获取所有数据
    const [packageTypes, changeOptions, memoryCards] = await Promise.all([
      PackageTypesService.getPackageTypes().catch(() => []),
      ChangeOptionsService.getChangeOptions().catch(() => []),
      MemoryCardsService.getMemoryCards().catch(() => [])
    ])

    // 更新数量
    const packageCategory = categories.value.find(cat => cat.key === 'package')
    if (packageCategory) {
      packageCategory.count = packageTypes.length
    }

    const exchangeCategory = categories.value.find(cat => cat.key === 'exchange')
    if (exchangeCategory) {
      exchangeCategory.count = changeOptions.length
    }

    const memoryCategory = categories.value.find(cat => cat.key === 'memory')
    if (memoryCategory) {
      memoryCategory.count = memoryCards.length
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 刷新指定分类的数据数量
const refreshCategoryCount = async (categoryKey: string) => {
  try {
    let count = 0

    switch (categoryKey) {
      case 'package':
        const packageTypes = await PackageTypesService.getPackageTypes()
        count = packageTypes.length
        break
      case 'exchange':
        const changeOptions = await ChangeOptionsService.getChangeOptions()
        count = changeOptions.length
        break
      case 'memory':
        const memoryCards = await MemoryCardsService.getMemoryCards()
        count = memoryCards.length
        break
    }

    const category = categories.value.find(cat => cat.key === categoryKey)
    if (category) {
      category.count = count
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 暴露方法给子组件使用
const refreshCurrentCategoryCount = () => {
  refreshCategoryCount(activeTab.value)
}

// 组件挂载时加载数据
onMounted(() => {
  loadDataCounts()

  // 初始化移动端检测
  checkMobile()

  // 创建防抖的窗口大小变化处理函数
  const debouncedResize = debounce(() => {
    checkMobile()
    if (isMobile.value) {
      // 延迟更新，确保DOM已经重新渲染
      nextTick(() => {
        setTimeout(() => {
          updateSwipePosition()
        }, 50)
      })
    }
  }, 100)

  // 监听窗口大小变化
  window.addEventListener('resize', debouncedResize)

  // 初始化滑动位置
  if (isMobile.value) {
    nextTick(() => {
      setTimeout(() => {
        // 根据当前activeTab设置正确的currentIndex
        const activeIndex = categories.value.findIndex(cat => cat.key === activeTab.value)
        if (activeIndex !== -1) {
          currentIndex.value = activeIndex
        }
        updateSwipePosition()
      }, 150)
    })
  }
})

// 暴露方法
defineExpose({
  refreshCategoryCount,
  refreshCurrentCategoryCount,
  loadDataCounts
})
</script>

<style lang="scss" scoped>
.config-management {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 24px;
    text-align: center;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 8px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-description {
      font-size: 14px;
      color: #6b7280;
      margin: 0;
    }
  }

  .config-layout {
    display: flex;
    gap: 24px;
    flex: 1;
    min-height: 0;
  }

  .config-sidebar {
    width: 280px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-radius: 16px;
    border: 1px solid #2563eb;
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);

    .sidebar-header {
      padding: 24px 24px 20px;
      background: rgba(0, 0, 0, 0.1);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: white;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }
    }

    .sidebar-nav {
      padding: 12px 16px;

      .nav-item {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        border-radius: 12px;
        margin-bottom: 8px;
        background: transparent;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          transform: translateX(4px);
        }

        &.active {
          background: rgba(255, 255, 255, 0.25);
          backdrop-filter: blur(10px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          transform: translateX(8px);
          border: 1px solid rgba(255, 255, 255, 0.3);

          .nav-icon {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }

          .nav-title {
            color: white;
            font-weight: 700;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
          }

          .nav-count {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
          }
        }

        .nav-icon {
          width: 44px;
          height: 44px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          transition: all 0.3s ease;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

          .icon-emoji {
            font-size: 20px;
          }
        }

        .nav-content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .nav-title {
            font-size: 15px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
          }

          .nav-count {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 11px;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 16px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
  }

  .config-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;

    .content-header {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 16px;
      border: 1px solid #e2e8f0;
      padding: 28px;
      margin-bottom: 24px;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #2563eb 100%);
      }

      .content-title {
        font-size: 22px;
        font-weight: 700;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0 0 12px 0;
      }

      .content-description {
        font-size: 15px;
        color: #64748b;
        margin: 0;
        line-height: 1.6;
      }
    }

    .content-body {
      flex: 1;
      min-height: 0;
    }
  }
}

// 移动端滑动标题样式
.mobile-swipe-header {
  margin-bottom: 20px;

  .swipe-container {
    position: relative;
    overflow: hidden;
    padding: 16px 0;
    width: 100%;
    touch-action: pan-x;
  }

  .swipe-wrapper {
    display: flex;
    transition: transform 0.3s ease;
    gap: 0;
    padding: 0;
    align-items: stretch;
    will-change: transform;
  }

  .swipe-item {
    flex-shrink: 0;
    width: 100vw;
    min-height: 120px;
    height: 120px;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .swipe-item-content {
    width: 100%;
    max-width: 320px;
    min-height: 120px;
    height: 120px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    padding: 20px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.08),
      0 1px 3px rgba(0, 0, 0, 0.1);



    &.active .swipe-item-content {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #2563eb 100%);
      border-color: rgba(255, 255, 255, 0.2);
      box-shadow:
        0 8px 32px rgba(59, 130, 246, 0.25),
        0 4px 16px rgba(59, 130, 246, 0.15);
      transform: translateY(-3px) scale(1.01);

      .swipe-title {
        color: white;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .swipe-description {
        color: rgba(255, 255, 255, 0.9);
      }

      .swipe-icon {
        background: rgba(255, 255, 255, 0.15) !important;
        color: white !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: scale(1.05);

        .icon-emoji {
          filter: brightness(1.1);
        }
      }
    }

    &:hover:not(.active) .swipe-item-content {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 30%, #e2e8f0 100%);
      border-color: #cbd5e1;
      box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06);
      transform: translateY(-2px);

      .swipe-icon {
        transform: scale(1.03);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }
    }
  }



  .swipe-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 3;

    .icon-emoji {
      font-size: 20px;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }
  }

  .swipe-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 2;
    position: relative;

    .swipe-title {
      font-size: 16px;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 6px 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      line-height: 1.2;
      letter-spacing: -0.01em;
    }

    .swipe-description {
      font-size: 13px;
      color: #64748b;
      margin: 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      transition: all 0.3s ease;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .swipe-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 16px;

    .indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #cbd5e1;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #3b82f6;
        transform: scale(1.2);
      }

      &:hover:not(.active) {
        background: #94a3b8;
      }
    }
  }
}

@media (max-width: 768px) {
  .config-management {
    padding: 16px;
    height: calc(100vh - 100px);

    .page-header {
      margin-bottom: 16px;

      .page-title {
        font-size: 20px;
      }

      .page-description {
        font-size: 13px;
      }
    }

    .config-layout {
      flex-direction: column;
      gap: 16px;
    }

    .config-sidebar {
      display: none; // 移动端隐藏配置分类侧边栏
    }

    .nav-icon {
      width: 32px;
      height: 32px;
      margin-right: 10px;

      .icon-emoji {
        font-size: 16px;
      }
    }

    .nav-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;

      .nav-title {
        font-size: 13px;
        color: white;
        font-weight: 600;
      }

      .nav-count {
        font-size: 10px;
        padding: 2px 8px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
    }

    .config-main {
      order: 1;

      .content-header {
        padding: 16px;
        margin-bottom: 16px;

        .content-title {
          font-size: 18px;
        }

        .content-description {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
