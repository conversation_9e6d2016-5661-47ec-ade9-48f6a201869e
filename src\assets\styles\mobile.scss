// 移动端样式处理

// 去除移动端点击背景色和优化触摸体验
@media screen and (max-width: $device-ipad-pro) {
  * {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
  }

  // 优化按钮和可点击元素
  button,
  .el-button,
  [role="button"],
  .clickable,
  .el-menu-item,
  .el-submenu__title {
    cursor: pointer !important;
    touch-action: manipulation !important;
    -webkit-user-select: none !important;
    user-select: none !important;

    // 防止长按菜单
    -webkit-touch-callout: none !important;

    // 只对非表格操作按钮应用最小高度
    &:not(.action-btn):not(.el-table .el-button) {
      min-height: 44px;
    }
  }

  // 优化输入框
  input,
  textarea,
  select,
  .el-input__inner,
  .el-textarea__inner {
    // 防止输入时页面缩放
    font-size: 16px !important;

    // 移除默认样式
    -webkit-appearance: none !important;
    appearance: none !important;

    // 防止iOS默认圆角
    border-radius: 0 !important;
  }

  // 优化滚动性能
  .scrollable,
  .el-scrollbar__wrap,
  .el-table__body-wrapper,
  .overflow-auto,
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch !important;
    overflow-scrolling: touch !important;

    // 防止滚动边界效果
    overscroll-behavior: contain !important;
  }

  // 防止图片被选中和拖拽
  img {
    -webkit-user-select: none !important;
    user-select: none !important;
    -webkit-user-drag: none !important;
    user-drag: none !important;
    -webkit-touch-callout: none !important;
  }

  // 优化链接点击
  a {
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
  }

  // 修复flex布局在移动端的问题
  .el-row,
  .el-col,
  .flex,
  .d-flex {
    min-height: 0 !important;
    min-width: 0 !important;
  }
}

// 专门针对iOS设备的修复
@supports (-webkit-touch-callout: none) {
  // 修复iOS Safari的特殊问题
  .el-dialog__wrapper,
  .el-drawer__wrapper,
  .el-overlay {
    -webkit-overflow-scrolling: touch !important;
  }

  // 修复iOS上的渲染问题
  .el-card,
  .art-custom-card,
  .page-content {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }

  // 修复iOS上的表格滚动
  .el-table .el-table__body-wrapper {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
  }
}

// 修复iOS 15+的视口问题
@supports (height: 100dvh) {
  .full-height,
  .min-h-screen {
    height: 100dvh !important;
    min-height: 100dvh !important;
  }
}

// 高分辨率屏幕字体优化
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  * {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }
}
