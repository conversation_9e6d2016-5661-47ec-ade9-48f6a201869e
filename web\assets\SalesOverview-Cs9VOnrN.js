import{_ as s}from"./index-CAIJlOgO.js";import{k as a,O as t,C as e,bp as r,x as i}from"./vendor-9ydHGNSq.js";import{_ as c}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./index-DEP0vMzR.js";import"./useChart-DM-2b2dH.js";const o={class:"card art-custom-card"},d=c(a({__name:"SalesOverview",setup(a){const c=[50,25,40,20,70,35,65,30,35,20,40,44],d=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];return(a,p)=>{const l=s;return e(),t("div",o,[p[0]||(p[0]=r('<div class="card-header" data-v-76c3f970><div class="title" data-v-76c3f970><h4 class="box-title" data-v-76c3f970>访问量</h4><p class="subtitle" data-v-76c3f970>今年增长<span class="text-success" data-v-76c3f970>+15%</span></p></div></div>',1)),i(l,{class:"chart",height:"calc(100% - 40px)",data:c,xAxisData:d,showAreaColor:!0,showAxisLine:!1})])}}}),[["__scopeId","data-v-76c3f970"]]);export{d as default};
