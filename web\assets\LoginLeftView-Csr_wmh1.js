import{_ as s}from"./index-B5Hr4D84.js";import{b as e}from"./lf_icon2-BooRsIsW.js";import{A as l}from"./index-BOCMSBcY.js";import{k as t,O as i,C as o,S as a,x as n,X as r,u as m}from"./vendor-9ydHGNSq.js";import{_ as f}from"./_plugin-vue_export-helper-BCo6x5W8.js";const p=""+new URL("lf_bg-DnUoi7WV.webp",import.meta.url).href,c={class:"login-left-view"},g={class:"logo"},u={class:"title"},_={class:"text-wrap"},d=f(t({__name:"LoginLeftView",setup:t=>(t,f)=>{const d=s;return o(),i("div",c,[a("div",g,[n(d,{class:"icon"}),a("h1",u,r(m(l).systemInfo.name),1)]),f[0]||(f[0]=a("img",{class:"left-bg",src:p},null,-1)),f[1]||(f[1]=a("img",{class:"left-img",src:e},null,-1)),a("div",_,[a("h1",null,r(t.$t("login.leftView.title")),1),a("p",null,r(t.$t("login.leftView.subTitle")),1)])])}}),[["__scopeId","data-v-abe1e359"]]);export{d as _};
