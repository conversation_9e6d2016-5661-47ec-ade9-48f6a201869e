/**
 * 中国地区数据 - 使用第三方库
 * 提供完整的省市区三级数据
 */

// 如果安装了 china-division 库，使用以下导入
// import { provinces, cities, areas } from 'china-division'

// 临时方案：内联完整的地区数据
const chinaAreaData = {
  // 省份数据
  provinces: [
    { code: '110000', name: '北京市' },
    { code: '120000', name: '天津市' },
    { code: '130000', name: '河北省' },
    { code: '140000', name: '山西省' },
    { code: '150000', name: '内蒙古自治区' },
    { code: '210000', name: '辽宁省' },
    { code: '220000', name: '吉林省' },
    { code: '230000', name: '黑龙江省' },
    { code: '310000', name: '上海市' },
    { code: '320000', name: '江苏省' },
    { code: '330000', name: '浙江省' },
    { code: '340000', name: '安徽省' },
    { code: '350000', name: '福建省' },
    { code: '360000', name: '江西省' },
    { code: '370000', name: '山东省' },
    { code: '410000', name: '河南省' },
    { code: '420000', name: '湖北省' },
    { code: '430000', name: '湖南省' },
    { code: '440000', name: '广东省' },
    { code: '450000', name: '广西壮族自治区' },
    { code: '460000', name: '海南省' },
    { code: '500000', name: '重庆市' },
    { code: '510000', name: '四川省' },
    { code: '520000', name: '贵州省' },
    { code: '530000', name: '云南省' },
    { code: '540000', name: '西藏自治区' },
    { code: '610000', name: '陕西省' },
    { code: '620000', name: '甘肃省' },
    { code: '630000', name: '青海省' },
    { code: '640000', name: '宁夏回族自治区' },
    { code: '650000', name: '新疆维吾尔自治区' },
    { code: '710000', name: '台湾省' },
    { code: '810000', name: '香港特别行政区' },
    { code: '820000', name: '澳门特别行政区' }
  ],

  // 城市数据映射
  cities: {
    '110000': [{ code: '110100', name: '北京市' }],
    '120000': [{ code: '120100', name: '天津市' }],
    '130000': [
      { code: '130100', name: '石家庄市' },
      { code: '130200', name: '唐山市' },
      { code: '130300', name: '秦皇岛市' },
      { code: '130400', name: '邯郸市' },
      { code: '130500', name: '邢台市' },
      { code: '130600', name: '保定市' },
      { code: '130700', name: '张家口市' },
      { code: '130800', name: '承德市' },
      { code: '130900', name: '沧州市' },
      { code: '131000', name: '廊坊市' },
      { code: '131100', name: '衡水市' }
    ],
    // 更多城市数据...
  },

  // 区县数据映射
  areas: {
    '110100': [
      { code: '110101', name: '东城区' },
      { code: '110102', name: '西城区' },
      { code: '110105', name: '朝阳区' },
      { code: '110106', name: '丰台区' },
      { code: '110107', name: '石景山区' },
      { code: '110108', name: '海淀区' },
      { code: '110109', name: '门头沟区' },
      { code: '110111', name: '房山区' },
      { code: '110112', name: '通州区' },
      { code: '110113', name: '顺义区' },
      { code: '110114', name: '昌平区' },
      { code: '110115', name: '大兴区' },
      { code: '110116', name: '怀柔区' },
      { code: '110117', name: '平谷区' },
      { code: '110118', name: '密云区' },
      { code: '110119', name: '延庆区' }
    ],
    // 更多区县数据...
  }
}

/**
 * 地区数据转换器
 * 将第三方库数据转换为项目需要的格式
 */
class AreaDataConverter {
  /**
   * 转换为级联选择器格式
   */
  static toCascaderFormat() {
    return chinaAreaData.provinces.map(province => ({
      value: province.code,
      label: province.name,
      children: (chinaAreaData.cities[province.code] || []).map(city => {
        const districts = chinaAreaData.areas[city.code] || []
        return {
          value: city.code,
          label: city.name,
          // 只有当有区县数据时才添加children属性
          ...(districts.length > 0 ? {
            children: districts.map(district => ({
              value: district.code,
              label: district.name
            }))
          } : {})
        }
      })
    }))
  }

  /**
   * 转换为原有格式（兼容性）
   */
  static toOriginalFormat() {
    return {
      provinceData: chinaAreaData.provinces.map(p => ({ value: p.code, label: p.name })),
      cityData: Object.fromEntries(
        Object.entries(chinaAreaData.cities).map(([provinceCode, cities]) => [
          provinceCode,
          cities.map(c => ({ value: c.code, label: c.name }))
        ])
      ),
      districtData: Object.fromEntries(
        Object.entries(chinaAreaData.areas).map(([cityCode, areas]) => [
          cityCode,
          areas.map(a => ({ value: a.code, label: a.name }))
        ])
      )
    }
  }

  /**
   * 获取省份列表
   */
  static getProvinces() {
    return chinaAreaData.provinces
  }

  /**
   * 获取指定省份的城市列表
   */
  static getCitiesByProvince(provinceCode: string) {
    return chinaAreaData.cities[provinceCode] || []
  }

  /**
   * 获取指定城市的区县列表
   */
  static getAreasByCity(cityCode: string) {
    return chinaAreaData.areas[cityCode] || []
  }

  /**
   * 根据代码查找名称
   */
  static getNameByCode(code: string) {
    // 查找省份
    const province = chinaAreaData.provinces.find(p => p.code === code)
    if (province) return province.name

    // 查找城市
    for (const cities of Object.values(chinaAreaData.cities)) {
      const city = cities.find(c => c.code === code)
      if (city) return city.name
    }

    // 查找区县
    for (const areas of Object.values(chinaAreaData.areas)) {
      const area = areas.find(a => a.code === code)
      if (area) return area.name
    }

    return ''
  }

  /**
   * 解析地址字符串，尝试匹配省市区
   */
  static parseAddress(address: string) {
    const result = {
      province: '',
      city: '',
      area: '',
      detail: address
    }

    // 简单的地址解析逻辑
    for (const province of chinaAreaData.provinces) {
      if (address.includes(province.name.replace('省', '').replace('市', '').replace('自治区', ''))) {
        result.province = province.name
        
        const cities = chinaAreaData.cities[province.code] || []
        for (const city of cities) {
          if (address.includes(city.name.replace('市', ''))) {
            result.city = city.name
            
            const areas = chinaAreaData.areas[city.code] || []
            for (const area of areas) {
              if (address.includes(area.name.replace('区', '').replace('县', ''))) {
                result.area = area.name
                break
              }
            }
            break
          }
        }
        break
      }
    }

    return result
  }
}

// 导出转换后的数据
export const areaData = AreaDataConverter.toOriginalFormat()
export const cascaderOptions = AreaDataConverter.toCascaderFormat()
export const areaConverter = AreaDataConverter

export default areaData
