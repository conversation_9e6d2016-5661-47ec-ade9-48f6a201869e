var e=(e,a,t)=>new Promise(((l,s)=>{var i=e=>{try{n(t.next(e))}catch(a){s(a)}},c=e=>{try{n(t.throw(e))}catch(a){s(a)}},n=e=>e.done?l(e.value):Promise.resolve(e.value).then(i,c);n((t=t.apply(e,a)).next())}));import{q as a}from"./index-BOCMSBcY.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css                         */import{_ as t}from"./ArtTable-ClHSxhXb.js";/* empty css                  *//* empty css                        *//* empty css                    *//* empty css                  */import{k as l,r as s,c as i,d as c,p as n,V as r,O as o,C as d,R as u,a2 as p,x as m,B as y,a3 as f,D as v,S as g,Y as h,u as _,aH as k,W as b,aI as w,aT as x,X as j,aG as T,aJ as P,aK as C,a6 as V,F as A,Z as z,aL as D,aM as O,aN as U,aO as q,aP as B,ab as M,ac as S,$,ak as R,aQ as E,aR as F,ae as I,E as L,aD as W}from"./vendor-9ydHGNSq.js";import{u as X,a as Z}from"./useForm-DsFKgpsv.js";import{u as G}from"./useDebounce-D5CjXTjR.js";import"./index-DEP0vMzR.js";import{_ as H}from"./_plugin-vue_export-helper-BCo6x5W8.js";class J{static getPackageTypes(t){return e(this,null,(function*(){try{return(yield a.get({url:"/package-types",params:t}))||[]}catch(e){throw e}}))}static getActivePackageTypes(){return e(this,null,(function*(){return this.getPackageTypes({isActive:!0})}))}static getPackageTypeById(t){return e(this,null,(function*(){try{return yield a.get({url:`/package-types/${t}`})}catch(e){throw e}}))}static searchPackageTypes(a){return e(this,null,(function*(){return this.getPackageTypes({keyword:a})}))}static createPackageType(t){return e(this,null,(function*(){try{return yield a.post({url:"/package-types",data:t})}catch(e){throw e}}))}static updatePackageType(t,l){return e(this,null,(function*(){try{return yield a.put({url:`/package-types/${t}`,data:l})}catch(e){throw e}}))}static deletePackageType(t){return e(this,null,(function*(){try{yield a.del({url:`/package-types/${t}`})}catch(e){throw e}}))}}const K={class:"package-type-manager"},N={key:0,class:"action-bar"},Q={key:1,class:"mobile-header"},Y={class:"mobile-stats-card"},ee={class:"stats-icon"},ae={class:"stats-content"},te={class:"stats-number"},le={class:"price-text"},se={key:3,class:"mobile-card-list"},ie={key:0,class:"mobile-skeleton"},ce={class:"skeleton-header"},ne={class:"skeleton-content"},re={class:"card-header-mobile"},oe={class:"package-info"},de={class:"package-icon"},ue={class:"package-details"},pe={class:"package-name"},me={class:"package-index"},ye={class:"package-status"},fe={class:"package-content"},ve={class:"info-section"},ge={class:"info-item"},he={class:"info-value price-value"},_e={key:0,class:"info-item description-item"},ke={class:"info-value description-text"},be={class:"card-footer"},we={class:"action-buttons-mobile"},xe={key:2,class:"empty-state"},je={key:4,class:"mobile-action-bar"},Te={class:"action-bar-container"},Pe={class:"action-icon add-icon"},Ce={class:"action-icon refresh-icon"},Ve={class:"dialog-footer"},Ae=H(l({__name:"PackageTypeManager",emits:["data-changed"],setup(a,{emit:l}){const H=l,Ae=s(!1),ze=()=>{Ae.value=window.innerWidth<=768},De=s(!1),Oe=()=>e(this,null,(function*(){if(!De.value){De.value=!0;try{yield Me(),H("data-changed")}catch(e){}finally{setTimeout((()=>{De.value=!1}),1e3)}}})),Ue=G((()=>e(this,null,(function*(){try{yield Me(),H("data-changed")}catch(e){}}))),{delay:1500}),{tableData:qe,loading:Be,refresh:Me}=X({api:()=>e(this,null,(function*(){const e=yield J.getPackageTypes();return{data:e,total:e.length}})),deleteApi:e=>J.deletePackageType(e),immediate:!0,apiOptions:{noThrottle:!0}}),{formRef:Se,formData:$e,rules:Re,submitting:Ee,submit:Fe,setCreateMode:Ie}=Z({initialData:{name:"",description:"",price:0,isActive:!0},rules:{name:[{required:!0,message:"请输入套餐名称",trigger:"blur"}],description:[{required:!0,message:"请输入描述",trigger:"blur"}],price:[{required:!0,message:"请输入价格",trigger:"blur"}]},submitApi:e=>$e._id?J.updatePackageType($e._id,e):J.createPackageType(e),onSuccess:()=>{L.success(We.value?"更新成功":"创建成功"),Le.value=!1,Ue()},showSuccessMessage:!1}),Le=s(!1),We=i((()=>!!$e._id)),Xe=()=>{Ie(),Le.value=!0},Ze=e=>{Object.assign($e,e),Le.value=!0},Ge=a=>e(this,null,(function*(){try{yield W.confirm(`确定要删除套餐类型"${a.name}"吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield J.deletePackageType(a._id),L.success("删除成功"),Ue()}catch(e){"cancel"!==e&&L.error("删除失败")}})),He=()=>e(this,null,(function*(){yield Fe()}));return c((()=>{ze(),window.addEventListener("resize",ze)})),n((()=>{window.removeEventListener("resize",ze)})),(e,a)=>{const l=h,s=f,i=P,c=C,n=t,L=D,W=O,X=$,Z=S,G=R,H=F,J=E,ze=M,Ue=I,Me=r("ripple"),Fe=T;return d(),o("div",K,[Ae.value?u("",!0):(d(),o("div",N,[p((d(),y(s,{type:"primary",size:"default",onClick:Xe,class:"add-button"},{default:v((()=>[m(l,{class:"el-icon--left"},{default:v((()=>[m(_(k))])),_:1}),a[6]||(a[6]=g("span",{class:"btn-text"},"新增套餐",-1))])),_:1,__:[6]})),[[Me]]),p((d(),y(s,{class:b(["refresh-button",{refreshing:De.value}]),onClick:Oe,disabled:De.value,circle:""},{default:v((()=>[m(l,null,{default:v((()=>[m(_(w))])),_:1})])),_:1},8,["class","disabled"])),[[Me]])])),Ae.value?(d(),o("div",Q,[g("div",Y,[g("div",ee,[m(l,null,{default:v((()=>[m(_(x))])),_:1})]),g("div",ae,[g("div",te,j(_(qe).length),1),a[7]||(a[7]=g("div",{class:"stats-label"},"套餐配置",-1))])])])):u("",!0),Ae.value?u("",!0):p((d(),y(n,{key:2,data:_(qe),style:{"margin-top":"10px"}},{default:v((()=>[m(i,{type:"index",label:"序号",width:"60",align:"center"}),m(i,{prop:"name",label:"套餐名称","min-width":"120"}),m(i,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),m(i,{prop:"price",label:"价格",width:"100",align:"center"},{default:v((({row:e})=>[g("span",le,"¥"+j(e.price),1)])),_:1}),m(i,{prop:"isActive",label:"状态",width:"80",align:"center"},{default:v((({row:e})=>[m(c,{type:e.isActive?"success":"danger"},{default:v((()=>[V(j(e.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),m(i,{label:"操作",width:"150",align:"center",fixed:"right"},{default:v((({row:e})=>[p((d(),y(s,{type:"primary",size:"small",onClick:a=>Ze(e)},{default:v((()=>a[8]||(a[8]=[V(" 编辑 ")]))),_:2,__:[8]},1032,["onClick"])),[[Me]]),p((d(),y(s,{type:"danger",size:"small",onClick:a=>Ge(e)},{default:v((()=>a[9]||(a[9]=[V(" 删除 ")]))),_:2,__:[9]},1032,["onClick"])),[[Me]])])),_:1})])),_:1},8,["data"])),[[Fe,_(Be)]]),Ae.value?(d(),o("div",se,[_(Be)?(d(),o("div",ie,[(d(),o(A,null,z(3,(e=>g("div",{key:e,class:"mobile-skeleton-card"},[m(W,{animated:""},{template:v((()=>[g("div",ce,[m(L,{variant:"circle",style:{width:"40px",height:"40px"}}),g("div",ne,[m(L,{variant:"text",style:{width:"60%",height:"16px"}}),m(L,{variant:"text",style:{width:"40%",height:"14px"}})])]),m(L,{variant:"rect",style:{width:"100%",height:"60px","margin-top":"12px"}})])),_:1})]))),64))])):u("",!0),_(Be)?u("",!0):(d(),y(U,{key:1,name:"card-list",tag:"div",class:"card-container"},{default:v((()=>[(d(!0),o(A,null,z(_(qe),((e,t)=>(d(),o("div",{key:e.id,class:"package-card"},[g("div",re,[g("div",oe,[g("div",de,[m(l,null,{default:v((()=>[m(_(x))])),_:1})]),g("div",ue,[g("div",pe,j(e.name),1),g("div",me,"序号: "+j(t+1),1)])]),g("div",ye,[m(c,{type:e.isActive?"success":"danger",size:"small"},{default:v((()=>[V(j(e.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])]),g("div",fe,[g("div",ve,[g("div",ge,[a[10]||(a[10]=g("span",{class:"info-label"},"价格",-1)),g("span",he,"¥"+j(e.price),1)]),e.description?(d(),o("div",_e,[a[11]||(a[11]=g("span",{class:"info-label"},"描述",-1)),g("span",ke,j(e.description),1)])):u("",!0)])]),g("div",be,[g("div",we,[m(s,{size:"small",type:"primary",plain:"",onClick:a=>Ze(e)},{default:v((()=>[m(l,null,{default:v((()=>[m(_(q))])),_:1}),a[12]||(a[12]=g("span",null,"编辑",-1))])),_:2,__:[12]},1032,["onClick"]),m(s,{size:"small",type:"danger",plain:"",onClick:a=>Ge(e)},{default:v((()=>[m(l,null,{default:v((()=>[m(_(B))])),_:1}),a[13]||(a[13]=g("span",null,"删除",-1))])),_:2,__:[13]},1032,["onClick"])])])])))),128))])),_:1})),_(Be)||0!==_(qe).length?u("",!0):(d(),o("div",xe,[a[15]||(a[15]=g("div",{class:"empty-icon"},"📦",-1)),a[16]||(a[16]=g("div",{class:"empty-text"},"暂无套餐配置",-1)),m(s,{type:"primary",onClick:Xe,class:"empty-action"},{default:v((()=>[m(l,null,{default:v((()=>[m(_(k))])),_:1}),a[14]||(a[14]=g("span",null,"新增套餐",-1))])),_:1,__:[14]})]))])):u("",!0),Ae.value?(d(),o("div",je,[g("div",Te,[g("div",{class:"action-item primary-action",onClick:Xe},[g("div",Pe,[m(l,{size:24},{default:v((()=>[m(_(k))])),_:1})]),a[17]||(a[17]=g("span",{class:"action-label"},"新增",-1))]),g("div",{class:b(["action-item",{refreshing:De.value}]),onClick:Oe},[g("div",Ce,[m(l,{size:20},{default:v((()=>[m(_(w))])),_:1})]),a[18]||(a[18]=g("span",{class:"action-label"},"刷新",-1))],2)])])):u("",!0),m(Ue,{modelValue:Le.value,"onUpdate:modelValue":a[5]||(a[5]=e=>Le.value=e),title:We.value?"编辑套餐类型":"新增套餐类型",width:"500px","close-on-click-modal":!1},{footer:v((()=>[g("div",Ve,[m(s,{onClick:a[4]||(a[4]=e=>Le.value=!1)},{default:v((()=>a[21]||(a[21]=[V("取消")]))),_:1,__:[21]}),p((d(),y(s,{type:"primary",onClick:He,loading:_(Ee)},{default:v((()=>a[22]||(a[22]=[V(" 确定 ")]))),_:1,__:[22]},8,["loading"])),[[Me]])])])),default:v((()=>[m(ze,{ref_key:"formRef",ref:Se,model:_($e),rules:_(Re),"label-width":"100px",class:"config-form"},{default:v((()=>[m(Z,{label:"套餐名称",prop:"name"},{default:v((()=>[m(X,{modelValue:_($e).name,"onUpdate:modelValue":a[0]||(a[0]=e=>_($e).name=e),placeholder:"请输入套餐名称"},null,8,["modelValue"])])),_:1}),m(Z,{label:"描述",prop:"description"},{default:v((()=>[m(X,{modelValue:_($e).description,"onUpdate:modelValue":a[1]||(a[1]=e=>_($e).description=e),type:"textarea",rows:3,placeholder:"请输入套餐描述"},null,8,["modelValue"])])),_:1}),m(Z,{label:"价格",prop:"price"},{default:v((()=>[m(G,{modelValue:_($e).price,"onUpdate:modelValue":a[2]||(a[2]=e=>_($e).price=e),min:0,precision:2,placeholder:"请输入价格",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),m(Z,{label:"状态",prop:"isActive"},{default:v((()=>[m(J,{modelValue:_($e).isActive,"onUpdate:modelValue":a[3]||(a[3]=e=>_($e).isActive=e)},{default:v((()=>[m(H,{label:!0},{default:v((()=>a[19]||(a[19]=[V("启用")]))),_:1,__:[19]}),m(H,{label:!1},{default:v((()=>a[20]||(a[20]=[V("禁用")]))),_:1,__:[20]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-ef430742"]]),ze=Object.freeze(Object.defineProperty({__proto__:null,default:Ae},Symbol.toStringTag,{value:"Module"}));export{Ae as P,J as a,ze as b};
