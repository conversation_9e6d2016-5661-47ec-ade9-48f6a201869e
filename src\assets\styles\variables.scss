// Light 主题变量 ｜ Dark 主题变量

:root {
  // Theme color
  --art-primary: 93, 135, 255;
  --art-secondary: 73, 190, 255;
  --art-error: 250, 137, 107;
  --art-info: 83, 155, 255;
  --art-success: 19, 222, 185;
  --art-warning: 255, 174, 31;
  --art-danger: 255, 77, 79;

  // Theme background color
  --art-bg-primary: 236, 242, 255;
  --art-bg-secondary: 232, 247, 255;
  --art-bg-success: 230, 255, 250;
  --art-bg-error: 253, 237, 232;
  --art-bg-info: 235, 243, 254;
  --art-bg-warning: 254, 245, 229;
  --art-bg-danger: 253, 237, 232;

  --art-hoverColor: 246, 249, 252;
  --art-grey100: 242, 246, 250;
  --art-grey200: 234, 239, 244;

  --art-color: #ffffff;
  --art-light: #f9f9f9;
  --art-dark: #1e2129;

  // Background color | Hover color
  --art-text-muted: #99a1b7;
  --art-gray-100: #f9f9f9;
  --art-gray-100-rgb: 249, 249, 249;
  --art-gray-200: #f1f1f4;
  --art-gray-200-rgb: 241, 241, 244;
  --art-gray-300: #dbdfe9;
  --art-gray-300-rgb: 219, 223, 233;
  --art-gray-400: #c4cada;
  --art-gray-400-rgb: 196, 202, 218;
  --art-gray-500: #99a1b7;
  --art-gray-500-rgb: 153, 161, 183;
  --art-gray-600: #78829d;
  --art-gray-600-rgb: 120, 130, 157;
  --art-gray-700: #4b5675;
  --art-gray-700-rgb: 75, 86, 117;
  --art-gray-800: #252f4a;
  --art-gray-800-rgb: 37, 47, 74;
  --art-gray-900: #071437;
  --art-gray-900-rgb: 7, 20, 55;

  // Text color
  --art-text-muted: #99a1b7;
  --art-text-gray-100: #f9f9f9;
  --art-text-gray-200: #f1f1f4;
  --art-text-gray-300: #dbdfe9;
  --art-text-gray-400: #c4cada;
  --art-text-gray-500: #99a1b7;
  --art-text-gray-600: #78829d;
  --art-text-gray-700: #4b5675;
  --art-text-gray-800: #252f4a;
  --art-text-gray-900: #071437;

  // Border
  --art-border-color: #eaebf1;
  --art-border-dashed-color: #dbdfe9;
  --art-root-card-border-color: #f1f1f4;

  // Shadow
  --art-box-shadow-xs: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
  --art-box-shadow-sm: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05);
  --art-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  --art-box-shadow-lg: 0 1rem 2rem 1rem rgba(0, 0, 0, 0.1);

  // Root card box、shadow
  --art-root-card-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --art-root-card-border-color: #f1f1f4;

  // Theme background color
  --art-bg-color: #fafbfc; // 最底部背景颜色
  --art-main-bg-color: #ffffff;
}

// Dark 主题变量
html.dark {
  // Theme color
  --art-primary: 93, 135, 255;
  --art-secondary: 73, 190, 255;
  --art-error: 250, 137, 107;
  --art-info: 83, 155, 255;
  --art-success: 19, 222, 185;
  --art-warning: 255, 174, 31;
  --art-danger: 255, 77, 79;

  // Theme background color
  --art-bg-primary: 37, 54, 98;
  --art-bg-secondary: 28, 69, 93;
  --art-bg-success: 27, 60, 72;
  --art-bg-error: 75, 49, 61;
  --art-bg-info: 34, 54, 98;
  --art-bg-warning: 77, 58, 42;
  --art-bg-danger: 100, 49, 61;

  --art-hoverColor: 51, 63, 85;
  --art-grey100: 51, 63, 85;
  --art-grey200: 70, 86, 112;

  --art-color: #000000;
  --art-light: #1b1c22;
  --art-dark: #272a34;

  // Background color | Hover color
  --art-text-muted: #636674;
  --art-gray-100: #1b1c22;
  --art-gray-100-rgb: 27, 28, 34;
  --art-gray-200: #26272f;
  --art-gray-200-rgb: 38, 39, 47;
  --art-gray-300: #363843;
  --art-gray-300-rgb: 54, 56, 67;
  --art-gray-400: #464852;
  --art-gray-400-rgb: 70, 72, 82;
  --art-gray-500: #636674;
  --art-gray-500-rgb: 99, 102, 116;
  --art-gray-600: #808290;
  --art-gray-600-rgb: 128, 130, 144;
  --art-gray-700: #9a9cae;
  --art-gray-700-rgb: 154, 156, 174;
  --art-gray-800: #b5b7c8;
  --art-gray-800-rgb: 181, 183, 200;
  --art-gray-900: #f5f5f5;
  --art-gray-900-rgb: 245, 245, 245;

  // Text color
  --art-text-muted: #636674;
  --art-text-gray-100: #1b1c22;
  --art-text-gray-200: #26272f;
  --art-text-gray-300: #363843;
  --art-text-gray-400: #464852;
  --art-text-gray-500: #636674;
  --art-text-gray-600: #808290;
  --art-text-gray-700: #9a9cae;
  --art-text-gray-800: #b5b7c8;
  --art-text-gray-900: #f5f5f5;

  // Border
  --art-border-color: #26272f;
  --art-border-dashed-color: #363843;
  --art-root-card-border-color: #1e2027;

  // Shadow
  --art-box-shadow-xs: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
  --art-box-shadow-sm: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05);
  --art-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  --art-box-shadow-lg: 0 1rem 2rem 1rem rgba(0, 0, 0, 0.1);

  // Root card box、shadow
  --art-root-card-box-shadow: none;
  --art-root-card-border-color: #1e2027;

  // Theme background color
  --art-bg-color: #070707;
  --art-main-bg-color: #161618;
}

// CSS 全局变量
:root {
  --art-card-border: rgba(var(--art-gray-300-rgb), 0.6); // 卡片边框颜色
  --art-card-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04); // 卡片阴影
}

// 媒体查询-设备尺寸
// notebook
$device-notebook: 1600px;
// ipad pro
$device-ipad-pro: 1180px;
// ipad
$device-ipad: 800px;
// ipad-竖屏
$device-ipad-vertical: 900px;
// mobile
$device-phone: 500px;

.bg-primary {
  background-color: rgb(var(--art-bg-primary)) !important;
  color: rgb(var(--art-primary)) !important;
}

.bg-secondary {
  background-color: rgb(var(--art-bg-secondary)) !important;
  color: rgb(var(--art-secondary)) !important;
  border: 1px solid var(--art-secondary);
}

.bg-warning {
  background-color: rgb(var(--art-bg-warning)) !important;
  color: rgb(var(--art-warning)) !important;
}

.bg-error {
  background-color: rgb(var(--art-bg-error)) !important;
  color: rgb(var(--art-error)) !important;
}

.bg-success {
  background-color: rgb(var(--art-bg-success)) !important;
  color: rgb(var(--art-success)) !important;
}

.bg-danger {
  background-color: rgb(var(--art-bg-danger)) !important;
  color: rgb(var(--art-danger)) !important;
}

.bg-grey100 {
  background-color: rgb(var(--art-grey100)) !important;
}

.bg-grey200 {
  background-color: rgb(var(--art-grey200)) !important;
}

.bg-hoverColor {
  background-color: rgb(var(--art-hoverColor)) !important;
}

.text-primary {
  color: rgb(var(--art-primary)) !important;
}

.text-secondary {
  color: rgb(var(--art-secondary)) !important;
}

.text-error {
  color: rgb(var(--art-error)) !important;
}

.text-danger {
  color: rgb(var(--art-danger)) !important;
}

.text-info {
  color: rgb(var(--art-info)) !important;
}

.text-success {
  color: rgb(var(--art-success)) !important;
}

.text-warning {
  color: rgb(var(--art-warning)) !important;
}
