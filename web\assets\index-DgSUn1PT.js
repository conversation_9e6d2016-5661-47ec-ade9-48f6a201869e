var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,i=(t,a,s)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s;import"./index-BOCMSBcY.js";/* empty css                     */import{k as o,c,O as n,C as p,S as d,x as m,X as u,ah as b,Q as f,u as v,D as y,by as _,F as j,Z as O,B as x,R as h,bz as g}from"./vendor-9ydHGNSq.js";import{_ as C}from"./_plugin-vue_export-helper-BCo6x5W8.js";const P={class:"timeline-list-card"},w={class:"art-card art-custom-card"},k={class:"card-header"},D={class:"card-title"},I={class:"card-subtitle"},S={class:"timeline-item"},q={class:"timeline-content"},z={class:"timeline-text"},A={key:0,class:"timeline-code"},B=o((E=((e,t)=>{for(var a in t||(t={}))r.call(t,a)&&i(e,a,t[a]);if(s)for(var a of s(t))l.call(t,a)&&i(e,a,t[a]);return e})({},{name:"ArtTimelineListCard"}),t(E,a({__name:"index",props:{list:{},title:{default:""},subtitle:{default:""},maxCount:{default:5}},setup(e){const t=e,a=c((()=>65*t.maxCount+"px"));return(e,t)=>{const s=g,r=_,l=b;return p(),n("div",P,[d("div",w,[d("div",k,[d("p",D,u(e.title),1),d("p",I,u(e.subtitle),1)]),m(l,{style:f({height:v(a)})},{default:y((()=>[m(r,null,{default:y((()=>[(p(!0),n(j,null,O(e.list,(e=>(p(),x(s,{key:e.time,timestamp:e.time,placement:"top",color:e.status,center:!0},{default:y((()=>[d("div",S,[d("div",q,[d("span",z,u(e.content),1),e.code?(p(),n("span",A," #"+u(e.code),1)):h("",!0)])])])),_:2},1032,["timestamp","color"])))),128))])),_:1})])),_:1},8,["style"])])])}}}))));var E;const F=C(B,[["__scopeId","data-v-9eda4bc8"]]);export{F as _};
