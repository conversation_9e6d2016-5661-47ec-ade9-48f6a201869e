<template>
  <div class="appointment-management">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="客户姓名">
          <el-input v-model="searchForm.customerName" placeholder="请输入客户姓名" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="预约状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待确认" value="pending" />
            <el-option label="已确认" value="confirmed" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="预约日期">
          <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" v-ripple>搜索</el-button>
          <el-button @click="handleReset" v-ripple>重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" :icon="Plus" @click="handleAdd" v-ripple>
        新增预约
      </el-button>
      <el-button @click="refresh" :loading="loading" v-ripple>
        刷新
      </el-button>
      <el-dropdown @command="handleExcelCommand" trigger="click">
        <el-button :icon="Document" v-ripple>
          Excel操作
          <el-icon class="el-icon--right">
            <ArrowDown />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="import">导入Excel</el-dropdown-item>
            <el-dropdown-item command="export">导出Excel</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="tableData" border stripe @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="customerName" label="客户姓名" min-width="100" />
      <el-table-column prop="phone" label="手机号" width="120" />
      <el-table-column prop="serviceType" label="服务类型" width="120" />
      <el-table-column prop="appointmentDate" label="预约日期" width="120" align="center">
        <template #default="{ row }">
          {{ formatDate(row.appointmentDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="appointmentTime" label="预约时间" width="100" align="center" />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="totalPrice" label="总价" width="100" align="center">
        <template #default="{ row }">
          <span class="price-text">¥{{ row.totalPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="notes" label="备注" min-width="150" show-overflow-tooltip />
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)" v-ripple>
            编辑
          </el-button>
          <el-button type="success" size="small" @click="handleConfirm(row)" v-if="row.status === 'pending'" v-ripple>
            确认
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)" v-ripple>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handlePageChange" />
    </div>

    <!-- 预约表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑预约' : '新增预约'" width="600px" :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" class="appointment-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户姓名" prop="customerName">
              <el-input v-model="formData.customerName" placeholder="请输入客户姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务类型" prop="serviceType">
              <el-select v-model="formData.serviceType" placeholder="请选择服务类型">
                <el-option v-for="service in serviceOptions" :key="service.value" :label="service.label"
                  :value="service.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预约日期" prop="appointmentDate">
              <el-date-picker v-model="formData.appointmentDate" type="date" placeholder="选择预约日期" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约时间" prop="appointmentTime">
              <el-time-picker v-model="formData.appointmentTime" placeholder="选择预约时间" format="HH:mm"
                value-format="HH:mm" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总价" prop="totalPrice">
              <el-input-number v-model="formData.totalPrice" :min="0" :precision="2" placeholder="请输入总价"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="pending">待确认</el-radio>
            <el-radio label="confirmed">已确认</el-radio>
            <el-radio label="completed">已完成</el-radio>
            <el-radio label="cancelled">已取消</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting" v-ripple>
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { Plus, Document, ArrowDown } from '@element-plus/icons-vue'
import { AppointmentService } from '@/api/appointmentApi'
// 显式导入增强版本的Hooks
import { useTable, useForm } from '@/composables'

defineOptions({ name: 'AppointmentManagement' })

// 搜索表单
const searchForm = reactive({
  customerName: '',
  phone: '',
  status: '',
  dateRange: []
})

// 服务类型选项
const serviceOptions = ref([
  { label: '基础套餐', value: 'basic' },
  { label: '标准套餐', value: 'standard' },
  { label: '高级套餐', value: 'premium' },
  { label: '定制套餐', value: 'custom' }
])

// 表格管理
const {
  tableData,
  loading,
  pagination,
  selectedRows,
  refresh,
  search,
  handlePageChange,
  handleSizeChange,
  handleSelectionChange
} = useTable({
  api: (params) => AppointmentService.getAppointmentList(params),
  deleteApi: (id) => AppointmentService.deleteAppointment(id),
  immediate: true
})

// 表单管理
const {
  formRef,
  formData,
  rules,
  submitting,
  submit,
  setCreateMode,
  setEditMode
} = useForm({
  initialData: {
    customerName: '',
    phone: '',
    serviceType: '',
    appointmentDate: '',
    appointmentTime: '',
    totalPrice: 0,
    notes: '',
    status: 'pending'
  },
  rules: {
    customerName: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
    appointmentDate: [{ required: true, message: '请选择预约日期', trigger: 'change' }],
    appointmentTime: [{ required: true, message: '请选择预约时间', trigger: 'change' }],
    totalPrice: [{ required: true, message: '请输入总价', trigger: 'blur' }]
  },
  submitApi: (data) => {
    return formData._id
      ? AppointmentService.updateAppointment(formData._id, data)
      : AppointmentService.createAppointment(data)
  },
  onSuccess: () => {
    dialogVisible.value = false
    refresh()
  }
})

// 对话框状态
const dialogVisible = ref(false)
const isEdit = computed(() => !!formData._id)

// 操作处理
const handleAdd = () => {
  setCreateMode()
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户"${row.customerName}"的预约吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await AppointmentService.deleteAppointment(row._id)
    ElMessage.success('删除成功')
    refresh()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleConfirm = async (row: any) => {
  try {
    await AppointmentService.updateAppointment(row._id, { status: 'confirmed' })
    ElMessage.success('预约确认成功')
    refresh()
  } catch (error) {
    ElMessage.error('确认失败')
  }
}

const handleSubmit = async () => {
  await submit()
}

const handleSearch = () => {
  const params: any = { ...searchForm }
  if (params.dateRange && params.dateRange.length === 2) {
    params.startDate = params.dateRange[0]
    params.endDate = params.dateRange[1]
    delete params.dateRange
  }
  search(params)
}

const handleReset = () => {
  Object.assign(searchForm, {
    customerName: '',
    phone: '',
    status: '',
    dateRange: []
  })
  search({})
}

const handleExcelCommand = (command: string) => {
  if (command === 'import') {
    ElMessage.info('导入功能开发中...')
  } else if (command === 'export') {
    ElMessage.info('导出功能开发中...')
  }
}

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    confirmed: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消'
  }
  return textMap[status] || status
}

const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}
</script>

<style lang="scss" scoped>
.appointment-management {
  padding: 20px;

  .search-bar {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 0;
        margin-right: 16px;
      }
    }
  }

  .action-bar {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }

  .el-table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .price-text {
      color: #f56c6c;
      font-weight: 500;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .appointment-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .appointment-management {
    padding: 10px;

    .search-bar {
      padding: 15px;

      .search-form {
        .el-form-item {
          margin-right: 0;
          margin-bottom: 15px;
          width: 100%;

          :deep(.el-form-item__content) {
            width: 100%;
          }
        }
      }
    }

    .action-bar {
      .el-button {
        flex: 1;
        min-width: 120px;
      }
    }

    .el-table {
      font-size: 14px;

      .el-table__cell {
        padding: 8px 4px;
      }
    }

    .pagination-wrapper {
      padding: 15px;

      :deep(.el-pagination) {
        justify-content: center;
        flex-wrap: wrap;
      }
    }

    .appointment-form {
      .el-col {
        width: 100% !important;
        flex: none !important;
      }
    }
  }
}
</style>
