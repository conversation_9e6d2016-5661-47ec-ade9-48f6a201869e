import { ref, computed, reactive, watch, type Ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useApi, type UseApiOptions } from './useApi'

export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | string
  sortable?: boolean
  show?: boolean
}

export interface PaginationConfig {
  page: number
  pageSize: number
  total: number
  pageSizes?: number[]
  layout?: string
}

export interface UseTableOptions<T = any> {
  /** 获取数据的API函数 */
  api: (params: any) => Promise<{ data: T[]; total?: number }>
  /** 删除数据的API函数 */
  deleteApi?: (id: string | number) => Promise<any>
  /** 初始查询参数 */
  defaultParams?: Record<string, any>
  /** 是否立即加载数据 */
  immediate?: boolean
  /** 分页配置 */
  pagination?: Partial<PaginationConfig>
  /** 表格列配置 */
  columns?: TableColumn[]
  /** API选项 */
  apiOptions?: UseApiOptions<{ data: T[]; total?: number }>
}

export interface UseTableReturn<T = any> {
  /** 表格数据 */
  tableData: Ref<T[]>
  /** 加载状态 */
  loading: Ref<boolean>
  /** 分页配置 */
  pagination: Ref<PaginationConfig>
  /** 查询参数 */
  queryParams: Ref<Record<string, any>>
  /** 选中的行 */
  selectedRows: Ref<T[]>
  /** 表格列配置 */
  columns: Ref<TableColumn[]>
  /** 加载数据 */
  loadData: (params?: Record<string, any>) => Promise<void>
  /** 刷新数据 */
  refresh: () => Promise<void>
  /** 重置查询 */
  resetQuery: () => Promise<void>
  /** 搜索 */
  search: (params: Record<string, any>) => Promise<void>
  /** 分页改变 */
  handlePageChange: (page: number) => void
  /** 页大小改变 */
  handleSizeChange: (size: number) => void
  /** 排序改变 */
  handleSortChange: (sort: { prop: string; order: string }) => void
  /** 选择改变 */
  handleSelectionChange: (selection: T[]) => void
  /** 删除行 */
  deleteRow: (row: T, idField?: string) => Promise<void>
  /** 批量删除 */
  batchDelete: (idField?: string) => Promise<void>
  /** 切换列显示 */
  toggleColumn: (prop: string) => void
}

/**
 * 表格管理Hook
 * 提供数据加载、分页、排序、搜索、删除等功能
 */
export function useTable<T = any>(options: UseTableOptions<T>): UseTableReturn<T> {
  const {
    api,
    deleteApi,
    defaultParams = {},
    immediate = true,
    pagination: paginationConfig = {},
    columns: columnsConfig = [],
    apiOptions = {}
  } = options

  // 表格数据
  const tableData = ref<T[]>([]) as Ref<T[]>
  
  // 分页配置
  const pagination = ref<PaginationConfig>({
    page: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [10, 20, 50, 100],
    layout: 'total, sizes, prev, pager, next, jumper',
    ...paginationConfig
  })

  // 查询参数
  const queryParams = ref<Record<string, any>>({ ...defaultParams })

  // 选中的行
  const selectedRows = ref<T[]>([]) as Ref<T[]>

  // 表格列配置
  const columns = ref<TableColumn[]>(
    columnsConfig.map(col => ({ show: true, ...col }))
  )

  // 显示的列
  const visibleColumns = computed(() => 
    columns.value.filter(col => col.show !== false)
  )

  // API请求Hook
  const {
    loading,
    execute: executeApi
  } = useApi(api, {
    immediate: false,
    onSuccess: (response) => {
      if (Array.isArray(response)) {
        // 如果直接返回数组
        tableData.value = response
      } else if (response && Array.isArray(response.data)) {
        // 如果返回包含data字段的对象
        tableData.value = response.data
        if (typeof response.total === 'number') {
          pagination.value.total = response.total
        }
      }
    },
    ...apiOptions
  })

  // 加载数据
  const loadData = async (params?: Record<string, any>) => {
    const requestParams = {
      ...queryParams.value,
      ...params,
      page: pagination.value.page,
      pageSize: pagination.value.pageSize
    }

    await executeApi(requestParams)
  }

  // 刷新数据
  const refresh = () => loadData()

  // 重置查询
  const resetQuery = async () => {
    queryParams.value = { ...defaultParams }
    pagination.value.page = 1
    await loadData()
  }

  // 搜索
  const search = async (params: Record<string, any>) => {
    queryParams.value = { ...queryParams.value, ...params }
    pagination.value.page = 1
    await loadData()
  }

  // 分页改变
  const handlePageChange = (page: number) => {
    pagination.value.page = page
    loadData()
  }

  // 页大小改变
  const handleSizeChange = (size: number) => {
    pagination.value.pageSize = size
    pagination.value.page = 1
    loadData()
  }

  // 排序改变
  const handleSortChange = (sort: { prop: string; order: string }) => {
    if (sort.order) {
      queryParams.value.sortBy = sort.prop
      queryParams.value.order = sort.order === 'ascending' ? 'asc' : 'desc'
    } else {
      delete queryParams.value.sortBy
      delete queryParams.value.order
    }
    pagination.value.page = 1
    loadData()
  }

  // 选择改变
  const handleSelectionChange = (selection: T[]) => {
    selectedRows.value = selection
  }

  // 删除行
  const deleteRow = async (row: T, idField = 'id') => {
    if (!deleteApi) {
      ElMessage.warning('未配置删除API')
      return
    }

    try {
      await ElMessageBox.confirm(
        '确定要删除这条记录吗？',
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const id = (row as any)[idField]
      await deleteApi(id)
      
      ElMessage.success('删除成功')
      await refresh()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 批量删除
  const batchDelete = async (idField = 'id') => {
    if (!deleteApi) {
      ElMessage.warning('未配置删除API')
      return
    }

    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要删除的记录')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
        '批量删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 批量删除
      const deletePromises = selectedRows.value.map(row => {
        const id = (row as any)[idField]
        return deleteApi(id)
      })

      await Promise.all(deletePromises)
      
      ElMessage.success('批量删除成功')
      selectedRows.value = []
      await refresh()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('批量删除失败')
      }
    }
  }

  // 切换列显示
  const toggleColumn = (prop: string) => {
    const column = columns.value.find(col => col.prop === prop)
    if (column) {
      column.show = !column.show
    }
  }

  // 立即加载数据
  if (immediate) {
    loadData()
  }

  return {
    tableData,
    loading,
    pagination,
    queryParams,
    selectedRows,
    columns: visibleColumns,
    loadData,
    refresh,
    resetQuery,
    search,
    handlePageChange,
    handleSizeChange,
    handleSortChange,
    handleSelectionChange,
    deleteRow,
    batchDelete,
    toggleColumn
  }
}
