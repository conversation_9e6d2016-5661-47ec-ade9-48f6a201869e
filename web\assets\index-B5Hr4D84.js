var e=Object.defineProperty,r=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,p=(r,t,o)=>t in r?e(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o;import{k as l,c as n,O as i,C as c,S as u,Q as b,u as m}from"./vendor-9ydHGNSq.js";import{_ as f}from"./_plugin-vue_export-helper-BCo6x5W8.js";const y=""+new URL("logo-M2jyX01t.webp",import.meta.url).href,O={class:"art-logo"},d=l((g=((e,r)=>{for(var t in r||(r={}))a.call(r,t)&&p(e,t,r[t]);if(o)for(var t of o(r))s.call(r,t)&&p(e,t,r[t]);return e})({},{name:"ArtLogo"}),r(g,t({__name:"index",props:{size:{default:36}},setup(e){const r=e,t=n((()=>({width:`${r.size}px`})));return(e,r)=>(c(),i("div",O,[u("img",{style:b(m(t)),src:y,alt:"logo"},null,4)]))}}))));var g;const j=f(d,[["__scopeId","data-v-85763627"]]);export{j as _};
