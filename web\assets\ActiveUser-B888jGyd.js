import{_ as s}from"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";import{k as a,O as t,C as d,x as e,bp as c,S as i,F as n,Z as r,X as p}from"./vendor-9ydHGNSq.js";import{_ as l}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";const m={class:"card art-custom-card"},u={class:"list"},o={class:"subtitle"},v=l(a({__name:"ActiveUser",setup(a){const l=[{name:"总用户量",num:"32k"},{name:"总访问量",num:"128k"},{name:"日访问量",num:"1.2k"},{name:"周同比",num:"+5%"}];return(a,v)=>{const _=s;return d(),t("div",m,[e(_,{class:"chart",barWidth:"50%",height:"13.7rem",showAxisLine:!1,data:[160,100,150,80,190,100,175,120,160],xAxisData:["1","2","3","4","5","6","7","8","9"]}),v[0]||(v[0]=c('<div class="text" data-v-cfdc54dd><h3 class="box-title" data-v-cfdc54dd>用户概述</h3><p class="subtitle" data-v-cfdc54dd>比上周 <span class="text-success" data-v-cfdc54dd>+23%</span></p><p class="subtitle" data-v-cfdc54dd>我们为您创建了多个选项，可将它们组合在一起并定制为像素完美的页面</p></div>',1)),i("div",u,[(d(),t(n,null,r(l,((s,a)=>i("div",{key:a},[i("p",null,p(s.num),1),i("p",o,p(s.name),1)]))),64))])])}}}),[["__scopeId","data-v-cfdc54dd"]]);export{v as default};
