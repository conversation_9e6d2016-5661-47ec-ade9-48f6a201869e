import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/modules/user'
import { useRoute } from 'vue-router'
import { useCommon } from '@/composables/useCommon'

export interface PermissionOptions {
  /** 是否严格模式（需要完全匹配） */
  strict?: boolean
  /** 默认权限（当用户权限为空时使用） */
  defaultPermissions?: string[]
}

/**
 * 权限管理Hook
 * 提供权限检查、角色检查等功能
 */
export function usePermission(options: PermissionOptions = {}) {
  const { strict = false, defaultPermissions = [] } = options

  const userStore = useUserStore()
  const route = useRoute()
  const { info } = storeToRefs(userStore)

  // 用户权限列表
  const permissions = computed(() => {
    const userPerms = info.value?.perms || []
    return userPerms.length > 0 ? userPerms : defaultPermissions
  })

  // 用户角色列表
  const roles = computed(() => info.value?.roles || [])

  // 是否为超级管理员
  const isSuperAdmin = computed(() => roles.value.includes('superadmin'))

  // 是否为管理员
  const isAdmin = computed(() => 
    roles.value.includes('admin') || roles.value.includes('superadmin')
  )

  // 检查是否拥有指定权限
  const hasPermission = (permission: string | string[]): boolean => {
    // 超级管理员拥有所有权限
    if (isSuperAdmin.value) return true

    const userPermissions = permissions.value
    if (!userPermissions || userPermissions.length === 0) return false

    // 如果权限列表包含通配符，则拥有所有权限
    if (userPermissions.includes('*')) return true

    if (Array.isArray(permission)) {
      // 检查多个权限
      if (strict) {
        // 严格模式：必须拥有所有权限
        return permission.every(perm => userPermissions.includes(perm))
      } else {
        // 非严格模式：拥有任一权限即可
        return permission.some(perm => userPermissions.includes(perm))
      }
    } else {
      // 检查单个权限
      return userPermissions.includes(permission)
    }
  }

  // 检查是否拥有指定角色
  const hasRole = (role: string | string[]): boolean => {
    const userRoles = roles.value
    if (!userRoles || userRoles.length === 0) return false

    if (Array.isArray(role)) {
      if (strict) {
        // 严格模式：必须拥有所有角色
        return role.every(r => userRoles.includes(r))
      } else {
        // 非严格模式：拥有任一角色即可
        return role.some(r => userRoles.includes(r))
      }
    } else {
      return userRoles.includes(role)
    }
  }

  // 检查当前路由权限
  const hasRoutePermission = (): boolean => {
    const routePermissions = route.meta?.permissions as string[]
    if (!routePermissions || routePermissions.length === 0) return true
    
    return hasPermission(routePermissions)
  }

  // 检查按钮权限
  const hasButtonPermission = (buttonKey: string): boolean => {
    // 构建按钮权限标识：路由名称:按钮键
    const routeName = route.name as string
    if (!routeName) return true

    const buttonPermission = `${routeName}:${buttonKey}`
    return hasPermission(buttonPermission)
  }

  // 权限过滤器（用于过滤菜单、按钮等）
  const filterByPermission = <T extends { permission?: string | string[] }>(
    items: T[]
  ): T[] => {
    return items.filter(item => {
      if (!item.permission) return true
      return hasPermission(item.permission)
    })
  }

  // 角色过滤器
  const filterByRole = <T extends { roles?: string | string[] }>(
    items: T[]
  ): T[] => {
    return items.filter(item => {
      if (!item.roles) return true
      return hasRole(item.roles)
    })
  }

  // 获取用户可访问的菜单权限
  const getAccessibleMenus = (menus: any[]): any[] => {
    return menus.filter(menu => {
      // 检查菜单权限
      if (menu.permission && !hasPermission(menu.permission)) {
        return false
      }

      // 检查角色权限
      if (menu.roles && !hasRole(menu.roles)) {
        return false
      }

      // 递归检查子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children = getAccessibleMenus(menu.children)
        // 如果所有子菜单都没有权限，则隐藏父菜单
        return menu.children.length > 0
      }

      return true
    })
  }

  // 权限状态
  const permissionStatus = computed(() => ({
    isLoggedIn: !!info.value,
    isSuperAdmin: isSuperAdmin.value,
    isAdmin: isAdmin.value,
    hasAnyPermission: permissions.value.length > 0,
    permissionCount: permissions.value.length,
    roleCount: roles.value.length
  }))

  return {
    // 数据
    permissions,
    roles,
    isSuperAdmin,
    isAdmin,
    permissionStatus,
    
    // 方法
    hasPermission,
    hasRole,
    hasRoutePermission,
    hasButtonPermission,
    filterByPermission,
    filterByRole,
    getAccessibleMenus
  }
}

/**
 * 简化的权限检查Hook（兼容旧版API）
 */
export function useAuth() {
  const userStore = useUserStore()
  const route = useRoute()
  const { isFrontendMode } = useCommon()
  const { info } = storeToRefs(userStore)
  const { hasPermission, hasRole, isSuperAdmin, isAdmin } = usePermission()

  // 前端按钮权限（例如：['add', 'edit']）
  const frontendAuthList = info.value?.buttons ?? []

  // 后端路由 meta 配置的权限列表
  const backendAuthList: any[] = Array.isArray(route.meta.authList)
    ? (route.meta.authList as any[])
    : []

  /**
   * 检查是否拥有某权限标识（兼容旧版API）
   * @param auth 权限标识
   * @returns 是否有权限
   */
  const hasAuth = (auth: string): boolean => {
    if (isFrontendMode.value) {
      return frontendAuthList.includes(auth)
    }

    // 先检查新的权限系统
    if (hasPermission(auth)) {
      return true
    }

    // 兼容旧的权限系统
    return backendAuthList.some((item) => item?.auth_mark === auth)
  }

  return {
    hasAuth,
    hasRole,
    isSuperAdmin,
    isAdmin,
    // 新增的方法
    hasPermission
  }
}

/**
 * 按钮权限Hook
 */
export function useButtonAuth() {
  const { hasButtonPermission, hasPermission } = usePermission()
  
  return {
    hasButtonAuth: hasButtonPermission,
    hasAuth: hasPermission
  }
}
