var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,i=(t,a,l)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[a]=l,o=(e,t)=>{for(var a in t||(t={}))r.call(t,a)&&i(e,a,t[a]);if(l)for(var a of l(t))n.call(t,a)&&i(e,a,t[a]);return e},u=(e,l)=>t(e,a(l)),s=(e,t,a)=>new Promise(((l,r)=>{var n=e=>{try{o(a.next(e))}catch(t){r(t)}},i=e=>{try{o(a.throw(e))}catch(t){r(t)}},o=e=>e.done?l(e.value):Promise.resolve(e.value).then(n,i);o((a=a.apply(e,t)).next())}));import{r as c,E as d,c as v,aD as f,aa as p}from"./vendor-9ydHGNSq.js";import"./index-BOCMSBcY.js";function y(e,t={}){const{immediate:a=!1,defaultData:l=null,onSuccess:r,onError:n,onFinally:i,showErrorMessage:v=!0,showSuccessMessage:f=!1,retryCount:p=0,retryDelay:y=1e3}=t,g=c(l),h=c(!1),m=c(null),w=c(!1);let b=null,P=0;const S=t=>s(this,null,(function*(){var a,l;h.value=!0,m.value=null,w.value=!1,b=new AbortController;try{const a=yield e(u(o({},t),{signal:b.signal}));if(g.value=a,w.value=!0,r&&r(a,a),f){const e="string"==typeof f?f:"操作成功";d.success(e)}return P=0,a}catch(s){if("AbortError"===s.name||"ERR_CANCELED"===s.code)return Promise.reject(s);if(m.value=s,w.value=!0,P<p)return P++,yield new Promise((e=>setTimeout(e,y))),S(t);if(n)n(s);else if(v){const e=(null==(l=null==(a=s.response)?void 0:a.data)?void 0:l.message)||s.message||"请求失败";d.error(e)}throw s}finally{h.value=!1,i&&i()}}));return a&&S(),{data:g,loading:h,error:m,finished:w,execute:S,refresh:()=>S(),reset:()=>{g.value=l,h.value=!1,m.value=null,w.value=!1,P=0},cancel:()=>{b&&(b.abort(),b=null),h.value=!1}}}function g(e){const{api:t,deleteApi:a,defaultParams:l={},immediate:r=!0,pagination:n={},columns:i=[],apiOptions:p={}}=e,g=c([]),h=c(o({page:1,pageSize:10,total:0,pageSizes:[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper"},n)),m=c(o({},l)),w=c([]),b=c(i.map((e=>o({show:!0},e)))),P=v((()=>b.value.filter((e=>!1!==e.show)))),{loading:S,execute:E}=y(t,o({immediate:!1,onSuccess:e=>{Array.isArray(e)?g.value=e:e&&Array.isArray(e.data)&&(g.value=e.data,"number"==typeof e.total&&(h.value.total=e.total))}},p)),A=e=>s(this,null,(function*(){const t=u(o(o({},m.value),e),{page:h.value.page,pageSize:h.value.pageSize});yield E(t)})),O=()=>A();return r&&A(),{tableData:g,loading:S,pagination:h,queryParams:m,selectedRows:w,columns:P,loadData:A,refresh:O,resetQuery:()=>s(this,null,(function*(){m.value=o({},l),h.value.page=1,yield A()})),search:e=>s(this,null,(function*(){m.value=o(o({},m.value),e),h.value.page=1,yield A()})),handlePageChange:e=>{h.value.page=e,A()},handleSizeChange:e=>{h.value.pageSize=e,h.value.page=1,A()},handleSortChange:e=>{e.order?(m.value.sortBy=e.prop,m.value.order="ascending"===e.order?"asc":"desc"):(delete m.value.sortBy,delete m.value.order),h.value.page=1,A()},handleSelectionChange:e=>{w.value=e},deleteRow:(e,t="id")=>s(this,null,(function*(){if(a)try{yield f.confirm("确定要删除这条记录吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=e[t];yield a(l),d.success("删除成功"),yield O()}catch(l){"cancel"!==l&&d.error("删除失败")}else d.warning("未配置删除API")})),batchDelete:(e="id")=>s(this,null,(function*(){if(a)if(0!==w.value.length)try{yield f.confirm(`确定要删除选中的 ${w.value.length} 条记录吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=w.value.map((t=>{const l=t[e];return a(l)}));yield Promise.all(t),d.success("批量删除成功"),w.value=[],yield O()}catch(t){"cancel"!==t&&d.error("批量删除失败")}else d.warning("请选择要删除的记录");else d.warning("未配置删除API")})),toggleColumn:e=>{const t=b.value.find((t=>t.prop===e));t&&(t.show=!t.show)}}}function h(e={}){const{initialData:t={},rules:a={},submitApi:l,getDetailApi:r,onSuccess:n,onError:i,beforeSubmit:u,showSuccessMessage:f=!0,apiOptions:g={}}=e,h=c(),m=p(o({},t)),w=c(a),b=c("create"),P=v((()=>"edit"===b.value)),{loading:S,execute:E}=y((e=>s(this,null,(function*(){if(!l)throw new Error("未配置提交API");return l(e)}))),o({immediate:!1,onSuccess:e=>{const t="string"==typeof f?f:P.value?"更新成功":"创建成功";f&&d.success(t),n&&n(m,e)},onError:e=>{i&&i(e)}},g)),{loading:A,execute:O}=y((e=>s(this,null,(function*(){if(!r)throw new Error("未配置获取详情API");return r(e)}))),{immediate:!1,onSuccess:e=>{D(e)}}),j=()=>s(this,null,(function*(){if(!h.value)return!1;try{return yield h.value.validate(),!0}catch(e){return!1}})),x=()=>{h.value&&h.value.resetFields(),Object.keys(m).forEach((e=>{delete m[e]})),Object.assign(m,o({},t))},D=e=>{Object.assign(m,e)},C=e=>s(this,null,(function*(){yield O(e)}));return{formRef:h,formData:m,rules:w,submitting:S,loading:A,mode:b,isEdit:P,submit:()=>s(this,null,(function*(){if(yield j())try{let e=o({},m);u&&(e=yield u(e)),yield E(e)}catch(e){}})),validate:j,validateField:e=>s(this,null,(function*(){if(!h.value)return!1;try{return yield h.value.validateField(e),!0}catch(t){return!1}})),resetForm:x,clearValidate:e=>{h.value&&h.value.clearValidate(e)},setFormData:D,getDetail:C,setCreateMode:()=>{b.value="create",x()},setEditMode:e=>s(this,null,(function*(){b.value="edit",e&&r&&(yield C(e))}))}}export{h as a,g as u};
