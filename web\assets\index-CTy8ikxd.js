var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,n=(t,a,s)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s,c=(e,t,a)=>new Promise(((s,i)=>{var l=e=>{try{c(a.next(e))}catch(t){i(t)}},n=e=>{try{c(a.throw(e))}catch(t){i(t)}},c=e=>e.done?s(e.value):Promise.resolve(e.value).then(l,n);c((a=a.apply(e,t)).next())}));import{d as o}from"./index-BOCMSBcY.js";/* empty css                   *//* empty css                    *//* empty css               *//* empty css               *//* empty css                  */import{k as r,M as u,c as d,r as p,d as v,O as m,C as y,S as g,x as h,X as f,D as b,a6 as T,u as x,aH as k,a3 as C,aV as _,F as w,Z as A,B as j,aY as O,a2 as B,Q as L,W as M,aG as H,aX as P,aF as D,E as S}from"./vendor-9ydHGNSq.js";import{C as V}from"./vue3-count-to.esm-Dsfbf00q.js";import{A as I}from"./appointmentApi-a7HLmJi8.js";import{_ as F}from"./_plugin-vue_export-helper-BCo6x5W8.js";const G={class:"admin-home"},U={class:"welcome-banner-section"},q={class:"custom-banner"},z={class:"banner-content"},Y={class:"banner-text-area"},E={class:"banner-title"},X={class:"user-identity"},N={class:"banner-subtitle"},Q={class:"date-info"},W={class:"banner-actions-area"},Z={class:"action-buttons"},$={class:"quick-stats"},J={class:"stat-item"},K={class:"stat-number"},R={class:"stat-item"},ee={class:"stat-number"},te={class:"stats-section"},ae={class:"modern-stat-card"},se=["innerHTML"],ie={class:"stat-content"},le={class:"stat-value"},ne={class:"stat-label"},ce={class:"quick-actions-section"},oe=["onClick"],re=["innerHTML"],ue={class:"action-title"},de={class:"action-desc"},pe={class:"activity-card"},ve={class:"card-header"},me={class:"activity-list"},ye=["innerHTML"],ge={class:"activity-content"},he={class:"activity-title"},fe={class:"activity-time"},be={class:"todo-card"},Te={class:"card-header"},xe={class:"todo-list"},ke={class:"system-status-section"},Ce={class:"status-card"},_e={class:"status-header"},we={class:"status-title"},Ae={class:"status-value"},je={class:"status-desc"},Oe=r((Be=((e,t)=>{for(var a in t||(t={}))i.call(t,a)&&n(e,a,t[a]);if(s)for(var a of s(t))l.call(t,a)&&n(e,a,t[a]);return e})({},{name:"AdminHome"}),t(Be,a({__name:"index",setup(e){const t=u(),a=o(),s=d((()=>a.getUserInfo)),i=d((()=>{const e=s.value.nickname;return"超级管理员"===e?"超级管理员":"管理员"===e?"管理员":s.value.name||s.value.username||"用户"})),l=d((()=>(new Date).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"}))),n=p({todayAppointments:0,monthlyCompleted:0}),r=p([{id:1,label:"今日预约",value:0,icon:"&#xe7f6;",iconBg:"#3b82f6",change:"今日",changeType:"neutral"},{id:2,label:"待处理",value:0,icon:"&#xe724;",iconBg:"#f59e0b",change:"待处理",changeType:"neutral"},{id:3,label:"已完成",value:0,icon:"&#xe82a;",iconBg:"#10b981",change:"已完成",changeType:"positive"},{id:4,label:"总客户数",value:0,icon:"&#xe813;",iconBg:"#8b5cf6",change:"总客户",changeType:"neutral"}]),F=p(!1),Oe=p([{id:1,key:"appointment",title:"预约管理",description:"查看和管理预约",icon:"&#xe7f6;",bgColor:"#2196f3"},{id:2,key:"users",title:"用户管理",description:"管理系统用户",icon:"&#xe813;",bgColor:"#4caf50"},{id:3,key:"roles",title:"角色权限",description:"配置角色权限",icon:"&#xe7b9;",bgColor:"#ff9800"},{id:4,key:"settings",title:"系统设置",description:"系统配置管理",icon:"&#xe721;",bgColor:"#9c27b0"}]),Be=p([{id:1,title:"新用户注册",time:"5分钟前",icon:"&#xe813;",color:"#4caf50",status:"已处理",statusClass:"success"},{id:2,title:"预约申请",time:"10分钟前",icon:"&#xe7f6;",color:"#2196f3",status:"待审核",statusClass:"warning"},{id:3,title:"系统更新",time:"1小时前",icon:"&#xe721;",color:"#ff9800",status:"已完成",statusClass:"success"},{id:4,title:"数据备份",time:"2小时前",icon:"&#xe806;",color:"#9c27b0",status:"进行中",statusClass:"info"}]),Le=p([{id:1,title:"审核待处理预约",completed:!1,priority:"high",priorityText:"高"},{id:2,title:"更新系统配置",completed:!1,priority:"medium",priorityText:"中"},{id:3,title:"检查服务器状态",completed:!0,priority:"low",priorityText:"低"},{id:4,title:"备份数据库",completed:!1,priority:"high",priorityText:"高"}]),Me=p([{id:1,title:"服务器状态",value:"正常运行",description:"99.9% 可用性",status:"online"},{id:2,title:"数据库",value:"连接正常",description:"响应时间 < 100ms",status:"online"},{id:3,title:"存储空间",value:"75% 已使用",description:"剩余 250GB",status:"warning"},{id:4,title:"内存使用",value:"68% 已使用",description:"8GB / 12GB",status:"online"}]),He=e=>{switch(e){case"appointment":t.push("/appointment/list");break;case"users":t.push("/system/user");break;case"roles":t.push("/system/role");break;case"settings":t.push("/appointment/config");break;case"report":S.info("报表功能开发中...");break;default:S.info("功能开发中...")}},Pe=()=>{S.info("查看全部活动功能开发中...")},De=()=>{S.info("添加待办事项功能开发中...")},Se=()=>c(this,null,(function*(){try{F.value=!0,yield Ve()}catch(e){Ie()}finally{F.value=!1}})),Ve=()=>c(this,null,(function*(){try{const e=yield I.getAppointmentList({limit:1e3,page:1});let t=[];t=e&&Array.isArray(e)?e:e&&e.items&&Array.isArray(e.items)?e.items:e&&e.data&&Array.isArray(e.data)?e.data:[];const a=yield I.getCustomerList({limit:1e3});let s=0;if(a&&Array.isArray(a)?s=a.length:a&&a.items&&Array.isArray(a.items)?s=a.items.length:a&&a.total&&(s=a.total),t.length>0){const e=new Date,a=e.toISOString().split("T")[0],i=t.filter((e=>!!e.appointmentTime&&new Date(e.appointmentTime).toISOString().split("T")[0]===a)).length,l=t.filter((e=>"pending"===e.status||"待确认"===e.status)).length,c=t.filter((e=>"completed"===e.status||"已完成"===e.status)).length,o=e.getMonth(),u=e.getFullYear(),d=t.filter((e=>{if(("completed"===e.status||"已完成"===e.status)&&e.appointmentTime){const t=new Date(e.appointmentTime);return t.getMonth()===o&&t.getFullYear()===u}return!1})).length;r.value[0].value=i,r.value[0].change="今日",r.value[0].changeType="positive",r.value[1].value=l,r.value[1].change="待处理",r.value[1].changeType=l>0?"positive":"neutral",r.value[2].value=c,r.value[2].change="已完成",r.value[2].changeType="positive",r.value[3].value=s,r.value[3].change="总客户",r.value[3].changeType="positive",n.value.todayAppointments=i,n.value.monthlyCompleted=d}else r.value[0].value=0,r.value[0].change="今日",r.value[0].changeType="neutral",r.value[1].value=0,r.value[1].change="待处理",r.value[1].changeType="neutral",r.value[2].value=0,r.value[2].change="已完成",r.value[2].changeType="neutral",r.value[3].value=s,r.value[3].change="总客户",r.value[3].changeType=s>0?"positive":"neutral",n.value.todayAppointments=0,n.value.monthlyCompleted=0}catch(e){Ie()}})),Ie=()=>{r.value[0].value=0,r.value[0].change="今日",r.value[0].changeType="positive",r.value[1].value=0,r.value[1].change="待处理",r.value[1].changeType="positive",r.value[2].value=0,r.value[2].change="已完成",r.value[2].changeType="positive",r.value[3].value=0,r.value[3].change="总数",r.value[3].changeType="positive",n.value.todayAppointments=0,n.value.monthlyCompleted=0};return v((()=>c(this,null,(function*(){yield a.updateUserInfo(),Se()})))),(e,t)=>{const a=C,s=O,c=P,o=D,u=H;return y(),m("div",G,[g("div",U,[g("div",q,[g("div",z,[g("div",Y,[g("h1",E,[t[2]||(t[2]=g("span",{class:"greeting"},"欢迎回来 ",-1)),g("span",X,f(i.value)+"！",1)]),g("p",N,[g("span",Q,f(l.value),1),t[3]||(t[3]=g("span",{class:"divider"},"·",-1)),t[4]||(t[4]=g("span",{class:"wish-text"},"祝您工作愉快",-1))])]),g("div",W,[g("div",Z,[h(a,{type:"primary",size:"large",icon:x(k),onClick:t[0]||(t[0]=e=>He("appointment")),class:"primary-action-btn"},{default:b((()=>t[5]||(t[5]=[T(" 新建预约 ")]))),_:1,__:[5]},8,["icon"]),h(a,{size:"large",icon:x(_),onClick:t[1]||(t[1]=e=>He("report")),class:"secondary-action-btn"},{default:b((()=>t[6]||(t[6]=[T(" 查看报表 ")]))),_:1,__:[6]},8,["icon"])]),g("div",$,[g("div",J,[g("span",K,f(n.value.todayAppointments),1),t[7]||(t[7]=g("span",{class:"stat-label"},"今日预约",-1))]),g("div",R,[g("span",ee,f(n.value.monthlyCompleted),1),t[8]||(t[8]=g("span",{class:"stat-label"},"本月完成",-1))])])])])])]),g("div",te,[t[9]||(t[9]=g("div",{class:"section-header"},[g("h2",{class:"section-title"},"数据概览"),g("p",{class:"section-subtitle"},"实时业务数据统计")],-1)),h(c,{gutter:24},{default:b((()=>[(y(!0),m(w,null,A(r.value,(e=>(y(),j(s,{xs:24,sm:12,md:12,lg:6,key:e.id},{default:b((()=>[B((y(),m("div",ae,[g("div",{class:"stat-icon-wrapper",style:L({backgroundColor:e.iconBg})},[g("i",{class:"iconfont-sys stat-icon",innerHTML:e.icon},null,8,se)],4),g("div",ie,[g("div",le,[h(x(V),{endVal:e.value,duration:1500},null,8,["endVal"])]),g("div",ne,f(e.label),1),g("div",{class:M(["stat-change",`change-${e.changeType}`])},f(e.change),3)])])),[[u,F.value]])])),_:2},1024)))),128))])),_:1})]),g("div",ce,[t[10]||(t[10]=g("div",{class:"section-header"},[g("h2",{class:"section-title"},"快捷操作"),g("p",{class:"section-subtitle"},"常用功能快速入口")],-1)),h(c,{gutter:20},{default:b((()=>[(y(!0),m(w,null,A(Oe.value,(e=>(y(),j(s,{xs:12,sm:8,md:6,key:e.id},{default:b((()=>[g("div",{class:"action-card",onClick:t=>He(e.key)},[g("div",{class:"action-icon",style:L({backgroundColor:e.bgColor})},[g("i",{class:"iconfont-sys",innerHTML:e.icon},null,8,re)],4),g("div",ue,f(e.title),1),g("div",de,f(e.description),1)],8,oe)])),_:2},1024)))),128))])),_:1})]),h(c,{gutter:20,class:"activity-section"},{default:b((()=>[h(s,{xs:24,md:12},{default:b((()=>[g("div",pe,[g("div",ve,[t[12]||(t[12]=g("h3",{class:"card-title"},"最近活动",-1)),h(a,{text:"",type:"primary",onClick:Pe},{default:b((()=>t[11]||(t[11]=[T("查看全部")]))),_:1,__:[11]})]),g("div",me,[(y(!0),m(w,null,A(Be.value,(e=>(y(),m("div",{key:e.id,class:"activity-item"},[g("div",{class:"activity-avatar",style:L({backgroundColor:e.color})},[g("i",{class:"iconfont-sys",innerHTML:e.icon},null,8,ye)],4),g("div",ge,[g("div",he,f(e.title),1),g("div",fe,f(e.time),1)]),g("div",{class:M(["activity-status",e.statusClass])},f(e.status),3)])))),128))])])])),_:1}),h(s,{xs:24,md:12},{default:b((()=>[g("div",be,[g("div",Te,[t[14]||(t[14]=g("h3",{class:"card-title"},"待办事项",-1)),h(a,{text:"",type:"primary",onClick:De},{default:b((()=>t[13]||(t[13]=[T("添加")]))),_:1,__:[13]})]),g("div",xe,[(y(!0),m(w,null,A(Le.value,(e=>(y(),m("div",{key:e.id,class:"todo-item"},[h(o,{modelValue:e.completed,"onUpdate:modelValue":t=>e.completed=t,onChange:t=>(e=>{S.success(e.completed?"任务已完成":"任务已重新激活")})(e)},{default:b((()=>[g("span",{class:M({completed:e.completed})},f(e.title),3)])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),g("div",{class:M(["todo-priority",e.priority])},f(e.priorityText),3)])))),128))])])])),_:1})])),_:1}),g("div",ke,[t[15]||(t[15]=g("div",{class:"section-header"},[g("h2",{class:"section-title"},"系统状态"),g("p",{class:"section-subtitle"},"实时监控系统运行状态")],-1)),h(c,{gutter:20},{default:b((()=>[(y(!0),m(w,null,A(Me.value,(e=>(y(),j(s,{xs:24,sm:12,md:6,key:e.id},{default:b((()=>[g("div",Ce,[g("div",_e,[g("span",we,f(e.title),1),g("div",{class:M(["status-indicator",e.status])},null,2)]),g("div",Ae,f(e.value),1),g("div",je,f(e.description),1)])])),_:2},1024)))),128))])),_:1})])])}}}))));var Be;const Le=F(Oe,[["__scopeId","data-v-25b3ecb8"]]);export{Le as default};
