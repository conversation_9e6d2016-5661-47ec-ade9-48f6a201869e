import { asyncRoutes } from '@/router/routes/asyncRoutes'
import { menuDataToRouter } from '@/router/utils/menuToRouter'
import { AppRouteRecord } from '@/types/router'
import request from '@/utils/http'
import { useUserStore } from '@/store/modules/user'

interface MenuResponse {
  menuList: AppRouteRecord[]
}

// 简化权限检查函数
const checkAdvancedPermission = (requiredPerm: string, userPerms: string[]): boolean => {
  // 1. 直接权限匹配
  if (userPerms.includes(requiredPerm)) {
    return true
  }

  // 2. 检查层级权限格式（父菜单:子菜单）
  const hierarchicalPerms = userPerms.filter((p) => p.includes(':'))
  for (const hierarchicalPerm of hierarchicalPerms) {
    const [parent, child] = hierarchicalPerm.split(':')
    if (child === requiredPerm || parent === requiredPerm) {
      return true
    }
  }

  return false
}

// 根据权限过滤菜单的辅助函数
function filterMenusByRoles(routes: any[], userRoles: string[]): any[] {
  const userStore = useUserStore()
  const userPerms = userStore.getUserInfo.perms || []



  return routes.filter((route) => {
    // 如果是超级管理员，显示所有菜单
    if (userRoles.includes('superadmin')) {
      if (route.children) {
        route.children = filterMenusByRoles(route.children, userRoles)
      }
      return true
    }

    // 检查权限：优先使用 perms，如果没有则使用 roles
    let hasPermission = false

    if (route.meta?.perms) {
      // 首页对所有登录用户可见
      if (route.meta.perms.includes('Home')) {
        hasPermission = true
      } else {
        // 使用高级权限检查
        hasPermission = route.meta.perms.some((perm: string) => {
          return checkAdvancedPermission(perm, userPerms)
        })
      }
    } else if (route.meta?.roles) {
      // 兼容旧的 roles 权限控制
      hasPermission = route.meta.roles.some((role: string) => userRoles.includes(role))
    } else {
      // 如果没有设置权限，则所有用户都可以访问
      hasPermission = true
    }

    if (hasPermission && route.children) {
      // 递归过滤子菜单
      route.children = filterMenusByRoles(route.children, userRoles)
      // 如果父菜单有权限但所有子菜单都被过滤掉了，则隐藏父菜单
      if (route.children.length === 0 && route.meta?.perms) {
        hasPermission = false
      }
    }

    return hasPermission
  })
}

// 菜单管理接口
export interface MenuCreateData {
  title: string
  path: string
  name: string
  icon?: string
  sort?: number
  isEnable?: boolean
  isMenu?: boolean
  keepAlive?: boolean
  isHidden?: boolean
  link?: string
  isIframe?: boolean
  parentId?: string
}

export interface MenuUpdateData extends Partial<MenuCreateData> {
  id: string
}

// 菜单接口
export const menuService = {
  async getMenuList(delay = 300): Promise<MenuResponse> {
    try {
      // 获取用户信息
      const userStore = useUserStore()
      const userInfo = userStore.getUserInfo

      // 确保用户信息存在
      if (!userInfo || !userInfo.roles) {
        throw new Error('用户信息不完整')
      }

      // 如果是超级管理员，返回所有菜单
      if (userInfo.roles?.includes('superadmin')) {
        // 每次都重新生成菜单，避免缓存问题
        const menuData = [...asyncRoutes] // 创建副本
        const menuList = menuData.map((route) => menuDataToRouter(route))
        await new Promise((resolve) => setTimeout(resolve, delay))
        return { menuList }
      }

      // 普通用户根据角色权限获取菜单
      try {
        const response = await request.get<MenuResponse>({
          url: '/users/menus',
          showErrorMessage: false // 禁用错误提示，避免显示错误弹窗
        })
        return response
      } catch (error) {

        // 如果后端接口不可用，根据用户角色过滤菜单
        const userRoles = userInfo.roles || []
        const filteredMenus = filterMenusByRoles(asyncRoutes, userRoles)
        const menuList = filteredMenus.map((route) => menuDataToRouter(route))
        await new Promise((resolve) => setTimeout(resolve, delay))
        return { menuList }
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('获取菜单失败')
    }
  },

  // 创建菜单
  async createMenu(data: MenuCreateData): Promise<AppRouteRecord> {
    try {
      // 模拟API调用，实际项目中应该调用后端接口
      await new Promise((resolve) => setTimeout(resolve, 500))

      // 创建新的路由记录
      const newRoute: AppRouteRecord = {
        id: Date.now().toString(),
        path: data.path,
        name: data.name,
        component: '/dashboard/home/<USER>', // 默认组件
        meta: {
          title: data.title,
          icon: data.icon || '&#xe676;',
          keepAlive: data.keepAlive ?? true,
          isHide: data.isHidden ?? false,
          link: data.link,
          isIframe: data.isIframe ?? false,
          perms: [`Menu:${data.name}`]
        }
      }

      // 将新菜单添加到asyncRoutes中
      asyncRoutes.push(newRoute)

      return newRoute
    } catch (error) {
      throw error instanceof Error ? error : new Error('创建菜单失败')
    }
  },

  // 更新菜单
  async updateMenu(data: MenuUpdateData): Promise<AppRouteRecord> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 500))

      // 在asyncRoutes中查找并更新菜单
      const menuIndex = asyncRoutes.findIndex(route => route.id === data.id || route.name === data.name)
      if (menuIndex === -1) {
        throw new Error('菜单不存在')
      }

      const updatedRoute: AppRouteRecord = {
        ...asyncRoutes[menuIndex],
        path: data.path || asyncRoutes[menuIndex].path,
        name: data.name || asyncRoutes[menuIndex].name,
        meta: {
          ...asyncRoutes[menuIndex].meta,
          title: data.title || asyncRoutes[menuIndex].meta.title,
          icon: data.icon || asyncRoutes[menuIndex].meta.icon,
          keepAlive: data.keepAlive ?? asyncRoutes[menuIndex].meta.keepAlive,
          isHide: data.isHidden ?? asyncRoutes[menuIndex].meta.isHide,
          link: data.link || asyncRoutes[menuIndex].meta.link,
          isIframe: data.isIframe ?? asyncRoutes[menuIndex].meta.isIframe
        }
      }

      asyncRoutes[menuIndex] = updatedRoute
      return updatedRoute
    } catch (error) {
      throw error instanceof Error ? error : new Error('更新菜单失败')
    }
  },

  // 删除菜单
  async deleteMenu(id: string): Promise<void> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 500))

      // 从asyncRoutes中删除菜单
      const menuIndex = asyncRoutes.findIndex(route => route.id === id || route.name === id)
      if (menuIndex === -1) {
        throw new Error('菜单不存在')
      }

      asyncRoutes.splice(menuIndex, 1)
    } catch (error) {
      throw error instanceof Error ? error : new Error('删除菜单失败')
    }
  }
}
