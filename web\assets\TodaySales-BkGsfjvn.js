import"./index-BOCMSBcY.js";/* empty css               *//* empty css               */import{C as a}from"./vue3-count-to.esm-Dsfbf00q.js";import{k as s,r as e,O as l,C as n,bp as t,S as c,x as o,D as r,F as d,Z as i,u,B as p,aY as f,W as v,X as m,a6 as b,aX as g}from"./vendor-9ydHGNSq.js";import{_ as x}from"./_plugin-vue_export-helper-BCo6x5W8.js";const h={class:"custom-card art-custom-card today-sales"},_={class:"sales-summary"},y={class:v(["sales-card"])},j=["innerHTML"],T=x(s({__name:"TodaySales",setup(s){const x=e([{label:"总销售额",value:999,change:"+10%",iconfont:"&#xe7d9",class:"bg-primary"},{label:"总订单量",value:300,change:"+15%",iconfont:"&#xe70f",class:"bg-warning"},{label:"产品销售量",value:56,change:"-5%",iconfont:"&#xe712",class:"bg-error"},{label:"新客户数",value:68,change:"+8%",iconfont:"&#xe77f",class:"bg-success"}]);return(s,e)=>{const T=f,k=g;return n(),l("div",h,[e[1]||(e[1]=t('<div class="custom-card-header" data-v-d9f4883c><span class="title" data-v-d9f4883c>今日销售</span><span class="subtitle" data-v-d9f4883c>销售总结</span><div class="export-btn" data-v-d9f4883c><i class="iconfont-sys" data-v-d9f4883c></i><span data-v-d9f4883c>导出</span></div></div>',1)),c("div",_,[o(k,{gutter:20},{default:r((()=>[(n(!0),l(d,null,i(u(x),((s,l)=>(n(),p(T,{span:6,xs:24,key:l},{default:r((()=>[c("div",y,[c("i",{class:"iconfont-sys",innerHTML:s.iconfont},null,8,j),c("h2",null,[o(u(a),{class:"number box-title",endVal:s.value,duration:1e3,separator:""},null,8,["endVal"])]),c("p",null,m(s.label),1),c("small",null,[e[0]||(e[0]=b(" 较昨天 ")),c("span",{class:v([-1===s.change.indexOf("+")?"text-danger":"text-success"])},m(s.change),3)])])])),_:2},1024)))),128))])),_:1})])])}}}),[["__scopeId","data-v-d9f4883c"]]);export{T as default};
