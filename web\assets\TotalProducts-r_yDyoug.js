import{_ as s}from"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";import{_ as e}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as t,C as r,S as i,x as a}from"./vendor-9ydHGNSq.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";const o={class:"card art-custom-card",style:{height:"13.3rem"}};const p=e({},[["render",function(e,p){const n=s;return r(),t("div",o,[p[0]||(p[0]=i("div",{class:"card-header"},[i("p",{class:"title",style:{"font-size":"24px"}},"55,231"),i("p",{class:"subtitle"},"商品总数")],-1)),a(n,{showAxisLabel:!1,showAxisLine:!1,showSplitLine:!1,data:[50,80,40,90,60,70],height:"7rem",barWidth:"18px"})])}]]);export{p as default};
