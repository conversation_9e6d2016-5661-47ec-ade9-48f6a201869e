import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";import{_ as s}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as t,C as i,S as e,x as c,bp as d}from"./vendor-9ydHGNSq.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";const v={class:"card art-custom-card yearly-card"};const o=s({},[["render",function(s,o){const n=a;return i(),t("div",v,[o[0]||(o[0]=e("div",{class:"card-header"},[e("p",{class:"title"},"年度销售额"),e("p",{class:"subtitle"},"按季度统计")],-1)),c(n,{showAxisLabel:!1,showAxisLine:!1,showSplitLine:!1,data:[50,80,50,90,60,70,50],barWidth:"26px",height:"calc(100% - 155px)"}),o[1]||(o[1]=d('<div class="icon-text-widget" data-v-0cbaae65><div class="item" data-v-0cbaae65><div class="icon" data-v-0cbaae65><i class="iconfont-sys" data-v-0cbaae65></i></div><div class="content" data-v-0cbaae65><p data-v-0cbaae65>¥200,858</p><span data-v-0cbaae65>线上销售</span></div></div><div class="item" data-v-0cbaae65><div class="icon" data-v-0cbaae65><i class="iconfont-sys" data-v-0cbaae65></i></div><div class="content" data-v-0cbaae65><p data-v-0cbaae65>¥102,927</p><span data-v-0cbaae65>线下销售</span></div></div></div>',1))])}],["__scopeId","data-v-0cbaae65"]]);export{o as default};
