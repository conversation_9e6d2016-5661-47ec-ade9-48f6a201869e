var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,n=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,r=(e,a)=>{for(var l in a||(a={}))i.call(a,l)&&n(e,l,a[l]);if(t)for(var l of t(a))s.call(a,l)&&n(e,l,a[l]);return e},o=(e,t)=>a(e,l(t)),d=(e,a,l)=>new Promise(((t,i)=>{var s=e=>{try{r(l.next(e))}catch(a){i(a)}},n=e=>{try{r(l.throw(e))}catch(a){i(a)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,n);r((l=l.apply(e,a)).next())}));import{a as c,f as u}from"./index-BOCMSBcY.js";/* empty css                   *//* empty css                    *//* empty css                     *//* empty css                  *//* empty css                   *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                      *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                         */import{_ as m}from"./ArtTable-ClHSxhXb.js";/* empty css                  *//* empty css                        *//* empty css               *//* empty css               */import{k as p,r as v,s as h,c as f,aa as g,d as y,E as _,p as k,V as b,O as C,C as x,R as w,a2 as A,x as j,B as S,D as E,aY as W,$ as V,a0 as z,i as L,u as T,a3 as R,a6 as O,aX as $,S as P,Y as D,ag as U,aG as I,aJ as K,X as M,aK as B,F,Z as N,aL as q,aM as X,bB as Y,bC as H,aO as Z,aP as G,aN as J,aH as Q,aI as ee,aW as ae,ab as le,ac as te,ae as ie,ah as se,bD as ne,aF as re,a1 as oe,W as de,ay as ce,n as ue,aD as me}from"./vendor-9ydHGNSq.js";import{u as pe,R as ve}from"./roleApi-CKQf8GC_.js";import{_ as he}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-CT2bh8-V.js";const fe={class:"page-content role-management-container"},ge={key:0,class:"pc-search-bar"},ye={key:1,class:"mobile-search-bar"},_e={class:"search-input-wrapper"},ke={key:3,class:"mobile-card-list"},be={key:0,class:"mobile-skeleton"},Ce={class:"skeleton-header"},xe={class:"skeleton-content"},we={class:"card-header-mobile"},Ae={class:"role-info"},je={class:"role-avatar"},Se={class:"role-icon"},Ee={class:"role-details"},We={class:"role-name"},Ve={class:"role-description"},ze={class:"role-status"},Le={class:"role-content"},Te={class:"info-section"},Re={class:"info-item"},Oe={class:"info-value"},$e={class:"info-item"},Pe={class:"info-value"},De={class:"card-footer"},Ue={class:"action-buttons-mobile"},Ie={class:"action-buttons"},Ke={key:2,class:"empty-state"},Me={key:4,class:"mobile-action-bar"},Be={class:"action-bar-container"},Fe={class:"action-icon add-icon"},Ne={class:"action-icon refresh-icon"},qe={key:5,class:"pagination-container"},Xe={key:0,class:"form-tip"},Ye={class:"dialog-footer"},He={style:{display:"flex","align-items":"center"}},Ze={key:0},Ge={key:1},Je={class:"dialog-footer"},Qe={class:"mobile-permission-header"},ea={class:"permission-stats"},aa={class:"stats-text"},la={class:"header-actions"},ta={class:"mobile-permission-content"},ia={class:"permission-list"},sa=["onClick"],na={class:"item-content"},ra={class:"item-left"},oa={class:"item-info"},da={class:"item-title"},ca={class:"item-subtitle"},ua={class:"item-right"},ma={key:0,class:"children-container"},pa={class:"item-content"},va={class:"item-left"},ha={class:"item-info"},fa={class:"item-title"},ga={class:"item-subtitle"},ya={key:0,class:"children-container level-3"},_a={class:"item-content"},ka={class:"item-left"},ba={class:"item-info"},Ca={class:"item-title"},xa={class:"item-subtitle"},wa={class:"mobile-permission-footer"},Aa={class:"footer-stats"},ja={class:"selected-count"},Sa={class:"footer-actions"},Ea=he(p(o(r({},{name:"Role"}),{__name:"index",setup(e){const a=v(!1),l=v(!1),{menuList:t}=h(c()),i=v(),s=v(!0),n=v(!1),p=f((()=>{if(Ua.value){const e=gl(Fa.value);if(0===e.length)return!1;return e.every((e=>Va.value.includes(e)))}return n.value})),he=v(!1),Ea=v(!1),Wa=v(""),Va=v([]),za=v([]),La=v(null),Ta=v({}),Ra=v(1),Oa=v(10),$a=v(0),{width:Pa}=pe(),Da=f((()=>Pa.value<768)),Ua=v(!1),Ia=()=>{Ua.value=window.innerWidth<=768},Ka={user:"普通用户",admin:"管理员",superadmin:"超级管理员"},Ma=e=>Ka[e]||e,Ba=e=>({_id:e._id||e.id||"",name:e.name||e.roleName||"",description:e.description||e.desc||e.des||"",permissions:e.permissions||e.perms||[],isSystem:"boolean"==typeof e.isSystem?e.isSystem:!!e.isSystem,createdAt:e.createdAt||e.createTime||e.createAt||e.date||"",updatedAt:e.updatedAt||e.updateTime||e.updateAt||""}),Fa=f((()=>{if(!t.value||0===t.value.length)return[];const e=a=>{const l=o(r({},a),{expanded:void 0!==a.expanded&&a.expanded});if(a.meta&&a.meta.authList&&a.meta.authList.length){const e=a.meta.authList.map((e=>({id:`${a.id}_${e.auth_mark}`,name:`${a.name}_${e.auth_mark}`,label:e.title,auth_mark:e.auth_mark,isAuth:!0,checked:e.checked||!1,expanded:!1})));l.children=l.children?[...l.children,...e]:e}return l.children&&(l.children=l.children.map(e)),l};return t.value.map(e)})),Na=v(),qa=g({name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]}),Xa=g({name:"",description:"",permissions:[]}),Ya=v([]);y((()=>{Ha()}));const Ha=()=>d(this,null,(function*(){he.value=!0;try{const e=yield ve.getRoles({page:Ra.value,limit:Oa.value,keyword:Wa.value}),{items:a,total:l}=(e=>{var a;return(null==(a=null==e?void 0:e.data)?void 0:a.items)&&Array.isArray(e.data.items)?{items:e.data.items.map(Ba),total:e.data.total||0}:(null==e?void 0:e.items)&&Array.isArray(e.items)?{items:e.items.map(Ba),total:e.total||0}:Array.isArray(e)?{items:e.map(Ba),total:e.length}:(null==e?void 0:e.data)&&Array.isArray(e.data)?{items:e.data.map(Ba),total:e.data.length}:{items:[],total:0}})(e);Ya.value=a,$a.value=l}catch(e){_.error("获取角色列表失败"),Ya.value=[],$a.value=0}finally{he.value=!1}})),Za=()=>{Ra.value=1,Ha()},Ga=()=>{Ra.value=1,Ha()};let Ja=null;const Qa=e=>{Ja&&clearTimeout(Ja),e&&""!==e.trim()||(Ja=setTimeout((()=>{Wa.value&&""!==Wa.value.trim()||(Ra.value=1,Ha())}),500))},el=e=>{Oa.value=e,Ha()},al=e=>{Ra.value=e,Ha()},ll=v("add"),tl=(e,l)=>{a.value=!0,ll.value=e,Na.value&&Na.value.resetFields(),"edit"===e&&l?(Xa.name=l.name,Xa.description=l.description,Xa.permissions=l.permissions||[],La.value=l):(Xa.name="",Xa.description="",Xa.permissions=[],La.value=null)},il=e=>{La.value=e;const a=e.permissions||[];Va.value=[...a],za.value=[...a],Ta.value={},l.value=!0,ue((()=>{ol(),Ua.value&&rl()}))},sl=()=>{Va.value=[...za.value]},nl=()=>{Va.value=[...za.value],l.value=!1},rl=()=>{Va.value&&0!==Va.value.length&&Va.value},ol=()=>{const e=i.value;if(e)try{if(e.setCheckedKeys([]),Va.value&&Va.value.length>0){const a=dl(Va.value);e.setCheckedKeys(a)}}catch(a){}},dl=e=>{const a=[];return e.forEach((l=>{const t=l.split(":");if(1===t.length){e.some((e=>e.startsWith(l+":")))||a.push(t[0])}else a.push(t[t.length-1])})),[...new Set(a)]},cl={children:"children",label:e=>{var a;return u(null==(a=e.meta)?void 0:a.title)||""}},ul=e=>{me.confirm("确定删除该角色吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((()=>d(this,null,(function*(){try{yield ve.deleteRole(e),_.success("删除成功"),Ha()}catch(a){_.error("删除角色失败")}}))))},ml=()=>d(this,null,(function*(){Na.value&&(yield Na.value.validate((e=>d(this,null,(function*(){if(e){Ea.value=!0;try{"add"===ll.value?(yield ve.createRole({name:Xa.name,description:Xa.description,permissions:Xa.permissions}),_.success("新增成功")):"edit"===ll.value&&La.value&&(yield ve.updateRole(La.value._id,{name:Xa.name,description:Xa.description,permissions:Xa.permissions}),_.success("修改成功")),a.value=!1,Ha()}catch(l){_.error("操作失败，请重试")}finally{Ea.value=!1}}})))))})),pl=()=>d(this,null,(function*(){if(La.value){Ea.value=!0;try{let e;if(Ua.value)e=vl(Va.value,[]);else{const a=i.value;if(!a)return void(Ea.value=!1);const l=a.getCheckedKeys(),t=a.getHalfCheckedKeys();e=vl(l,t)}yield ve.updateRole(La.value._id,{permissions:e}),_.success("权限保存成功"),l.value=!1,Ha()}catch(e){_.error("保存权限失败")}finally{Ea.value=!1}}})),vl=(e,a)=>{const l=new Set,t={AppointmentList:"Appointment",AppointmentConfig:"Appointment",User:"System",Role:"System",Menus:"System",Console:"Dashboard",Analysis:"Dashboard",Ecommerce:"Dashboard",Cards:"Template",Banners:"Template",Charts:"Template",Map:"Template",Chat:"Template",Calendar:"Template",Pricing:"Template",IconList:"Widgets",IconSelector:"Widgets",ImageCrop:"Widgets",Excel:"Widgets",Video:"Widgets",CountTo:"Widgets",WangEditor:"Widgets",Watermark:"Widgets",ContextMenu:"Widgets",Qrcode:"Widgets",Drag:"Widgets",TextScroll:"Widgets",Fireworks:"Widgets",ElementUI:"Widgets",Menu:"Widgets",Nested:"Widgets",ArticleList:"Article",ArticleDetail:"Article",CommentManagement:"Article",PublishArticle:"Article",ResultSuccess:"Result",ResultFail:"Result",Exception403:"Exception",Exception404:"Exception",Exception500:"Exception",ServerMonitor:"Safeguard"};e.forEach((e=>{const a=t[e];a?(l.add(a),l.add(`${a}:${e}`)):l.add(e)}));return Array.from(l)},hl=()=>{if(Ua.value){const e=!s.value,a=l=>{l.forEach((l=>{l.name&&l.children&&l.children.length>0&&(Ta.value[l.name]=e),l.children&&a(l.children)}))};return a(Fa.value),void(s.value=e)}const e=i.value;if(!e)return;const a=e.store.nodesMap;for(const l in a)a[l].expanded=!s.value;s.value=!s.value},fl=()=>{if(Ua.value){const e=gl(Fa.value),a=p.value;return Va.value=a?[]:[...e],void ue((()=>{}))}const e=i.value;if(e){if(n.value)e.setCheckedKeys([]);else{const a=gl(Fa.value);e.setCheckedKeys(a)}n.value=!n.value}},gl=e=>{const a=[],l=e=>{e.forEach((e=>{e.name&&a.push(e.name),e.children&&e.children.length>0&&l(e.children)}))};return l(e),a},yl=()=>{const e=i.value;if(!e)return;const a=e.getCheckedKeys(),l=gl(Fa.value);n.value=a.length===l.length&&l.length>0},_l=e=>e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-"):"-",kl=e=>{var a;return e.isAuth?e.label||e.title||e.name:u(null==(a=e.meta)?void 0:a.title)||e.title||e.label||e.name||"未知菜单"},bl=e=>Ta.value[e.name]||!1,Cl=(e,a)=>{if(!e.name||!a)return!1;if(a===e.name)return!0;if(e.path&&a===e.path)return!0;if(e.component&&a.includes(e.component))return!0;if(a.includes(":")){const[l,t]=a.split(":");if(e.name.toLowerCase().includes(l.toLowerCase())&&e.name.toLowerCase().includes(t.toLowerCase()))return!0}if(a.startsWith("/")){const l=a.split("/").filter(Boolean),t=e.name.toLowerCase();if(l.every((e=>t.includes(e.toLowerCase()))))return!0}return!1},xl=e=>!!e.name&&(!!Va.value.includes(e.name)||Va.value.some((a=>Cl(e,a)))),wl=e=>{if(!e.children||0===e.children.length)return!1;const a=e.children.filter((e=>xl(e)));return a.length>0&&a.length<e.children.length},Al=(e,a)=>{if(e&&e.name){if(a)Va.value.includes(e.name)||Va.value.push(e.name),e.children&&e.children.length>0&&e.children.forEach((e=>{Al(e,!0)}));else{const a=Va.value.indexOf(e.name);if(a>-1)Va.value.splice(a,1);else{Va.value.filter((a=>Cl(e,a))).forEach((e=>{const a=Va.value.indexOf(e);a>-1&&Va.value.splice(a,1)}))}e.children&&e.children.length>0&&e.children.forEach((e=>{Al(e,!1)}))}Va.value=[...Va.value]}},jl=()=>{if(!Fa.value||0===Fa.value.length)return 0;const e=a=>{let l=0;return a.forEach((a=>{a.name&&Va.value.includes(a.name)&&l++,a.children&&a.children.length>0&&(l+=e(a.children))})),l};return e(Fa.value)},Sl=e=>{var a;if(e.isAuth)return e.auth_mark||"权限项";if(e.children&&e.children.length>0){const a=e=>{let l=0;return e.forEach((e=>{e.name&&Va.value.includes(e.name)&&l++,e.children&&e.children.length>0&&(l+=a(e.children))})),l},l=e=>{let a=0;return e.forEach((e=>{e.name&&a++,e.children&&e.children.length>0&&(a+=l(e.children))})),a};return`${a(e.children)}/${l(e.children)} 项已选择`}return e.path||(null==(a=e.meta)?void 0:a.title)||"菜单项"},El=f((()=>Da.value?"total, prev, pager, next":"total, sizes, prev, pager, next, jumper"));return y((()=>{Ia(),window.addEventListener("resize",Ia)})),k((()=>{window.removeEventListener("resize",Ia),Ja&&(clearTimeout(Ja),Ja=null)})),(e,t)=>{var r,o,d;const c=V,u=W,v=R,h=$,f=D,g=K,y=B,_=R,k=m,ue=q,me=X,pe=B,ve=ae,za=te,Pa=le,Ia=ie,Ba=ne,Ha=se,Ja=re,rl=b("ripple"),ol=I;return x(),C("div",fe,[Ua.value?w("",!0):(x(),C("div",ge,[j(h,{gutter:12},{default:E((()=>[j(u,{xs:24,sm:14,md:16,lg:18,xl:20},{default:E((()=>[j(c,{modelValue:T(Wa),"onUpdate:modelValue":t[0]||(t[0]=e=>L(Wa)?Wa.value=e:null),placeholder:"角色名称/描述",onKeyup:z(Za,["enter"]),onClear:Ga,onInput:Qa,clearable:""},null,8,["modelValue"])])),_:1}),j(u,{xs:24,sm:10,md:8,lg:6,xl:4,class:"el-col2"},{default:E((()=>[A((x(),S(v,{onClick:Za},{default:E((()=>t[15]||(t[15]=[O("搜索")]))),_:1,__:[15]})),[[rl]]),A((x(),S(v,{onClick:t[1]||(t[1]=e=>tl("add"))},{default:E((()=>t[16]||(t[16]=[O("新增角色")]))),_:1,__:[16]})),[[rl]])])),_:1})])),_:1})])),Ua.value?(x(),C("div",ye,[P("div",_e,[j(c,{modelValue:T(Wa),"onUpdate:modelValue":t[2]||(t[2]=e=>L(Wa)?Wa.value=e:null),placeholder:"搜索角色名称、描述",onKeyup:z(Za,["enter"]),onClear:Ga,onInput:Qa,clearable:"",class:"search-input"},{prefix:E((()=>[j(f,null,{default:E((()=>[j(T(U))])),_:1})])),_:1},8,["modelValue"]),j(v,{type:"primary",onClick:Za,class:"search-button"},{default:E((()=>[j(f,null,{default:E((()=>[j(T(U))])),_:1})])),_:1})])])):w("",!0),Ua.value?w("",!0):A((x(),S(k,{key:2,data:T(Ya),index:""},{default:E((()=>[j(g,{label:"角色名称",prop:"name"},{default:E((e=>[O(M(Ma(e.row.name)),1)])),_:1}),j(g,{label:"描述",prop:"description"}),j(g,{label:"权限数",width:"100"},{default:E((e=>{var a;return[P("span",null,M((null==(a=e.row.permissions)?void 0:a.length)||0),1)]})),_:1}),j(g,{label:"系统角色",width:"100"},{default:E((e=>[j(y,{type:e.row.isSystem?"info":"primary"},{default:E((()=>[O(M(e.row.isSystem?"是":"否"),1)])),_:2},1032,["type"])])),_:1}),j(g,{label:"创建时间",prop:"createdAt"},{default:E((e=>[O(M(_l(e.row.createdAt)),1)])),_:1}),j(g,{fixed:"right",label:"操作",width:"200px"},{default:E((e=>[j(h,null,{default:E((()=>[j(_,{link:"",onClick:a=>il(e.row)},{default:E((()=>t[17]||(t[17]=[O(" 菜单权限 ")]))),_:2,__:[17]},1032,["onClick"]),j(_,{link:"",onClick:a=>tl("edit",e.row),disabled:e.row.isSystem},{default:E((()=>t[18]||(t[18]=[O(" 编辑 ")]))),_:2,__:[18]},1032,["onClick","disabled"]),j(_,{link:"",onClick:a=>ul(e.row._id),type:"danger",disabled:e.row.isSystem},{default:E((()=>t[19]||(t[19]=[O(" 删除 ")]))),_:2,__:[19]},1032,["onClick","disabled"])])),_:2},1024)])),_:1})])),_:1},8,["data"])),[[ol,T(he)]]),Ua.value?(x(),C("div",ke,[T(he)?(x(),C("div",be,[(x(),C(F,null,N(3,(e=>P("div",{key:e,class:"mobile-skeleton-card"},[j(me,{animated:""},{template:E((()=>[P("div",Ce,[j(ue,{variant:"circle",style:{width:"40px",height:"40px"}}),P("div",xe,[j(ue,{variant:"text",style:{width:"60%",height:"16px"}}),j(ue,{variant:"text",style:{width:"40%",height:"14px"}})])]),j(ue,{variant:"rect",style:{width:"100%",height:"60px","margin-top":"12px"}})])),_:1})]))),64))])):w("",!0),T(he)?w("",!0):(x(),S(J,{key:1,name:"card-list",tag:"div",class:"card-container"},{default:E((()=>[(x(!0),C(F,null,N(T(Ya),((e,a)=>{var l;return x(),C("div",{key:e._id,class:"role-card"},[P("div",we,[P("div",Ae,[P("div",je,[P("div",Se,[j(f,{size:20},{default:E((()=>[j(T(Y))])),_:1})])]),P("div",Ee,[P("div",We,M(Ma(e.name)),1),P("div",Ve,M(e.description||"-"),1)])]),P("div",ze,[j(pe,{type:e.isSystem?"info":"primary",size:"small"},{default:E((()=>[O(M(e.isSystem?"系统角色":"自定义"),1)])),_:2},1032,["type"])])]),P("div",Le,[P("div",Te,[P("div",Re,[t[20]||(t[20]=P("span",{class:"info-label"},"权限数量",-1)),P("span",Oe,M((null==(l=e.permissions)?void 0:l.length)||0)+" 个",1)]),P("div",$e,[t[21]||(t[21]=P("span",{class:"info-label"},"创建时间",-1)),P("span",Pe,M(_l(e.createdAt)),1)])])]),P("div",De,[P("div",Ue,[P("div",Ie,[j(_,{size:"small",type:"primary",plain:"",onClick:a=>il(e)},{default:E((()=>[j(f,null,{default:E((()=>[j(T(H))])),_:1}),t[22]||(t[22]=P("span",null,"菜单权限",-1))])),_:2,__:[22]},1032,["onClick"]),j(_,{size:"small",type:"success",plain:"",onClick:a=>tl("edit",e),disabled:e.isSystem},{default:E((()=>[j(f,null,{default:E((()=>[j(T(Z))])),_:1}),t[23]||(t[23]=P("span",null,"编辑",-1))])),_:2,__:[23]},1032,["onClick","disabled"]),j(_,{size:"small",type:"danger",plain:"",onClick:a=>ul(e._id),disabled:e.isSystem},{default:E((()=>[j(f,null,{default:E((()=>[j(T(G))])),_:1}),t[24]||(t[24]=P("span",null,"删除",-1))])),_:2,__:[24]},1032,["onClick","disabled"])])])])])})),128))])),_:1})),T(he)||0!==T(Ya).length?w("",!0):(x(),C("div",Ke,[t[26]||(t[26]=P("div",{class:"empty-icon"},"👥",-1)),t[27]||(t[27]=P("div",{class:"empty-text"},"暂无角色数据",-1)),j(_,{type:"primary",onClick:t[3]||(t[3]=e=>tl("add")),class:"empty-action"},{default:E((()=>[j(f,null,{default:E((()=>[j(T(Q))])),_:1}),t[25]||(t[25]=P("span",null,"新增角色",-1))])),_:1,__:[25]})]))])):w("",!0),Ua.value?(x(),C("div",Me,[P("div",Be,[P("div",{class:"action-item primary-action",onClick:t[4]||(t[4]=e=>tl("add"))},[P("div",Fe,[j(f,{size:24},{default:E((()=>[j(T(Q))])),_:1})]),t[28]||(t[28]=P("span",{class:"action-label"},"新增",-1))]),P("div",{class:"action-item",onClick:t[5]||(t[5]=(...a)=>e.getRoleList&&e.getRoleList(...a))},[P("div",Ne,[j(f,{size:20},{default:E((()=>[j(T(ee))])),_:1})]),t[29]||(t[29]=P("span",{class:"action-label"},"刷新",-1))])])])):w("",!0),Ua.value?w("",!0):(x(),C("div",qe,[j(ve,{"current-page":T(Ra),"onUpdate:currentPage":t[6]||(t[6]=e=>L(Ra)?Ra.value=e:null),"page-size":T(Oa),"onUpdate:pageSize":t[7]||(t[7]=e=>L(Oa)?Oa.value=e:null),"page-sizes":[10,20,50,100],total:T($a),layout:El.value,onSizeChange:el,onCurrentChange:al,small:Da.value,"pager-count":Da.value?5:7,background:""},null,8,["current-page","page-size","total","layout","small","pager-count"])])),j(Ia,{modelValue:T(a),"onUpdate:modelValue":t[11]||(t[11]=e=>L(a)?a.value=e:null),title:"add"===T(ll)?"新增角色":`编辑角色 - ${(null==(r=T(La))?void 0:r.name)?Ma(T(La).name):""}`,width:"30%","align-center":""},{footer:E((()=>[P("div",Ye,[j(v,{onClick:t[10]||(t[10]=e=>a.value=!1)},{default:E((()=>t[30]||(t[30]=[O("取消")]))),_:1,__:[30]}),j(v,{type:"primary",onClick:ml,loading:T(Ea)},{default:E((()=>t[31]||(t[31]=[O("提交")]))),_:1,__:[31]},8,["loading"])])])),default:E((()=>[j(Pa,{ref_key:"formRef",ref:Na,model:T(Xa),rules:T(qa),"label-width":"120px"},{default:E((()=>[j(za,{label:"角色名称",prop:"name"},{default:E((()=>[j(c,{modelValue:T(Xa).name,"onUpdate:modelValue":t[8]||(t[8]=e=>T(Xa).name=e)},null,8,["modelValue"]),"edit"===T(ll)&&Ka[T(Xa).name]?(x(),C("div",Xe," 系统预设角色："+M(Ma(T(Xa).name)),1)):w("",!0)])),_:1}),j(za,{label:"描述",prop:"description"},{default:E((()=>[j(c,{modelValue:T(Xa).description,"onUpdate:modelValue":t[9]||(t[9]=e=>T(Xa).description=e),type:"textarea",rows:3},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),Ua.value?w("",!0):(x(),S(Ia,{key:6,modelValue:T(l),"onUpdate:modelValue":t[12]||(t[12]=e=>L(l)?l.value=e:null),title:`菜单权限 - ${(null==(o=T(La))?void 0:o.name)?Ma(T(La).name):""}`,width:"520px","align-center":"",class:"el-dialog-border",onClose:sl},{footer:E((()=>[P("div",Je,[j(v,{onClick:hl},{default:E((()=>[O(M(T(s)?"全部收起":"全部展开"),1)])),_:1}),j(v,{onClick:fl,style:{"margin-left":"8px"}},{default:E((()=>[O(M(T(n)?"取消全选":"全部选择"),1)])),_:1}),j(v,{type:"primary",onClick:pl,loading:T(Ea)},{default:E((()=>t[32]||(t[32]=[O("保存")]))),_:1,__:[32]},8,["loading"])])])),default:E((()=>[j(Ha,{height:"70vh"},{default:E((()=>[j(Ba,{ref_key:"treeRef",ref:i,data:Fa.value,"show-checkbox":"","node-key":"name","default-expand-all":T(s),"default-checked-keys":T(Va),props:cl,onCheck:yl},{default:E((({data:e})=>[P("div",He,[e.isAuth?(x(),C("span",Ze,M(e.label),1)):(x(),C("span",Ge,M(cl.label(e)),1))])])),_:1},8,["data","default-expand-all","default-checked-keys"])])),_:1})])),_:1},8,["modelValue","title"])),Ua.value?(x(),S(Ia,{key:7,modelValue:T(l),"onUpdate:modelValue":t[14]||(t[14]=e=>L(l)?l.value=e:null),title:`菜单权限 - ${(null==(d=T(La))?void 0:d.name)?Ma(T(La).name):""}`,width:"95%","align-center":"",class:"el-dialog-border mobile-permission-dialog",onClose:sl},{footer:E((()=>[P("div",wa,[P("div",Aa,[P("span",ja,"已选择 "+M(jl())+" 项",1)]),P("div",Sa,[j(v,{onClick:nl,class:"cancel-btn"},{default:E((()=>t[33]||(t[33]=[O("取消")]))),_:1,__:[33]}),j(v,{type:"primary",onClick:pl,loading:T(Ea),class:"save-btn"},{default:E((()=>t[34]||(t[34]=[O(" 保存权限 ")]))),_:1,__:[34]},8,["loading"])])])])),default:E((()=>[P("div",Qe,[P("div",ea,[P("span",aa,"已选择 "+M(jl())+" 项权限",1)]),P("div",la,[j(_,{size:"small",onClick:hl,class:"action-btn"},{default:E((()=>[j(f,null,{default:E((()=>[j(T(H))])),_:1}),O(" "+M(T(s)?"收起":"展开"),1)])),_:1}),j(_,{size:"small",onClick:fl,class:"action-btn",type:"primary",plain:""},{default:E((()=>[j(f,null,{default:E((()=>[j(T(Y))])),_:1}),O(" "+M(p.value?"取消全选":"全选"),1)])),_:1})])]),P("div",ta,[P("div",ia,[(x(!0),C(F,null,N(Fa.value,((e,a)=>(x(),C("div",{key:e.name,class:"permission-group"},[P("div",{class:"permission-item level-1",onClick:a=>(e=>{e.name&&(Ta.value[e.name]=!Ta.value[e.name])})(e)},[P("div",na,[P("div",ra,[j(Ja,{"model-value":xl(e),indeterminate:wl(e),onChange:a=>Al(e,a),onClick:t[13]||(t[13]=oe((()=>{}),["stop"])),class:"item-checkbox"},null,8,["model-value","indeterminate","onChange"]),P("div",oa,[P("div",da,M(kl(e)),1),P("div",ca,M(Sl(e)),1)])]),P("div",ua,[j(f,{class:de(["expand-icon",{expanded:bl(e)}])},{default:E((()=>[j(T(ce))])),_:2},1032,["class"])])])],8,sa),bl(e)&&e.children?(x(),C("div",ma,[(x(!0),C(F,null,N(e.children,(e=>(x(),C("div",{key:e.name,class:"permission-item level-2"},[P("div",pa,[P("div",va,[j(Ja,{"model-value":xl(e),indeterminate:wl(e),onChange:a=>Al(e,a),class:"item-checkbox"},null,8,["model-value","indeterminate","onChange"]),P("div",ha,[P("div",fa,M(kl(e)),1),P("div",ga,M(Sl(e)),1)])])]),e.children?(x(),C("div",ya,[(x(!0),C(F,null,N(e.children,(e=>(x(),C("div",{key:e.name,class:"permission-item level-3"},[P("div",_a,[P("div",ka,[j(Ja,{"model-value":xl(e),onChange:a=>Al(e,a),class:"item-checkbox"},null,8,["model-value","onChange"]),P("div",ba,[P("div",Ca,M(kl(e)),1),P("div",xa,M(Sl(e)),1)])])])])))),128))])):w("",!0)])))),128))])):w("",!0)])))),128))])])])),_:1},8,["modelValue","title"])):w("",!0)])}}})),[["__scopeId","data-v-3ed68e2b"]]);export{Ea as default};
