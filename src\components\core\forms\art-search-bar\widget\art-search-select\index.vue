<template>
  <el-select
    v-model="value"
    v-bind="config"
    @change="(val) => changeValue(val)"
    @keyup.enter="handleEnterKey"
    ref="selectRef"
  >
    <el-option
      v-for="v in options"
      :key="v.value"
      :label="v.label"
      :value="v.value"
      :disabled="v.disabled || false"
    >
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
  import { SearchFormItem } from '@/types'
  import { useI18n } from 'vue-i18n'
  import { computed, reactive, ref } from 'vue'

  const { t } = useI18n()

  // 选择框引用
  const selectRef = ref(null)

  // 定义组件值类型
  export type ValueVO = unknown

  // 定义组件props
  const prop = defineProps<{
    value: ValueVO // 选择框的值
    item: SearchFormItem // 表单项配置
  }>()

  // 定义emit事件
  const emit = defineEmits<{
    (e: 'update:value', value: ValueVO): void // 更新选择框值的事件
  }>()

  // 计算属性:处理v-model双向绑定
  const value = computed({
    get: () => prop.value as string,
    set: (value: ValueVO) => emit('update:value', value)
  })

  // 合并默认配置和自定义配置
  const config = reactive({
    placeholder: (t('table.searchBar.searchSelectPlaceholder') + (prop.item.label || '')) as string, // 修改默认placeholder文案
    ...(prop.item.config || {})
  })

  // 选择框值变化处理函数
  const changeValue = (val: unknown): void => {
    if (prop.item.onChange) {
      prop.item.onChange({
        prop: prop.item.prop,
        val
      })
    }
  }

  // 处理回车键
  const handleEnterKey = () => {
    // 1. 关闭下拉菜单并失焦
    if (selectRef.value) {
      // 使用Element Plus的blur方法
      ;(selectRef.value as any).blur()
    }

    // 2. 触发搜索按钮点击
    setTimeout(() => {
      const searchBtn = document.querySelector('.search-button') as HTMLElement
      if (searchBtn) {
        searchBtn.click()
      }
    }, 100)
  }

  // 计算属性:处理选项数据
  const options = computed(() => {
    if (prop.item.options) {
      // 判断options是数组还是函数
      if (Array.isArray(prop.item.options)) {
        return prop.item.options
      } else {
        return prop.item.options()
      }
    } else {
      return []
    }
  })
</script>
