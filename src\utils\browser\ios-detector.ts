// iOS设备检测和兼容性处理工具

/**
 * 检测是否为iOS设备
 */
export function isIOS(): boolean {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
         (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)
}

/**
 * 检测是否为iOS Safari浏览器
 */
export function isIOSSafari(): boolean {
  const ua = navigator.userAgent
  return isIOS() && /Safari/.test(ua) && !/CriOS|FxiOS|OPiOS|mercury/.test(ua)
}

/**
 * 获取iOS版本
 */
export function getIOSVersion(): number | null {
  if (!isIOS()) return null
  
  const match = navigator.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/)
  if (match) {
    return parseInt(match[1], 10)
  }
  return null
}

/**
 * 检测是否为iOS 15+
 */
export function isIOS15Plus(): boolean {
  const version = getIOSVersion()
  return version !== null && version >= 15
}

/**
 * 初始化iOS兼容性处理
 */
export function initIOSCompatibility(): void {
  if (!isIOS()) return

  // 添加iOS标识类
  document.documentElement.classList.add('ios-device')
  
  if (isIOSSafari()) {
    document.documentElement.classList.add('ios-safari')
  }
  
  if (isIOS15Plus()) {
    document.documentElement.classList.add('ios-15-plus')
  }

  // 修复iOS视口高度问题
  fixIOSViewportHeight()
  
  // 修复iOS滚动问题
  fixIOSScrolling()
  
  // 修复iOS按钮点击问题
  fixIOSButtonClicks()
  
  // 修复iOS输入框问题
  fixIOSInputs()
}

/**
 * 修复iOS视口高度问题
 */
function fixIOSViewportHeight(): void {
  const setViewportHeight = () => {
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)
  }
  
  setViewportHeight()
  window.addEventListener('resize', setViewportHeight)
  window.addEventListener('orientationchange', () => {
    setTimeout(setViewportHeight, 100)
  })
}

/**
 * 修复iOS滚动问题
 */
function fixIOSScrolling(): void {
  // 为所有滚动容器添加iOS滚动优化
  const scrollableElements = document.querySelectorAll(
    '.scrollable, .el-scrollbar__wrap, .el-table__body-wrapper, .overflow-auto, .overflow-y-auto'
  )
  
  scrollableElements.forEach(element => {
    const el = element as HTMLElement
    el.style.webkitOverflowScrolling = 'touch'
    el.style.overflowScrolling = 'touch'
  })
  
  // 监听新添加的滚动元素
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement
          const scrollableChildren = element.querySelectorAll(
            '.scrollable, .el-scrollbar__wrap, .el-table__body-wrapper, .overflow-auto, .overflow-y-auto'
          )
          
          scrollableChildren.forEach(child => {
            const el = child as HTMLElement
            el.style.webkitOverflowScrolling = 'touch'
            el.style.overflowScrolling = 'touch'
          })
        }
      })
    })
  })
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
}

/**
 * 修复iOS按钮点击问题
 */
function fixIOSButtonClicks(): void {
  // 为所有按钮添加点击优化
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement
    const button = target.closest('button, .el-button, [role="button"], .clickable')
    
    if (button) {
      // 添加点击反馈
      button.classList.add('ios-clicking')
      setTimeout(() => {
        button.classList.remove('ios-clicking')
      }, 150)
    }
  })
  
  // 添加CSS样式
  const style = document.createElement('style')
  style.textContent = `
    .ios-clicking {
      opacity: 0.7 !important;
      transform: scale(0.98) !important;
      transition: all 0.1s ease !important;
    }
  `
  document.head.appendChild(style)
}

/**
 * 修复iOS输入框问题
 */
function fixIOSInputs(): void {
  // 防止输入时页面缩放
  const inputs = document.querySelectorAll('input, textarea, select')
  inputs.forEach(input => {
    const el = input as HTMLElement
    if (el.style.fontSize === '' || parseInt(el.style.fontSize) < 16) {
      el.style.fontSize = '16px'
    }
  })
  
  // 监听新添加的输入框
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement
          const inputs = element.querySelectorAll('input, textarea, select')
          
          inputs.forEach(input => {
            const el = input as HTMLElement
            if (el.style.fontSize === '' || parseInt(el.style.fontSize) < 16) {
              el.style.fontSize = '16px'
            }
          })
        }
      })
    })
  })
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
}

/**
 * 修复iOS弹窗滚动问题
 */
export function fixIOSModalScrolling(modalElement: HTMLElement): void {
  if (!isIOS()) return
  
  modalElement.style.webkitOverflowScrolling = 'touch'
  modalElement.style.overflowScrolling = 'touch'
  
  // 防止背景滚动
  const originalBodyOverflow = document.body.style.overflow
  document.body.style.overflow = 'hidden'
  
  // 清理函数
  return () => {
    document.body.style.overflow = originalBodyOverflow
  }
}

/**
 * 获取安全区域信息
 */
export function getSafeAreaInsets(): {
  top: number
  right: number
  bottom: number
  left: number
} {
  const style = getComputedStyle(document.documentElement)
  
  return {
    top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
    right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
    bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
    left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')
  }
}
