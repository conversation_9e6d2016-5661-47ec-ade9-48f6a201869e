var e=Object.defineProperty,a=Object.defineProperties,s=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,i=(a,s,l)=>s in a?e(a,s,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[s]=l,o=(e,a)=>{for(var s in a||(a={}))r.call(a,s)&&i(e,s,a[s]);if(l)for(var s of l(a))t.call(a,s)&&i(e,s,a[s]);return e},n=(e,a,s)=>new Promise(((l,r)=>{var t=e=>{try{o(s.next(e))}catch(a){r(a)}},i=e=>{try{o(s.throw(e))}catch(a){r(a)}},o=e=>e.done?l(e.value):Promise.resolve(e.value).then(t,i);o((s=s.apply(e,a)).next())}));import{d}from"./index-BOCMSBcY.js";/* empty css                   *//* empty css                *//* empty css                  *//* empty css                     *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                      *//* empty css                 *//* empty css                         */import{_ as u}from"./ArtTable-ClHSxhXb.js";/* empty css                  *//* empty css                        *//* empty css                    *//* empty css               *//* empty css               */import{k as c,c as m,r as p,aa as v,d as f,E as g,p as w,V as y,O as b,C as h,R as k,B as _,a2 as x,x as C,D as j,aY as P,$ as U,a0 as V,u as I,a3 as z,a6 as O,aX as N,S,Y as T,ag as B,X as A,a$ as R,aG as W,aJ as $,Q as q,aK as D,aj as E,aB as L,az as F,aA as K,F as M,Z as X,aL as Y,aM as H,aO as Z,aP as G,aN as J,aH as Q,aI as ee,ai as ae,aW as se,ab as le,ac as re,al as te,am as ie,at as oe,a5 as ne,i as de,ae as ue,b8 as ce,b9 as me,aD as pe,l as ve}from"./vendor-9ydHGNSq.js";import{u as fe,A as ge}from"./ArtButtonTable-BDkAL_5A.js";import{U as we}from"./userManageApi-C7bYjqv8.js";import{u as ye,R as be}from"./roleApi-CKQf8GC_.js";import{u as he}from"./useNetworkStatus-BzVqGruR.js";import{_ as ke}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-CT2bh8-V.js";const _e={class:"page-content user-management-container"},xe={key:0,class:"pc-search-bar"},Ce={key:1,class:"mobile-search-bar"},je={class:"search-input-wrapper"},Pe={class:"network-message"},Ue={class:"user",style:{display:"flex","align-items":"center"}},Ve=["src"],Ie={class:"user-name"},ze={class:"nickname"},Oe={key:1},Ne={class:"created-time-cell"},Se={class:"operation-cell"},Te={key:4,class:"mobile-card-list"},Be={key:0,class:"mobile-skeleton"},Ae={class:"skeleton-header"},Re={class:"skeleton-content"},We={class:"card-header-mobile"},$e={class:"user-info"},qe={class:"user-avatar"},De=["src","alt"],Ee={class:"user-details"},Le={class:"user-name"},Fe={class:"user-nickname"},Ke={class:"user-status"},Me={class:"user-content"},Xe={class:"info-section"},Ye={class:"info-item"},He={class:"info-value"},Ze={key:1},Ge={class:"info-item"},Je={class:"info-value"},Qe={class:"card-footer"},ea={class:"action-buttons-mobile"},aa={class:"switch-wrapper"},sa={class:"action-buttons"},la={key:2,class:"empty-state"},ra={key:5,class:"mobile-action-bar"},ta={class:"action-bar-container"},ia={class:"action-icon add-icon"},oa={class:"action-icon refresh-icon"},na={class:"pagination-container"},da={class:"dialog-footer"},ua=["element-loading-text"],ca={key:0,class:"admin-password-step"},ma={key:1,class:"security-code-step"},pa={class:"security-code-display"},va={key:2},fa={class:"dialog-footer"};var ga,wa;const ya=ke(c((ga=o({},{name:"User"}),wa={__name:"index",setup(e){const a=d(),s=m((()=>{const e=a.getUserInfo.roles||[];return e.includes("admin")||e.includes("superadmin")})),l=m((()=>(a.getUserInfo.roles||[]).includes("superadmin"))),r=m((()=>s.value)),t=m((()=>l.value?Aa.value.filter((e=>"superadmin"!==e.name)):s.value?Aa.value.filter((e=>"user"===e.name)):[])),i=p("add"),c=p(!1),ke=p(!1),{width:ga}=ye(),wa=p(!1),ya=p(!1),ba=()=>{ya.value=window.innerWidth<=768},ha=p(!1),ka=p(!1),_a=p(0),xa=p("正在获取用户信息..."),Ca=p(),ja=p(),Pa=v({adminPassword:""}),Ua=p(null),Va=v({securityCode:"",targetUser:{userId:"",username:"",nickname:""},validUntil:"",warning:""}),Ia=v({userId:"",username:"",securityCode:"",newPassword:"",confirmPassword:"",adminPassword:""}),za=he({maxRetries:3,retryInterval:5e3,onReconnect:()=>{g.success("网络已恢复，重新加载数据"),Ha()},onOffline:()=>{wa.value=!0}}),Oa=m((()=>"网络连接已断开")),Na=m((()=>za.retryCount.value<za.maxRetries?`正在尝试重新连接 (${za.retryCount.value}/${za.maxRetries})...`:"已达到最大重试次数，请检查网络后手动刷新")),Sa=()=>n(this,null,(function*(){(yield za.checkNetworkConnection())?Ha():g.error("网络仍然不可用，请稍后再试")})),Ta={user:"普通用户",admin:"管理员",superadmin:"超级管理员"},Ba=e=>Ta[e]||e,Aa=p([]),Ra=v(o({},{keyword:""})),Wa=v({currentPage:1,pageSize:10,total:0}),$a=p([]),qa=p(!1),Da=()=>{Wa.currentPage=1,qa.value=!(!Ra.keyword||!Ra.keyword.trim()),Ha()},Ea=()=>{qa.value=!1,Wa.currentPage=1,Ha()};let La=null;const Fa=e=>{La&&clearTimeout(La),e&&""!==e.trim()||(La=setTimeout((()=>{Ra.keyword&&""!==Ra.keyword.trim()||(qa.value=!1,Wa.currentPage=1,Ha())}),500))},Ka=(e,a)=>n(this,null,(function*(){if(s.value)if(i.value=e,Xa.value&&Xa.value.resetFields(),"edit"===e&&a)try{ke.value=!0;const e=yield we.getUserDetail(a.userId);Ya.username=e.username,Ya.nickname=e.nickname||"",Ya.role=e.roles?e.roles[0]:"",Ya.avatar=e.avatar||"https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg",Ya.userId=e.userId,Ya.password="",ke.value=!1,c.value=!0}catch(l){g.error("获取用户详情失败"),ke.value=!1}else Ya.username="",Ya.nickname="",Ya.role="",Ya.avatar="https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg",Ya.userId="",Ya.password="",c.value=!0;else g.error("您没有权限执行此操作")})),Ma=e=>{if(!s.value)return void g.error("您没有权限执行此操作");const l=a.getUserInfo.id;e!==l?za.isOnline.value?pe.confirm("确定要删除该用户吗？","删除用户",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((()=>n(this,null,(function*(){try{const a=yield we.deleteUser(e);g.success(a.message||"删除用户成功"),Ha()}catch(a){let e="删除用户失败";if(a.response)switch(a.response.status){case 400:e="无效的用户ID或尝试删除当前登录用户";break;case 401:e="未授权，请重新登录";break;case 403:e="权限不足，只有超级管理员可以删除管理员账号";break;case 404:e="用户不存在"}g.error(e)}})))).catch((()=>{})):g.error("网络连接已断开，无法执行此操作"):g.error("不能删除当前登录的用户")};fe((()=>[{type:"selection"},{prop:"avatar",label:"用户信息",minWidth:ga.value<500?180:250,formatter:e=>ve("div",{class:"user",style:"display: flex; align-items: center"},[ve("img",{class:"avatar",src:e.avatar||"https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg",onerror:"this.src='https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg';",style:ga.value<500?"width: 35px; height: 35px;":""}),ve("div",{style:ga.value<500?"margin-left: 6px;":""},[ve("p",{class:"user-name"},e.username),ve("p",{class:"nickname"},e.nickname||"-")])])},{prop:"role",label:"角色",width:150,formatter:e=>{if(!e.roles||!e.roles.length)return"-";const a=e.roles[0],s=Ba(a);return ve(D,{type:"superadmin"===a?"danger":"admin"===a?"warning":"info"},(()=>s))}},{prop:"status",label:"状态",width:100,formatter:e=>{const a="active"===(e.status||"active");return ve(D,{type:a?"success":"danger",effect:"dark"},(()=>a?"正常":"已禁用"))}},{prop:"createdAt",label:"创建时间",sortable:!0,formatter:e=>rs(e.createdAt)},{prop:"operation",label:"操作",width:ga.value<768?120:150,formatter:e=>{const r=a.getUserInfo.id,t=e.roles.includes("superadmin"),i=e.roles.includes("admin"),o=e.userId===r,n=e.status||"active",d="active"===n;return t&&!l.value?"-":ve("div",{class:"operation-buttons"},[ve(E,{modelValue:d,"onUpdate:modelValue":()=>is(e.userId,n),activeColor:"#13ce66",inactiveColor:"#ff4949",class:"status-switch",disabled:!s.value||o||t||i&&!l.value}),ve(L,{trigger:"click"},{default:()=>ve(ge,{type:"more",class:"more-button"}),dropdown:()=>ve(F,{},[ve(K,{onClick:()=>Ka("edit",e),disabled:!s.value||t},{default:()=>"编辑"}),...!l.value||o||t?[]:[ve(K,{onClick:()=>os(e)},{default:()=>"修改密码"})],ve(K,{onClick:()=>Ma(e.userId),disabled:!s.value||o||i&&!l.value||t},{default:()=>"删除"})])})])}}]));const Xa=p(),Ya=v({userId:"",username:"",nickname:"",avatar:"https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg",password:"",role:""});f((()=>{Ha(),Za()}));const Ha=()=>n(this,null,(function*(){if(za.isOnline.value){ke.value=!0,wa.value=!1;try{const e=yield we.getUserList({page:Wa.currentPage,limit:Wa.pageSize,keyword:Ra.keyword});if(e){let a=[...e.items||[]];a.sort(((e,a)=>{const s=e.roles&&e.roles.includes("superadmin"),l=a.roles&&a.roles.includes("superadmin"),r=e.roles&&e.roles.includes("admin"),t=a.roles&&a.roles.includes("admin");return s&&!l?-1:!s&&l?1:r&&!t?-1:!r&&t?1:new Date(a.createdAt).getTime()-new Date(e.createdAt).getTime()})),$a.value=[...a],Wa.total=e.total||0,Wa.currentPage=e.page||1}}catch(e){g.error("获取用户列表失败"),$a.value=[],Wa.total=0,za.checkNetworkConnection().then((e=>{e||(wa.value=!0)}))}finally{ke.value=!1}}else wa.value=!0})),Za=()=>n(this,null,(function*(){if(za.isOnline.value)try{const e=yield be.getRoles({page:1,limit:100});e&&e.items&&(Aa.value=e.items)}catch(e){Aa.value=[],za.checkNetworkConnection()}})),Ga=v({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"},{validator:(e,a,s)=>{/[\u4e00-\u9fa5]/.test(a)?s(new Error("用户名不能包含中文字符")):s()},trigger:"blur"}],password:[{required:"add"===i.value,message:"请输入密码",trigger:"blur"},{min:6,max:15,message:"密码长度在 6 到 15 个字符",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}]}),Ja=v({adminPassword:[{required:!0,message:"请输入管理员密码",trigger:"blur"},{min:6,max:15,message:"密码长度在 6 到 15 个字符",trigger:"blur"}]}),Qa=v({securityCode:[{required:!0,message:"请输入安全码",trigger:"blur"},{len:8,message:"安全码长度为8位",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,max:15,message:"密码长度在 6 到 15 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(e,a,s)=>{a!==Ia.newPassword?s(new Error("两次输入的密码不一致")):s()},trigger:"blur"}],adminPassword:[{required:!0,message:"请输入管理员密码进行二次确认",trigger:"blur"},{min:6,max:15,message:"密码长度在 6 到 15 个字符",trigger:"blur"}]}),es=e=>{"string"==typeof Ya[e]&&(Ya[e]=Ya[e].trim())},as=()=>n(this,null,(function*(){za.isOnline.value?Xa.value&&(l.value||"admin"!==Ya.role?yield Xa.value.validate((e=>n(this,null,(function*(){if(e)try{if("add"===i.value)yield we.createUser({username:Ya.username,password:Ya.password,nickname:Ya.nickname,avatar:Ya.avatar,roles:[Ya.role]}),g.success("添加用户成功");else{const e=yield we.updateUser({userId:Ya.userId,username:Ya.username,nickname:Ya.nickname,avatar:Ya.avatar,roles:[Ya.role]});g.success("更新用户成功"),e.needRelogin&&pe.confirm("由于您修改了关键信息，需要重新登录才能生效","需要重新登录",{confirmButtonText:"立即登录",cancelButtonText:"稍后处理",type:"warning"}).then((()=>{a.logOut()})).catch((()=>{}))}c.value=!1,Ha()}catch(s){let e="add"===i.value?"添加用户失败":"更新用户失败";if(s.response)switch(s.response.status){case 400:e="请求参数错误或用户名已存在";break;case 401:e="未认证，请重新登录";break;case 403:e="权限不足或尝试创建超级管理员"}g.error(e)}})))):g.error("您没有权限分配管理员角色")):g.error("网络连接已断开，无法提交表单")})),ss=e=>{Wa.pageSize=e,Ha()},ls=e=>{Wa.currentPage=e,Ha()},rs=e=>e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-"):"-",ts=()=>{Ya.avatar?pe.alert(`<div style="text-align:center"><img src="${Ya.avatar}" style="max-width: 100%; max-height: 300px;" onerror="this.src='https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg'; this.onerror=null;"></div>`,"头像预览",{dangerouslyUseHTMLString:!0,showConfirmButton:!1,showCancelButton:!0,cancelButtonText:"关闭"}):g.warning("请先输入头像URL")},is=(e,l)=>n(this,null,(function*(){if(!s.value)return void g.error("您没有权限执行此操作");const r=a.getUserInfo.id;if(e===r)return void g.error("不能修改当前登录用户的状态");if(!za.isOnline.value)return void g.error("网络连接已断开，无法执行此操作");const t="disabled"===l?"active":"disabled",i="disabled"===t?"禁用":"启用";pe.confirm(`确定要${i}该用户吗？`,`${i}用户`,{confirmButtonText:"确定",cancelButtonText:"取消",type:"disabled"===t?"warning":"info"}).then((()=>n(this,null,(function*(){try{const a=yield we.updateUserStatus({userId:e,status:t});g.success(a.message||`${i}用户成功`),Ha()}catch(a){let e=`${i}用户失败`;if(a.response)switch(a.response.status){case 400:e="无效的状态值";break;case 401:e="未授权，请重新登录";break;case 403:e="权限不足，不能修改超级管理员的状态";break;case 404:e="用户不存在"}g.error(e)}})))).catch((()=>{}))})),os=e=>n(this,null,(function*(){var s;if(!l.value)return void g.error("您没有权限重置用户密码");const r=a.getUserInfo.id;if(e.userId!==r)if(e.roles.includes("superadmin"))g.error("不能重置超级管理员的密码");else try{_a.value=0,ns(),ds(),fs(),Ua.value={userId:e.userId,username:e.username,nickname:e.nickname},Ia.userId=e.userId,Ia.username=e.username,ha.value=!0}catch(t){let e="生成安全码失败";if(null==(s=t.response)?void 0:s.status)switch(t.response.status){case 400:e="请求参数错误";break;case 403:e="权限不足，只有超级管理员可以操作";break;case 404:e="目标用户不存在";break;case 500:e="服务器错误"}else t.message&&(e=t.message);g.error(e),ka.value=!1}else g.error("不能重置当前登录用户的密码")})),ns=()=>{Object.assign(Ia,{userId:"",username:"",securityCode:"",newPassword:"",confirmPassword:"",adminPassword:""})},ds=()=>{Object.assign(Va,{securityCode:"",targetUser:{userId:"",username:"",nickname:""},validUntil:"",warning:""})},us=()=>{_a.value=2},cs=()=>{_a.value=1},ms=()=>n(this,null,(function*(){try{yield navigator.clipboard.writeText(Va.securityCode),g.success("安全码已复制到剪贴板")}catch(e){g.error("复制失败，请手动复制")}})),ps=()=>{ha.value=!1,_a.value=0,ns(),ds(),fs(),Ua.value=null,Ca.value&&Ca.value.resetFields(),ja.value&&ja.value.resetFields()},vs=()=>n(this,null,(function*(){ja.value&&(yield ja.value.validate((e=>n(this,null,(function*(){var a,s,l;if(e){ka.value=!0,xa.value="正在验证管理员密码并生成安全码...";try{const e=yield we.generateResetSecurityCode(Ua.value.userId,{adminPassword:Pa.adminPassword});Object.assign(Va,{securityCode:e.securityCode,targetUser:e.targetUser,validUntil:e.validUntil,warning:e.warning,securityNotice:e.securityNotice}),_a.value=1,g.success("管理员密码验证成功，安全码已生成")}catch(r){let e="生成安全码失败";if(null==(a=r.response)?void 0:a.status)switch(r.response.status){case 400:e=(null==(s=r.response.data)?void 0:s.message)||"管理员密码错误，无法生成安全码";break;case 403:e="权限不足，只有超级管理员可以操作";break;case 404:e="目标用户不存在";break;case 500:e="服务器错误，请稍后重试";break;default:e=(null==(l=r.response.data)?void 0:l.message)||"生成安全码失败"}else r.message&&(e=r.message);g.error(e)}finally{ka.value=!1}}})))))})),fs=()=>{var e;Pa.adminPassword="",null==(e=ja.value)||e.clearValidate()},gs=()=>n(this,null,(function*(){za.isOnline.value?Ca.value&&(yield Ca.value.validate((e=>n(this,null,(function*(){var a,s,l,r;if(e){ka.value=!0,xa.value="正在重置密码...";try{const e=yield we.resetUserPassword(Ia.userId,{newPassword:Ia.newPassword,confirmPassword:Ia.confirmPassword,securityCode:Ia.securityCode,adminPassword:Ia.adminPassword});g.success(e.message||"密码重置成功"),ps()}catch(t){let e="密码重置失败";if(null==(a=t.response)?void 0:a.status)switch(t.response.status){case 400:e=(null==(s=t.response.data)?void 0:s.message)||"请求参数错误、验证失败或业务逻辑错误";break;case 401:e="Token无效或已过期，请重新登录";break;case 403:e=(null==(l=t.response.data)?void 0:l.message)||"权限不足，只有超级管理员可以重置用户密码";break;case 404:e="目标用户不存在";break;case 500:e=(null==(r=t.response.data)?void 0:r.message)||"重置密码失败，服务器内部错误"}else t.message&&(e=t.message);g.error(e)}finally{ka.value=!1}}}))))):g.error("网络连接已断开，无法重置密码")}));return f((()=>{ba(),window.addEventListener("resize",ba)})),w((()=>{window.removeEventListener("resize",ba),La&&(clearTimeout(La),La=null)})),(e,o)=>{const n=U,d=P,m=z,p=N,v=$,f=z,g=u,w=Y,pe=H,ve=se,fe=re,ge=ie,we=te,ye=le,be=y("ripple"),he=W;return h(),b("div",_e,[ya.value?k("",!0):(h(),b("div",xe,[C(p,{gutter:12},{default:j((()=>[C(d,{xs:24,sm:14,md:16,lg:18,xl:20},{default:j((()=>[C(n,{modelValue:I(Ra).keyword,"onUpdate:modelValue":o[0]||(o[0]=e=>I(Ra).keyword=e),placeholder:"用户名、昵称",onKeyup:V(Da,["enter"]),onClear:Ea,onInput:Fa,clearable:""},null,8,["modelValue"])])),_:1}),C(d,{xs:24,sm:10,md:8,lg:6,xl:4,class:"el-col2"},{default:j((()=>[x((h(),_(m,{onClick:Da},{default:j((()=>o[27]||(o[27]=[O("搜索")]))),_:1,__:[27]})),[[be]]),r.value?x((h(),_(m,{key:0,onClick:o[1]||(o[1]=e=>Ka("add"))},{default:j((()=>o[28]||(o[28]=[O("新增用户")]))),_:1,__:[28]})),[[be]]):k("",!0)])),_:1})])),_:1})])),ya.value?(h(),b("div",Ce,[S("div",je,[C(n,{modelValue:I(Ra).keyword,"onUpdate:modelValue":o[2]||(o[2]=e=>I(Ra).keyword=e),placeholder:"搜索用户名、昵称",onKeyup:V(Da,["enter"]),onClear:Ea,onInput:Fa,clearable:"",class:"search-input"},{prefix:j((()=>[C(I(T),null,{default:j((()=>[C(I(B))])),_:1})])),_:1},8,["modelValue"]),C(m,{type:"primary",onClick:Da,class:"search-button"},{default:j((()=>[C(I(T),null,{default:j((()=>[C(I(B))])),_:1})])),_:1})])])):k("",!0),I(za).isOnline?k("",!0):(h(),_(I(R),{key:2,type:"error",title:Oa.value,"show-icon":"",class:"network-alert",closable:!1},{default:j((()=>[S("p",Pe,A(Na.value),1),C(m,{size:"small",onClick:Sa,type:"primary"},{default:j((()=>o[29]||(o[29]=[O("手动重试")]))),_:1,__:[29]})])),_:1},8,["title"])),ya.value?k("",!0):x((h(),_(g,{key:3,data:I($a),index:"","index-fixed":!1,currentPage:I(Wa).currentPage,pageSize:I(Wa).pageSize,total:I(Wa).total,onSizeChange:ss,onCurrentChange:ls},{default:j((()=>[C(v,{prop:"avatar",label:"用户信息","min-width":I(ga)<500?180:250},{default:j((e=>[S("div",Ue,[S("img",{class:"avatar",src:e.row.avatar||"https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg",style:q(I(ga)<500?"width: 35px; height: 35px;":""),onerror:"this.src='https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg';"},null,12,Ve),S("div",{style:q(I(ga)<500?"margin-left: 6px;":"")},[S("p",Ie,A(e.row.username),1),S("p",ze,A(e.row.nickname||"-"),1)],4)])])),_:1},8,["min-width"]),C(v,{prop:"role",label:"角色",width:"150"},{default:j((e=>[e.row.roles&&e.row.roles.length?(h(),_(I(D),{key:0,type:"superadmin"===e.row.roles[0]?"danger":"admin"===e.row.roles[0]?"warning":"info"},{default:j((()=>[O(A(Ba(e.row.roles[0])),1)])),_:2},1032,["type"])):(h(),b("span",Oe,"-"))])),_:1}),C(v,{prop:"status",label:"状态",width:"100"},{default:j((e=>[C(I(D),{type:"active"===(e.row.status||"active")?"success":"danger",effect:"dark"},{default:j((()=>[O(A("active"===(e.row.status||"active")?"正常":"已禁用"),1)])),_:2},1032,["type"])])),_:1}),C(v,{prop:"createdAt",label:"创建时间",sortable:"","min-width":"140","class-name":"created-time-column"},{default:j((e=>[S("div",Ne,A(rs(e.row.createdAt)),1)])),_:1}),C(v,{fixed:"right",label:"操作",width:I(ga)<768?140:150},{default:j((e=>[S("div",Se,[C(I(E),{"model-value":"active"===e.row.status,"onUpdate:modelValue":()=>is(e.row.userId,e.row.status||"active"),"active-color":"#13ce66","inactive-color":"#ff4949",class:"status-switch",disabled:!s.value||e.row.userId===I(a).getUserInfo.id||e.row.roles.includes("superadmin")||e.row.roles.includes("admin")&&!l.value},null,8,["model-value","onUpdate:modelValue","disabled"]),C(I(L),{trigger:"click"},{dropdown:j((()=>[C(I(F),null,{default:j((()=>[C(I(K),{onClick:a=>Ka("edit",e.row),disabled:!s.value||e.row.roles.includes("superadmin")},{default:j((()=>o[31]||(o[31]=[O(" 编辑 ")]))),_:2,__:[31]},1032,["onClick","disabled"]),l.value&&e.row.userId!==I(a).getUserInfo.id&&!e.row.roles.includes("superadmin")?(h(),_(I(K),{key:0,onClick:a=>os(e.row)},{default:j((()=>o[32]||(o[32]=[O(" 修改密码 ")]))),_:2,__:[32]},1032,["onClick"])):k("",!0),C(I(K),{onClick:a=>Ma(e.row.userId),disabled:!s.value||e.row.userId===I(a).getUserInfo.id||e.row.roles.includes("admin")&&!l.value||e.row.roles.includes("superadmin")},{default:j((()=>o[33]||(o[33]=[O(" 删除 ")]))),_:2,__:[33]},1032,["onClick","disabled"])])),_:2},1024)])),default:j((()=>[C(f,{link:"",class:"more-button"},{default:j((()=>o[30]||(o[30]=[O("更多")]))),_:1,__:[30]})])),_:2},1024)])])),_:1},8,["width"])])),_:1},8,["data","currentPage","pageSize","total"])),[[he,I(ke)]]),ya.value?(h(),b("div",Te,[I(ke)?(h(),b("div",Be,[(h(),b(M,null,X(3,(e=>S("div",{key:e,class:"mobile-skeleton-card"},[C(pe,{animated:""},{template:j((()=>[S("div",Ae,[C(w,{variant:"circle",style:{width:"40px",height:"40px"}}),S("div",Re,[C(w,{variant:"text",style:{width:"60%",height:"16px"}}),C(w,{variant:"text",style:{width:"40%",height:"14px"}})])]),C(w,{variant:"rect",style:{width:"100%",height:"60px","margin-top":"12px"}})])),_:1})]))),64))])):k("",!0),I(ke)?k("",!0):(h(),_(J,{key:1,name:"card-list",tag:"div",class:"card-container"},{default:j((()=>[(h(!0),b(M,null,X(I($a),((e,r)=>(h(),b("div",{key:e.userId,class:"user-card"},[S("div",We,[S("div",$e,[S("div",qe,[S("img",{src:e.avatar||"https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg",alt:e.username,onError:o[3]||(o[3]=e=>e.target.src="https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg")},null,40,De)]),S("div",Ee,[S("div",Le,A(e.username),1),S("div",Fe,A(e.nickname||"-"),1)])]),S("div",Ke,[C(I(D),{type:"active"===(e.status||"active")?"success":"danger",size:"small"},{default:j((()=>[O(A("active"===(e.status||"active")?"正常":"已禁用"),1)])),_:2},1032,["type"])])]),S("div",Me,[S("div",Xe,[S("div",Ye,[o[34]||(o[34]=S("span",{class:"info-label"},"角色",-1)),S("span",He,[e.roles&&e.roles.length?(h(),_(I(D),{key:0,type:"superadmin"===e.roles[0]?"danger":"admin"===e.roles[0]?"warning":"info",size:"small"},{default:j((()=>[O(A(Ba(e.roles[0])),1)])),_:2},1032,["type"])):(h(),b("span",Ze,"-"))])]),S("div",Ge,[o[35]||(o[35]=S("span",{class:"info-label"},"创建时间",-1)),S("span",Je,A(rs(e.createdAt)),1)])])]),S("div",Qe,[S("div",ea,[S("div",aa,[o[36]||(o[36]=S("span",{class:"switch-label"},"状态",-1)),C(I(E),{"model-value":"active"===e.status,"onUpdate:modelValue":()=>is(e.userId,e.status||"active"),"active-color":"#13ce66","inactive-color":"#ff4949",disabled:!s.value||e.userId===I(a).getUserInfo.id||e.roles.includes("superadmin")||e.roles.includes("admin")&&!l.value},null,8,["model-value","onUpdate:modelValue","disabled"])]),S("div",sa,[C(f,{size:"small",type:"primary",plain:"",onClick:a=>Ka("edit",e),disabled:!s.value||e.roles.includes("superadmin")},{default:j((()=>[C(I(T),null,{default:j((()=>[C(I(Z))])),_:1}),o[37]||(o[37]=S("span",null,"编辑",-1))])),_:2,__:[37]},1032,["onClick","disabled"]),C(f,{size:"small",type:"danger",plain:"",onClick:a=>Ma(e.userId),disabled:!s.value||e.userId===I(a).getUserInfo.id||e.roles.includes("admin")&&!l.value||e.roles.includes("superadmin")},{default:j((()=>[C(I(T),null,{default:j((()=>[C(I(G))])),_:1}),o[38]||(o[38]=S("span",null,"删除",-1))])),_:2,__:[38]},1032,["onClick","disabled"])])])])])))),128))])),_:1})),I(ke)||0!==I($a).length?k("",!0):(h(),b("div",la,[o[40]||(o[40]=S("div",{class:"empty-icon"},"👤",-1)),o[41]||(o[41]=S("div",{class:"empty-text"},"暂无用户数据",-1)),r.value?(h(),_(f,{key:0,type:"primary",onClick:o[4]||(o[4]=e=>Ka("add")),class:"empty-action"},{default:j((()=>[C(I(T),null,{default:j((()=>[C(I(Q))])),_:1}),o[39]||(o[39]=S("span",null,"新增用户",-1))])),_:1,__:[39]})):k("",!0)]))])):k("",!0),ya.value?(h(),b("div",ra,[S("div",ta,[r.value?(h(),b("div",{key:0,class:"action-item primary-action",onClick:o[5]||(o[5]=e=>Ka("add"))},[S("div",ia,[C(I(T),{size:24},{default:j((()=>[C(I(Q))])),_:1})]),o[42]||(o[42]=S("span",{class:"action-label"},"新增",-1))])):k("",!0),S("div",{class:"action-item",onClick:Ha},[S("div",oa,[C(I(T),{size:20},{default:j((()=>[C(I(ee))])),_:1})]),o[43]||(o[43]=S("span",{class:"action-label"},"刷新",-1))])])])):k("",!0),x(S("div",na,[C(ve,{"current-page":I(Wa).currentPage,"onUpdate:currentPage":o[6]||(o[6]=e=>I(Wa).currentPage=e),"page-size":I(Wa).pageSize,"onUpdate:pageSize":o[7]||(o[7]=e=>I(Wa).pageSize=e),"page-sizes":[10,20,30,50],total:I(Wa).total,layout:I(ga)<768?"prev, pager, next, sizes":"total, sizes, prev, pager, next, jumper",onSizeChange:ss,onCurrentChange:ls,small:I(ga)<768,"pager-count":I(ga)<768?5:7,background:""},null,8,["current-page","page-size","total","layout","small","pager-count"])],512),[[ae,!ya.value||!I(qa)]]),C(I(ue),{modelValue:I(c),"onUpdate:modelValue":o[19]||(o[19]=e=>de(c)?c.value=e:null),title:"add"===I(i)?"添加用户":"编辑用户",width:"30%","align-center":""},{footer:j((()=>[S("div",da,[C(m,{onClick:o[18]||(o[18]=e=>c.value=!1)},{default:j((()=>o[44]||(o[44]=[O("取消")]))),_:1,__:[44]}),C(m,{type:"primary",onClick:as},{default:j((()=>o[45]||(o[45]=[O("提交")]))),_:1,__:[45]})])])),default:j((()=>[C(ye,{ref_key:"formRef",ref:Xa,model:I(Ya),rules:I(Ga),"label-width":"80px"},{default:j((()=>[C(fe,{label:"用户名",prop:"username"},{default:j((()=>[C(n,{modelValue:I(Ya).username,"onUpdate:modelValue":o[8]||(o[8]=e=>I(Ya).username=e),onBlur:o[9]||(o[9]=e=>es("username"))},null,8,["modelValue"])])),_:1}),C(fe,{label:"昵称",prop:"nickname"},{default:j((()=>[C(n,{modelValue:I(Ya).nickname,"onUpdate:modelValue":o[10]||(o[10]=e=>I(Ya).nickname=e),onBlur:o[11]||(o[11]=e=>es("nickname"))},null,8,["modelValue"])])),_:1}),"add"===I(i)?(h(),_(fe,{key:0,label:"密码",prop:"password"},{default:j((()=>[C(n,{modelValue:I(Ya).password,"onUpdate:modelValue":o[12]||(o[12]=e=>I(Ya).password=e),type:"password","show-password":"",onBlur:o[13]||(o[13]=e=>es("password"))},null,8,["modelValue"])])),_:1})):k("",!0),C(fe,{label:"角色",prop:"role"},{default:j((()=>[C(we,{modelValue:I(Ya).role,"onUpdate:modelValue":o[14]||(o[14]=e=>I(Ya).role=e),placeholder:"请选择角色",onChange:o[15]||(o[15]=e=>es("role"))},{default:j((()=>[(h(!0),b(M,null,X(t.value,(e=>(h(),_(ge,{key:e.name,value:e.name,label:Ba(e.name)},null,8,["value","label"])))),128))])),_:1},8,["modelValue"])])),_:1}),C(fe,{label:"头像",prop:"avatar"},{default:j((()=>[C(n,{modelValue:I(Ya).avatar,"onUpdate:modelValue":o[16]||(o[16]=e=>I(Ya).avatar=e),placeholder:"头像URL地址",clearable:"",onBlur:o[17]||(o[17]=e=>es("avatar"))},{append:j((()=>[C(I(oe),{content:"点击预览头像",placement:"top"},{default:j((()=>[C(m,{onClick:ts},{default:j((()=>[C(I(T),null,{default:j((()=>[C(I(ne))])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),C(I(ue),{modelValue:I(ha),"onUpdate:modelValue":o[26]||(o[26]=e=>de(ha)?ha.value=e:null),title:"重置用户密码",width:"500px","align-center":""},{footer:j((()=>[S("div",fa,[C(m,{onClick:ps},{default:j((()=>o[47]||(o[47]=[O("取消")]))),_:1,__:[47]}),0===I(_a)?(h(),_(m,{key:0,type:"primary",onClick:vs,loading:I(ka)},{default:j((()=>o[48]||(o[48]=[O(" 验证并生成安全码 ")]))),_:1,__:[48]},8,["loading"])):k("",!0),1===I(_a)?(h(),_(m,{key:1,type:"primary",onClick:us},{default:j((()=>o[49]||(o[49]=[O("下一步")]))),_:1,__:[49]})):k("",!0),2===I(_a)?(h(),_(m,{key:2,onClick:cs},{default:j((()=>o[50]||(o[50]=[O("上一步")]))),_:1,__:[50]})):k("",!0),2===I(_a)?(h(),_(m,{key:3,type:"primary",onClick:gs,loading:I(ka)},{default:j((()=>o[51]||(o[51]=[O(" 重置密码 ")]))),_:1,__:[51]},8,["loading"])):k("",!0)])])),default:j((()=>[x((h(),b("div",{"element-loading-text":I(xa)},[C(I(ce),{active:I(_a),"finish-status":"success",style:{"margin-bottom":"20px"}},{default:j((()=>[C(I(me),{title:"验证管理员密码"}),C(I(me),{title:"生成安全码"}),C(I(me),{title:"重置密码"})])),_:1},8,["active"]),0===I(_a)?(h(),b("div",ca,[C(I(R),{title:"安全验证",type:"info",description:"为了确保安全，请输入您当前的登录密码来验证身份","show-icon":"",closable:!1,style:{"margin-bottom":"20px"}}),C(ye,{ref_key:"adminPasswordFormRef",ref:ja,model:I(Pa),rules:I(Ja),"label-width":"120px"},{default:j((()=>[C(fe,{label:"目标用户"},{default:j((()=>{var e;return[C(n,{value:null==(e=I(Ua))?void 0:e.username,disabled:""},null,8,["value"])]})),_:1}),C(fe,{label:"管理员密码",prop:"adminPassword"},{default:j((()=>[C(n,{modelValue:I(Pa).adminPassword,"onUpdate:modelValue":o[20]||(o[20]=e=>I(Pa).adminPassword=e),type:"password",placeholder:"请输入您的当前登录密码","show-password":"",onKeyup:V(vs,["enter"])},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])):k("",!0),1===I(_a)?(h(),b("div",ma,[C(I(R),{title:"安全提醒",type:"warning",description:I(Va).warning,"show-icon":"",closable:!1,style:{"margin-bottom":"20px"}},null,8,["description"]),I(Va).securityNotice?(h(),_(I(R),{key:0,title:"验证成功",type:"success",description:I(Va).securityNotice,"show-icon":"",closable:!1,style:{"margin-bottom":"20px"}},null,8,["description"])):k("",!0),C(ye,{"label-width":"100px"},{default:j((()=>[C(fe,{label:"目标用户"},{default:j((()=>{var e;return[C(n,{value:null==(e=I(Va).targetUser)?void 0:e.username,disabled:""},null,8,["value"])]})),_:1}),C(fe,{label:"安全码"},{default:j((()=>[S("div",pa,[C(n,{value:I(Va).securityCode,disabled:"",class:"security-code-input"},null,8,["value"]),C(m,{onClick:ms,type:"primary",size:"small",style:{"margin-left":"10px"}},{default:j((()=>o[46]||(o[46]=[O(" 复制 ")]))),_:1,__:[46]})])])),_:1}),C(fe,{label:"有效期"},{default:j((()=>[C(n,{value:I(Va).validUntil,disabled:""},null,8,["value"])])),_:1})])),_:1})])):k("",!0),2===I(_a)?(h(),b("div",va,[C(I(R),{title:"安全提示",type:"warning",description:"为了增强安全性，需要再次输入您的管理员密码进行二次确认","show-icon":"",closable:!1,style:{"margin-bottom":"20px"}}),C(ye,{ref_key:"passwordFormRef",ref:Ca,model:I(Ia),rules:I(Qa),"label-width":"120px"},{default:j((()=>[C(fe,{label:"目标用户",prop:"username"},{default:j((()=>[C(n,{modelValue:I(Ia).username,"onUpdate:modelValue":o[21]||(o[21]=e=>I(Ia).username=e),disabled:""},null,8,["modelValue"])])),_:1}),C(fe,{label:"安全码",prop:"securityCode"},{default:j((()=>[C(n,{modelValue:I(Ia).securityCode,"onUpdate:modelValue":o[22]||(o[22]=e=>I(Ia).securityCode=e),placeholder:"请手动输入安全码（不支持自动填充）",disabled:!1,autocomplete:"off"},null,8,["modelValue"])])),_:1}),C(fe,{label:"新密码",prop:"newPassword"},{default:j((()=>[C(n,{modelValue:I(Ia).newPassword,"onUpdate:modelValue":o[23]||(o[23]=e=>I(Ia).newPassword=e),type:"password",placeholder:"请输入新密码","show-password":"",autocomplete:"new-password"},null,8,["modelValue"])])),_:1}),C(fe,{label:"确认密码",prop:"confirmPassword"},{default:j((()=>[C(n,{modelValue:I(Ia).confirmPassword,"onUpdate:modelValue":o[24]||(o[24]=e=>I(Ia).confirmPassword=e),type:"password",placeholder:"请再次输入新密码","show-password":"",autocomplete:"new-password"},null,8,["modelValue"])])),_:1}),C(fe,{label:"管理员密码",prop:"adminPassword"},{default:j((()=>[C(n,{modelValue:I(Ia).adminPassword,"onUpdate:modelValue":o[25]||(o[25]=e=>I(Ia).adminPassword=e),type:"password",placeholder:"请输入您的当前登录密码进行二次确认","show-password":"",autocomplete:"current-password"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])):k("",!0)],8,ua)),[[he,I(ka)]])])),_:1},8,["modelValue"])])}}},a(ga,s(wa)))),[["__scopeId","data-v-0e31499a"]]);export{ya as default};
