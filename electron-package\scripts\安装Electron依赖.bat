@echo off
chcp 65001 >nul
title 安装Electron依赖

echo ========================================
echo        安装Electron依赖
echo ========================================
echo.

echo 1. 清理npm缓存...
npm cache clean --force

echo.
echo 2. 重置npm配置...
npm config delete registry 2>nul
npm config delete electron_mirror 2>nul

echo.
echo 3. 设置镜像源...
npm config set registry https://registry.npmmirror.com/

echo.
echo 4. 尝试安装electron...
npm install electron --save-dev --no-optional

if %errorlevel% neq 0 (
    echo.
    echo electron安装失败，尝试使用官方源...
    npm config set registry https://registry.npmjs.org/
    npm install electron --save-dev --no-optional
)

echo.
echo 5. 尝试安装electron-builder...
npm install electron-builder --save-dev --no-optional

if %errorlevel% neq 0 (
    echo.
    echo electron-builder安装失败，尝试使用官方源...
    npm config set registry https://registry.npmjs.org/
    npm install electron-builder --save-dev --no-optional
)

echo.
echo ========================================
echo 安装完成！现在可以运行以下命令：
echo   npm run build          - 构建Vue项目
echo   npm run electron:build - 打包Electron应用
echo ========================================
echo.
pause
