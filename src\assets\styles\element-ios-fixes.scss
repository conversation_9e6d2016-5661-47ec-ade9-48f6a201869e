// Element Plus组件iOS兼容性修复

// iOS设备特定修复
@supports (-webkit-touch-callout: none) {
  
  // 修复Element Plus按钮在iOS上的问题
  .el-button {
    cursor: pointer !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    touch-action: manipulation !important;
    
    // 确保按钮有足够的触摸区域
    min-height: 32px;
    
    // 防止按钮文字被选中
    * {
      -webkit-user-select: none !important;
      user-select: none !important;
    }
    
    // 修复按钮点击状态
    &:active {
      transform: scale(0.98) !important;
      transition: transform 0.1s ease !important;
    }
  }
  
  // 修复Element Plus输入框
  .el-input {
    .el-input__inner {
      -webkit-appearance: none !important;
      appearance: none !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      border-radius: 4px !important; // 保持Element Plus的圆角
      
      // 防止输入时页面缩放
      font-size: 14px !important;
      
      // 修复iOS上的输入框聚焦问题
      &:focus {
        -webkit-tap-highlight-color: transparent !important;
      }
    }
    
    // 修复输入框后缀图标
    .el-input__suffix {
      -webkit-user-select: none !important;
      user-select: none !important;
      -webkit-touch-callout: none !important;
    }
  }
  
  // 修复Element Plus文本域
  .el-textarea {
    .el-textarea__inner {
      -webkit-appearance: none !important;
      appearance: none !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      border-radius: 4px !important;
      font-size: 14px !important;
      
      // 修复iOS上的滚动
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
    }
  }
  
  // 修复Element Plus选择器
  .el-select {
    .el-input__inner {
      cursor: pointer !important;
      -webkit-user-select: none !important;
      user-select: none !important;
    }
    
    // 修复下拉面板
    .el-select-dropdown {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
      transform: translateZ(0) !important;
      -webkit-transform: translateZ(0) !important;
      
      .el-select-dropdown__wrap {
        -webkit-overflow-scrolling: touch !important;
        overflow-scrolling: touch !important;
      }
      
      .el-select-dropdown__item {
        cursor: pointer !important;
        -webkit-tap-highlight-color: transparent !important;
        -webkit-touch-callout: none !important;
        touch-action: manipulation !important;
        
        &:active {
          background-color: var(--el-fill-color-light) !important;
        }
      }
    }
  }
  
  // 修复Element Plus表格
  .el-table {
    // 修复表格滚动
    .el-table__body-wrapper {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
      transform: translateZ(0) !important;
      -webkit-transform: translateZ(0) !important;
      
      // 防止滚动时出现空白
      -webkit-backface-visibility: hidden !important;
      backface-visibility: hidden !important;
    }
    
    // 修复表格头部
    .el-table__header-wrapper {
      transform: translateZ(0) !important;
      -webkit-transform: translateZ(0) !important;
    }
    
    // 修复表格单元格
    .el-table__cell {
      -webkit-user-select: none !important;
      user-select: none !important;
      -webkit-touch-callout: none !important;
    }
  }
  
  // 修复Element Plus分页
  .el-pagination {
    -webkit-user-select: none !important;
    user-select: none !important;
    
    .el-pager li,
    .btn-prev,
    .btn-next {
      cursor: pointer !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      touch-action: manipulation !important;
      
      &:active {
        transform: scale(0.95) !important;
        transition: transform 0.1s ease !important;
      }
    }
  }
  
  // 修复Element Plus对话框
  .el-dialog {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    
    .el-dialog__wrapper {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
    }
    
    .el-dialog__body {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
    }
  }
  
  // 修复Element Plus抽屉
  .el-drawer {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    
    .el-drawer__wrapper {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
    }
    
    .el-drawer__body {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
    }
  }
  
  // 修复Element Plus菜单
  .el-menu {
    .el-menu-item,
    .el-submenu__title {
      cursor: pointer !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      -webkit-user-select: none !important;
      user-select: none !important;
      touch-action: manipulation !important;
      
      &:active {
        background-color: var(--el-menu-hover-bg-color) !important;
      }
    }
  }
  
  // 修复Element Plus标签页
  .el-tabs {
    .el-tabs__nav-wrap {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
    }
    
    .el-tabs__item {
      cursor: pointer !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      -webkit-user-select: none !important;
      user-select: none !important;
      touch-action: manipulation !important;
    }
  }
  
  // 修复Element Plus滚动条
  .el-scrollbar {
    .el-scrollbar__wrap {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
      transform: translateZ(0) !important;
      -webkit-transform: translateZ(0) !important;
    }
  }
  
  // 修复Element Plus日期选择器
  .el-date-picker,
  .el-time-picker {
    -webkit-overflow-scrolling: touch !important;
    overflow-scrolling: touch !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    
    .el-picker-panel__content {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
    }
  }
  
  // 修复Element Plus消息提示
  .el-message,
  .el-notification {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }
  
  // 修复Element Plus加载
  .el-loading-mask {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }
  
  // 修复Element Plus卡片
  .el-card {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }
  
  // 修复Element Plus折叠面板
  .el-collapse {
    .el-collapse-item__header {
      cursor: pointer !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      -webkit-user-select: none !important;
      user-select: none !important;
      touch-action: manipulation !important;
    }
    
    .el-collapse-item__content {
      -webkit-overflow-scrolling: touch !important;
      overflow-scrolling: touch !important;
    }
  }
}
