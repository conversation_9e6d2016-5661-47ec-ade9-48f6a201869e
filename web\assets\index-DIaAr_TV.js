var e=Object.defineProperty,r=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,n=(r,t,o)=>t in r?e(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o;import{k as p,O as c,C as i,S as l,Q as f}from"./vendor-9ydHGNSq.js";import{_ as y}from"./_plugin-vue_export-helper-BCo6x5W8.js";const b={class:"chart-empty-state"},d=p((m=((e,r)=>{for(var t in r||(r={}))a.call(r,t)&&n(e,t,r[t]);if(o)for(var t of o(r))s.call(r,t)&&n(e,t,r[t]);return e})({},{name:"ArtChartEmpty"}),r(m,t({__name:"index",props:{iconSize:{default:50},iconColor:{default:"var(--art-text-gray-300)"}},setup(e){const r=e;return(e,t)=>(i(),c("div",b,[l("i",{class:"iconfont-sys",style:f({fontSize:r.iconSize+"px",color:r.iconColor})},"",4)]))}}))));var m;const u=y(d,[["__scopeId","data-v-dad08253"]]);export{u as _};
