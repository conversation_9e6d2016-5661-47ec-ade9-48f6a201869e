import{_ as s}from"./index.vue_vue_type_script_setup_true_lang-B8tqK3Vx.js";import{_ as t}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as e,C as i,S as r,x as a,a6 as o}from"./vendor-9ydHGNSq.js";import"./index-BOCMSBcY.js";/* empty css                   */import"./index-DIaAr_TV.js";import"./useChart-DM-2b2dH.js";import"./index-DEP0vMzR.js";const n={class:"card art-custom-card",style:{height:"11rem"}};const p=t({},[["render",function(t,p){const l=s;return i(),e("div",n,[p[0]||(p[0]=r("div",{class:"card-header"},[r("p",{class:"title",style:{"font-size":"24px"}},[o("14.5k"),r("i",{class:"iconfont-sys text-success"},"")]),r("p",{class:"subtitle"},"销售量")],-1)),a(l,{showAxisLabel:!1,showAxisLine:!1,showSplitLine:!1,data:[50,80,50,90,60,70,50],barWidth:"16px",height:"4rem"})])}]]);export{p as default};
