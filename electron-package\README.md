# 点点管理系统 - Electron桌面应用打包

## 📋 项目概述

本项目将Vue3管理系统成功打包为Electron桌面应用程序，实现了从Web应用到桌面应用的完美转换。

## 🏗️ 技术栈

- **前端框架**: Vue3 + TypeScript + Element Plus
- **构建工具**: Vite
- **桌面框架**: Electron v37.2.3
- **打包工具**: electron-builder v26.0.11
- **包管理器**: pnpm

## 📁 文件结构

```
electron-package/
├── config/                     # 配置文件
│   ├── main.js                # Electron主进程
│   ├── preload.js             # 预加载脚本
│   └── electron-builder.json  # 打包配置
├── assets/                     # 资源文件
│   └── icons/                 # 应用图标
│       └── icon.ico           # 256x256应用图标
├── scripts/                    # 脚本文件
│   └── 安装Electron依赖.bat   # 依赖安装脚本
├── docs/                       # 文档
│   ├── 开发指南.md            # 开发说明
│   ├── 打包指南.md            # 打包说明
│   └── 使用说明.md            # 用户使用说明
└── README.md                   # 项目说明
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 方法1：使用脚本（推荐）
双击运行: scripts/安装Electron依赖.bat

# 方法2：手动安装
pnpm add electron electron-builder -D
```

### 2. 开发模式

```bash
# 启动Vue开发服务器
npm run dev

# 启动Electron应用（开发模式）
npx electron config/main.js
```

### 3. 构建和打包

```bash
# 构建Vue项目
npm run build

# 打包Electron应用
npx pnpm exec electron-builder build --config config/electron-builder.json
```

## ⚙️ 配置说明

### main.js 配置
- 窗口大小：1200x800
- 隐藏默认菜单
- 支持开发/生产环境切换
- 自动加载Vue构建产物

### electron-builder.json 配置
- 产品名称：点点管理系统
- 输出格式：便携版exe
- 支持架构：x64
- 自定义图标：256x256

## 📦 打包产物

成功打包后会生成：
- `点点管理系统 0.0.0.exe` - 便携版本（推荐）
- `win-unpacked/` - 解压版本文件夹

## 🎯 应用特性

- ✅ 真正的Windows桌面应用
- ✅ 自定义256x256应用图标
- ✅ 包含完整Vue3功能
- ✅ 无需安装，即开即用
- ✅ 支持便携和解压两种模式

## 📝 注意事项

1. 确保Node.js版本 >= 16
2. 图标必须是256x256的ico格式
3. 首次打包会下载Electron二进制文件
4. 打包过程需要良好的网络环境

## 🔧 故障排除

### 依赖安装问题
- 使用提供的安装脚本
- 检查网络连接
- 尝试使用不同的npm镜像

### 打包失败
- 确保web目录存在
- 检查图标文件格式
- 清理node_modules重新安装

## 📄 许可证

MIT License

## 👥 贡献

欢迎提交Issue和Pull Request！
