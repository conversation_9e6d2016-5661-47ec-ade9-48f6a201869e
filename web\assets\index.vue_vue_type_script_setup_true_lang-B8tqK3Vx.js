var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,i=(t,a,o)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[a]=o,l=(e,t)=>{for(var a in t||(t={}))r.call(t,a)&&i(e,a,t[a]);if(o)for(var a of o(t))s.call(t,a)&&i(e,a,t[a]);return e};import{O as n}from"./index-BOCMSBcY.js";/* empty css                   */import{_ as d}from"./index-DIaAr_TV.js";import{u as c,a as p}from"./useChart-DM-2b2dH.js";import{L as y}from"./index-DEP0vMzR.js";import{k as f,c as u,a2 as h,aG as g,O as b,C as m,Q as x,B as A,R as L,u as v}from"./vendor-9ydHGNSq.js";const w=f((j=l({},{name:"ArtBarChart"}),k={__name:"index",props:{data:{default:()=>[0,0,0,0,0,0,0]},xAxisData:{default:()=>[]},barWidth:{default:"40%"},stack:{type:Boolean,default:!1},borderRadius:{default:4},height:{default:c().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>c().colors},showAxisLabel:{type:Boolean,default:!0},showAxisLine:{type:Boolean,default:!0},showSplitLine:{type:Boolean,default:!0},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"bottom"}},setup(e){const t=e,a=u((()=>Array.isArray(t.data)&&t.data.length>0&&"object"==typeof t.data[0]&&"name"in t.data[0])),o=(e,a)=>e||(void 0!==a?t.colors[a%t.colors.length]:new y(0,0,0,1,[{offset:0,color:n("--el-color-primary-light-4")},{offset:1,color:n("--el-color-primary")}])),r=e=>new y(0,0,0,1,[{offset:0,color:e},{offset:1,color:e}]),s=e=>{const a=k();return l({name:e.name,data:e.data,type:"bar",stack:e.stack,itemStyle:(o=e.color,{borderRadius:t.borderRadius,color:"string"==typeof o?r(o):o}),barWidth:e.barWidth||t.barWidth},a);var o},{chartRef:i,getAxisLineStyle:c,getAxisLabelStyle:f,getAxisTickStyle:w,getSplitLineStyle:j,getAnimationConfig:k,getTooltipStyle:O,getLegendStyle:S,getGridWithLegend:B,isEmpty:P}=p({props:t,checkEmpty:()=>{if(Array.isArray(t.data)&&"number"==typeof t.data[0]){const e=t.data;return!e.length||e.every((e=>0===e))}if(Array.isArray(t.data)&&"object"==typeof t.data[0]){const e=t.data;return!e.length||e.every((e=>{var t;return!(null==(t=e.data)?void 0:t.length)||e.data.every((e=>0===e))}))}return!0},watchSources:[()=>t.data,()=>t.xAxisData,()=>t.colors],generateOptions:()=>{const e={grid:B(t.showLegend&&a.value,t.legendPosition,{top:15,right:0,left:0}),tooltip:t.showTooltip?O():void 0,xAxis:{type:"category",data:t.xAxisData,axisTick:w(),axisLine:c(t.showAxisLine),axisLabel:f(t.showAxisLabel)},yAxis:{type:"value",axisLabel:f(t.showAxisLabel),axisLine:c(t.showAxisLine),splitLine:j(t.showSplitLine)}};if(t.showLegend&&a.value&&(e.legend=S(t.legendPosition)),a.value){const a=t.data;e.series=a.map(((e,a)=>{const r=o(t.colors[a],a);return s({name:e.name,data:e.data,color:r,barWidth:e.barWidth,stack:t.stack?e.stack||"total":void 0})}))}else{const a=t.data,r=o();e.series=[s({data:a,color:r})]}return e}});return(e,a)=>{const o=d,r=g;return h((m(),b("div",{ref_key:"chartRef",ref:i,style:x({height:t.height})},[v(P)?(m(),A(o,{key:0})):L("",!0)],4)),[[r,t.loading]])}}},t(j,a(k))));var j,k;export{w as _};
